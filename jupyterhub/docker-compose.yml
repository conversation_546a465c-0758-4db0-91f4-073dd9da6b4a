services:
  jupyterhub:
    # REMOVE this line:
    # image: jupyterhub/jupyterhub

    # ADD this line instead:
    build: . # Tells Docker to build the image from the Dockerfile in this directory

    container_name: jupyterhub
    ports:
      - "8000:8000"
    volumes:
      - ./jupyterhub_config.py:/srv/jupyterhub/jupyterhub_config.py
      - /var/run/docker.sock:/var/run/docker.sock
      - jupyterhub-data:/srv/jupyterhub
    networks:
      - jupyterhub-net

networks:
  jupyterhub-net:
    name: jupyterhub-net

volumes:
  jupyterhub-data:
    name: jupyterhub-data
