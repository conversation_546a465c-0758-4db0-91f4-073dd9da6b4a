# jupyterhub_config.py
import os

# --- Get the Ju<PERSON>terHub config object ---
c = get_config()

# ------------------------------------------------------------------------------
# 🌐 Networking: Critical for Docker setup
# ------------------------------------------------------------------------------
c.<PERSON><PERSON><PERSON>Hub.hub_ip = "0.0.0.0"
c.<PERSON><PERSON>terHub.port = 8000
c.JupyterHub.hub_connect_ip = "jupyterhub"
c.<PERSON><PERSON><PERSON>Hub.hub_port = 8081

# ------------------------------------------------------------------------------
# 👤 Authenticator (DEMO)
# ------------------------------------------------------------------------------
from jupyterhub.auth import DummyAuthenticator

c.JupyterHub.authenticator_class = DummyAuthenticator
c.DummyAuthenticator.password = "demo-pass"
c.Authenticator.admin_users = {"admin"}

# ------------------------------------------------------------------------------
# 🚀 Spawner: Using DockerSpawner to launch user containers
# ------------------------------------------------------------------------------
c.JupyterHub.spawner_class = "dockerspawner.DockerSpawner"
c.DockerSpawner.network_name = "jupyterhub-net"
c.DockerSpawner.image = os.environ.get("DOCKER_IMAGE", "jupyter/base-notebook:latest")
c.DockerSpawner.remove = True
c.DockerSpawner.debug = True
c.DockerSpawner.volumes = {"jupyter-user-{username}": "/home/<USER>"}

# ------------------------------------------------------------------------------
# 🔑 Services and API Tokens (CORRECTED)
# ------------------------------------------------------------------------------
# The 'services' list is the modern and correct way to define services
# and grant them permissions like admin rights.
c.JupyterHub.services = [
    {
        "name": "admin-service",
        "api_token": "607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6",
        "admin": True,
    }
]

# ------------------------------------------------------------------------------
# 💾 Persistence: Storing Hub data
# ------------------------------------------------------------------------------
c.JupyterHub.db_url = "sqlite:////srv/jupyterhub/jupyterhub.sqlite"
c.JupyterHub.cookie_secret_file = "/srv/jupyterhub/jupyterhub_cookie_secret"

# ------------------------------------------------------------------------------
# ⚙️ Optional: Resource Limits and Container Configuration
# ------------------------------------------------------------------------------
c.DockerSpawner.cpu_limit = 2
c.DockerSpawner.mem_limit = "2G"
c.DockerSpawner.environment = {"GRANT_SUDO": "yes"}
