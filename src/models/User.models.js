import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';
import bcrypt from 'bcryptjs';
import sessionCacheService from '../services/sessionCache.service.js';
import logger from '../config/logger.config.js';

const User = sequelize.define(
  'User',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    lms_user_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      comment: 'User ID from D2L-Brightspace LMS'
    },
    google_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      comment: 'Google OAuth user ID'
    },
    password_hash: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'For local authentication (if needed)'
    },
    profile_picture: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL to profile picture'
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended'),
      defaultValue: 'active'
    },
    preferences: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'User preferences and settings'
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Additional metadata from LMS sync'
    }
  },
  {
    tableName: 'users',
    indexes: [
      {
        fields: ['email']
      },
      {
        fields: ['lms_user_id']
      },
      {
        fields: ['google_id']
      },
      {
        fields: ['status']
      }
    ],
    hooks: {
      beforeCreate: async user => {
        if (user.password_hash) {
          const salt = await bcrypt.genSalt(10);
          user.password_hash = await bcrypt.hash(user.password_hash, salt);
        }
      },
      beforeUpdate: async user => {
        if (user.changed('password_hash') && user.password_hash) {
          const salt = await bcrypt.genSalt(10);
          user.password_hash = await bcrypt.hash(user.password_hash, salt);
        }
      },
      afterUpdate: async user => {
        // Update Redis cache when user data is modified
        try {
          const sessionData = await sessionCacheService.getUserSession(user.id);
          if (sessionData) {
            // Update user data in session cache
            sessionData.user = {
              ...sessionData.user,
              name: user.name,
              email: user.email,
              lms_user_id: user.lms_user_id,
              google_id: user.google_id,
              profile_picture: user.profile_picture,
              last_login: user.last_login,
              status: user.status,
              preferences: user.preferences || {},
              metadata: user.metadata || {},
              updatedAt: user.updatedAt
            };

            // Get current TTL and preserve it
            const ttl = await sessionCacheService.getSessionTTL(user.id);
            if (ttl > 0) {
              await sessionCacheService.setUserSession(
                user.id,
                sessionData,
                ttl
              );
              logger.debug(
                `[User Model] Updated Redis cache for user: ${user.id}`
              );
            }
          }
        } catch (error) {
          logger.error(
            `[User Model] Failed to update Redis cache for user ${user.id}:`,
            error
          );
          // Don't throw error to avoid breaking the update operation
        }
      },
      afterDestroy: async user => {
        // Remove user session from Redis when user is deleted
        try {
          await sessionCacheService.deleteAllUserSessions(user.id);
          logger.debug(
            `[User Model] Removed Redis cache for deleted user: ${user.id}`
          );
        } catch (error) {
          logger.error(
            `[User Model] Failed to remove Redis cache for deleted user ${user.id}:`,
            error
          );
        }
      }
    }
  }
);

// Instance methods
User.prototype.comparePassword = async function (password) {
  if (!this.password_hash) return false;
  return bcrypt.compare(password, this.password_hash);
};

User.prototype.updateLastLogin = async function () {
  this.last_login = new Date();
  await this.save({ fields: ['last_login'] });
};

User.prototype.toJSON = function () {
  const values = { ...this.get() };
  delete values.password_hash;
  return values;
};

export default User;
