import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Submission = sequelize.define(
  'Submission',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    project_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'projects',
        key: 'id'
      }
    },
    attempt_number: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Submission attempt number'
    },
    notebook_s3_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'S3 URL for the submitted notebook'
    },
    additional_files_s3_urls: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
      comment: 'Array of S3 URLs for additional submitted files'
    },
    status: {
      type: DataTypes.ENUM(
        'in_progress',
        'submitted',
        'grading',
        'graded',
        'returned'
      ),
      defaultValue: 'in_progress'
    },
    submitted_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    late_submission: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    days_late: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    auto_save_data: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Auto-saved progress data'
    },
    execution_results: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Results from code execution and testing'
    },
    student_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Comments from student about their submission'
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Additional submission metadata'
    }
  },
  {
    tableName: 'submissions',
    // Override global define.paranoid=true since the submissions table
    // does not have a deleted_at column. This prevents Sequelize from
    // appending "deleted_at IS NULL" and causing 42703 errors.
    paranoid: false,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'project_id', 'attempt_number']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['project_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['submitted_at']
      },
      {
        fields: ['late_submission']
      }
    ]
  }
);

export default Submission;
