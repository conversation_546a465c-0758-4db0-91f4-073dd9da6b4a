import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import { sequelize } from './config/database.config.js';
import logger from './config/logger.config.js';
import setupSwagger from './config/swagger.config.js';
import { jwtAuthMiddleware } from './middlewares/jwtAuth.middlewares.js';
import {
  errorHandler,
  NotFoundError
} from './middlewares/errorHandler.middlewares.js';
import passport from './config/passport.config.js';

// Import routes
import authRoutes from './routes/auth.routes.js';
import courseRoutes from './routes/courses.routes.js';
import gradeRoutes from './routes/grades.routes.js';
import lmsRoutes from './routes/lms.routes.js';
import ltiRoutes from './routes/lti.routes.js';
import projectRoutes from './routes/projects.routes.js';
import roleRoutes from './routes/roles.routes.js';
import s3Routes from './routes/s3.routes.js';
import sandboxRoutes from './routes/sandbox.routes.js';
import submissionRoutes from './routes/submissions.routes.js';
import userRoutes from './routes/users.routes.js';
import workspaceRoutes from './routes/workspace.routes.js';
import checkpointRoutes from './routes/checkpoints.routes.js';
import instructorDashboardRoutes from './routes/instructorDashboard.routes.js';
import announcementRoutes from './routes/announcements.routes.js';
import messageRoutes from './routes/messages.routes.js';
import enhancedProjectRoutes from './routes/enhancedProjects.routes.js';
import courseDropdownRoutes from './routes/courseDropdown.routes.js';
import permissionFeedbackRoutes from './routes/permissionFeedback.routes.js';
import studentDashboardRoutes from './routes/studentDashboard.routes.js';
import projectAssignmentRoutes from './routes/projectAssignments.routes.js';
import rubricRoutes from './routes/rubrics.routes.js';
import jupyterHubRoutes from './routes/jupyterhub.routes.js';
import jupyterRoutes from './routes/jupyter.routes.js';

import { getJWKS } from './controllers/lti.controller.js';
import adminDashboardRoutes from './routes/adminDashboard.routes.js';

// Import associations
import './models/associations.js';

const app = express();
app.use(passport.initialize());
const PORT = process.env.PORT || 5000;

// --------------------
// Process-level crash logging for debugging
// --------------------
process.on('uncaughtException', err => {
  console.error(
    {
      name: err.name,
      message: err.message,
      stack: err.stack
    },
    'Uncaught Exception — shutting down'
  );
  process.exit(1);
});

process.on('unhandledRejection', reason => {
  console.error(
    {
      reason:
        reason instanceof Error
          ? { name: reason.name, message: reason.message, stack: reason.stack }
          : reason
    },
    'Unhandled Promise Rejection — shutting down'
  );
  process.exit(1);
});

process.on('warning', warning => {
  console.warn(
    {
      name: warning.name,
      message: warning.message,
      stack: warning.stack
    },
    'Process warning'
  );
});
// --------------------

// Middleware
app.use(
  helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        frameSrc: ["'self'", 'https:'],
        frameAncestors: ["'self'", 'https:']
      }
    }
  })
);

app.use(
  cors({
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  })
);

// HTTP logging
morgan.token('id', req => req.id || '-');
app.use(
  morgan(':id :method :url :status :response-time ms', {
    stream: { write: message => console.log(message.trim()) }
  })
);

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Cookie parser for refresh tokens
app.use(cookieParser());

// Initialize Passport (for Google OAuth)
app.use(passport.initialize());

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', jwtAuthMiddleware, userRoutes);
app.use('/api/submissions', jwtAuthMiddleware, submissionRoutes);
app.use('/api/grades', jwtAuthMiddleware, gradeRoutes);
app.use('/api/roles', jwtAuthMiddleware, roleRoutes);
app.use('/api/s3', jwtAuthMiddleware, s3Routes);
app.use('/api/lms', jwtAuthMiddleware, lmsRoutes);
app.use('/api/sandbox', sandboxRoutes);
app.use('/api/workspace', workspaceRoutes);
app.use('/api/checkpoints', jwtAuthMiddleware, checkpointRoutes);
app.use('/api/instructor', jwtAuthMiddleware, instructorDashboardRoutes);
app.use('/api/admin', jwtAuthMiddleware, adminDashboardRoutes);
app.use('/api/announcements', jwtAuthMiddleware, announcementRoutes);
app.use('/api/messages', jwtAuthMiddleware, messageRoutes);
app.use('/api/projects', jwtAuthMiddleware, enhancedProjectRoutes);
app.use('/api/courses', jwtAuthMiddleware, courseDropdownRoutes);
app.use('/api/permissions', jwtAuthMiddleware, permissionFeedbackRoutes);
app.use('/api/student', jwtAuthMiddleware, studentDashboardRoutes);
app.use('/api/project-assignments', jwtAuthMiddleware, projectAssignmentRoutes);
app.use('/api/rubrics', jwtAuthMiddleware, rubricRoutes);
app.use('/api/jupyterhub', jwtAuthMiddleware, jupyterHubRoutes);
app.use('/api/jupyter', jwtAuthMiddleware, jupyterRoutes);

// adjust path if needed
// app.use('/api/sandbox', sandboxRoutes);

// LTI routes (no authentication required for initial endpoints)

// Expose JWKS endpoint at root
app.get('/.well-known/jwks.json', getJWKS);

app.use('/api/lti', ltiRoutes);

// LTI Services routes (NRPS & AGS)
// app.use('/api/lti', ltiServicesRoutes);

// Swagger
setupSwagger(app);

// Error handling
app.use((req, res, next) => {
  next(new NotFoundError());
});
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    // Only connect to database if not in test mode
    if (process.env.NODE_ENV !== 'test') {
      await sequelize.authenticate();
      logger.info('Database connection established successfully');

      // Sync database models - temporarily disabled due to sync issues

      if (process.env.NODE_ENV === 'development') {
        try {
          await sequelize.sync({});
          logger.info('Database models synchronized');
        } catch (syncError) {
          logger.error('Database sync error:', {
            message: syncError.message,
            stack: syncError.stack,
            name: syncError.name,
            code: syncError.code,
            fields: syncError.fields,
            parent: syncError.parent,
            original: syncError.original
          });
          if (syncError?.parent) {
            logger.error('Sequelize parent error:', {
              message: syncError.parent.message,
              detail: syncError.parent.detail,
              hint: syncError.parent.hint,
              code: syncError.parent.code,
              table: syncError.parent.table,
              schema: syncError.parent.schema,
              stack: syncError.parent.stack
            });
          }
        }
      }
    }

    // Start server
    const server = app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`API Documentation: http://localhost:${PORT}/api-docs`);
      logger.info(`LTI Configuration: http://localhost:${PORT}/api/lti/config`);
    });

    // Graceful shutdown
    const gracefulShutdown = signal => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      server.close(() => {
        logger.info('HTTP server closed');

        if (process.env.NODE_ENV !== 'test') {
          sequelize
            .close()
            .then(() => {
              logger.info('Database connection closed');
              process.exit(0);
            })
            .catch(err => {
              logger.error('Error closing database connection:', err);
              process.exit(1);
            });
        } else {
          process.exit(0);
        }
      });
    };

    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Only start server if not in test mode
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

export default app;
