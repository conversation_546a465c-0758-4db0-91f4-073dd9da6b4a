'use strict';

import { v4 as uuidv4 } from 'uuid';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Check if role permissions already exist
      const existingRolePermissions = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count FROM role_permissions',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (existingRolePermissions[0].count > 0) {
        console.log(
          'Role permissions already exist, skipping role permissions seeding...'
        );
      } else {
        // Get all roles and permissions
        const [roles] = await queryInterface.sequelize.query(
          'SELECT id, name FROM roles ORDER BY name',
          { transaction }
        );

        const [permissions] = await queryInterface.sequelize.query(
          'SELECT id, key, category FROM permissions ORDER BY category, key',
          { transaction }
        );

        // Create role-permission mappings
        const rolePermissions = [];

        // Super Admin - gets all permissions
        const superAdminRole = roles.find(r => r.name === 'super_admin');
        if (superAdminRole) {
          permissions.forEach(permission => {
            rolePermissions.push({
              id: uuidv4(),
              role_id: superAdminRole.id,
              permission_id: permission.id,
              created_at: new Date(),
              updated_at: new Date()
            });
          });
        }

        // Admin - gets most permissions except system admin
        const adminRole = roles.find(r => r.name === 'admin');
        if (adminRole) {
          const adminPermissions = permissions.filter(
            p =>
              p.key !== 'system_admin' &&
              p.key !== 'delete_users' &&
              p.key !== 'manage_permissions' &&
              p.key !== 'manage_roles'
          );
          adminPermissions.forEach(permission => {
            rolePermissions.push({
              id: uuidv4(),
              role_id: adminRole.id,
              permission_id: permission.id,
              created_at: new Date(),
              updated_at: new Date()
            });
          });
        }

        // Instructor - gets teaching and course management permissions
        const instructorRole = roles.find(r => r.name === 'instructor');
        if (instructorRole) {
          const instructorPermissionKeys = [
            'view_courses',
            'manage_courses',
            'manage_enrollments',
            'view_projects',
            'create_projects',
            'edit_projects',
            'view_submissions',
            'grade_submissions',
            'manage_rubrics',
            'view_grades',
            'view_users',
            'view_analytics'
          ];
          const instructorPermissions = permissions.filter(p =>
            instructorPermissionKeys.includes(p.key)
          );
          instructorPermissions.forEach(permission => {
            rolePermissions.push({
              id: uuidv4(),
              role_id: instructorRole.id,
              permission_id: permission.id,
              created_at: new Date(),
              updated_at: new Date()
            });
          });
        }

        // Teaching Assistant - gets limited teaching permissions
        const taRole = roles.find(r => r.name === 'ta');
        if (taRole) {
          const taPermissionKeys = [
            'view_courses',
            'view_projects',
            'view_submissions',
            'grade_submissions',
            'view_grades',
            'view_users'
          ];
          const taPermissions = permissions.filter(p =>
            taPermissionKeys.includes(p.key)
          );
          taPermissions.forEach(permission => {
            rolePermissions.push({
              id: uuidv4(),
              role_id: taRole.id,
              permission_id: permission.id,
              created_at: new Date(),
              updated_at: new Date()
            });
          });
        }

        // Student - gets basic learning permissions
        const studentRole = roles.find(r => r.name === 'student');
        if (studentRole) {
          const studentPermissionKeys = [
            'view_courses',
            'view_projects',
            'submit_assignments',
            'view_own_submissions',
            'view_grades'
          ];
          const studentPermissions = permissions.filter(p =>
            studentPermissionKeys.includes(p.key)
          );
          studentPermissions.forEach(permission => {
            rolePermissions.push({
              id: uuidv4(),
              role_id: studentRole.id,
              permission_id: permission.id,
              created_at: new Date(),
              updated_at: new Date()
            });
          });
        }

        // Insert role permissions
        await queryInterface.bulkInsert('role_permissions', rolePermissions, {
          transaction
        });
        console.log(
          `✅ Created ${rolePermissions.length} role-permission mappings`
        );
      }

      // Check if sample rubrics already exist
      const existingRubrics = await queryInterface.sequelize.query(
        "SELECT COUNT(*) as count FROM rubrics WHERE title LIKE 'Sample Rubric%'",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (existingRubrics[0].count > 0) {
        console.log(
          'Sample rubrics already exist, skipping rubrics seeding...'
        );
      } else {
        // Get sample projects to create rubrics for
        const [projects] = await queryInterface.sequelize.query(
          "SELECT id, title, created_by FROM projects WHERE title LIKE 'Sample Project%' ORDER BY title",
          { transaction }
        );

        if (projects.length === 0) {
          console.log('No sample projects found, skipping rubrics creation...');
        } else {
          // Create sample rubrics
          const rubrics = [
            {
              id: uuidv4(),
              project_id: projects[0].id, // Data Exploration with Pandas
              title: 'Sample Rubric 1: Data Analysis Evaluation',
              description:
                'Comprehensive rubric for evaluating data exploration and analysis skills using Pandas.',
              criteria: JSON.stringify([
                {
                  id: 1,
                  name: 'Data Loading and Inspection',
                  description:
                    'Ability to load data and perform initial inspection',
                  points: 20,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 20,
                      description:
                        'Correctly loads data with proper error handling and comprehensive inspection'
                    },
                    {
                      name: 'Good',
                      points: 16,
                      description: 'Loads data correctly with basic inspection'
                    },
                    {
                      name: 'Satisfactory',
                      points: 12,
                      description: 'Loads data with minimal inspection'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 8,
                      description: 'Loads data but lacks proper inspection'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description:
                        'Unable to load data or no inspection performed'
                    }
                  ]
                },
                {
                  id: 2,
                  name: 'Data Cleaning',
                  description:
                    'Effectiveness of data cleaning and preprocessing techniques',
                  points: 25,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 25,
                      description:
                        'Comprehensive cleaning with proper handling of missing values, outliers, and data types'
                    },
                    {
                      name: 'Good',
                      points: 20,
                      description: 'Good cleaning practices with minor issues'
                    },
                    {
                      name: 'Satisfactory',
                      points: 15,
                      description: 'Basic cleaning performed'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 10,
                      description:
                        'Minimal cleaning with significant issues remaining'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No meaningful data cleaning performed'
                    }
                  ]
                },
                {
                  id: 3,
                  name: 'Statistical Analysis',
                  description: 'Quality and depth of statistical analysis',
                  points: 25,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 25,
                      description:
                        'Comprehensive statistical analysis with appropriate methods and interpretation'
                    },
                    {
                      name: 'Good',
                      points: 20,
                      description: 'Good statistical analysis with minor gaps'
                    },
                    {
                      name: 'Satisfactory',
                      points: 15,
                      description: 'Basic statistical analysis performed'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 10,
                      description: 'Limited statistical analysis'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No meaningful statistical analysis'
                    }
                  ]
                },
                {
                  id: 4,
                  name: 'Visualization',
                  description:
                    'Quality and appropriateness of data visualizations',
                  points: 20,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 20,
                      description:
                        'Clear, appropriate, and well-labeled visualizations that enhance understanding'
                    },
                    {
                      name: 'Good',
                      points: 16,
                      description: 'Good visualizations with minor issues'
                    },
                    {
                      name: 'Satisfactory',
                      points: 12,
                      description: 'Basic visualizations present'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 8,
                      description: 'Poor or inappropriate visualizations'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description:
                        'No visualizations or completely inappropriate'
                    }
                  ]
                },
                {
                  id: 5,
                  name: 'Code Quality and Documentation',
                  description:
                    'Code organization, comments, and documentation quality',
                  points: 10,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 10,
                      description:
                        'Well-organized, commented, and documented code'
                    },
                    {
                      name: 'Good',
                      points: 8,
                      description:
                        'Good code organization with adequate documentation'
                    },
                    {
                      name: 'Satisfactory',
                      points: 6,
                      description: 'Basic code organization'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 4,
                      description: 'Poor code organization or documentation'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No organization or documentation'
                    }
                  ]
                }
              ]),
              total_points: 100,
              grading_scale: JSON.stringify({
                'A+': { min: 97, max: 100 },
                A: { min: 93, max: 96.99 },
                'A-': { min: 90, max: 92.99 },
                'B+': { min: 87, max: 89.99 },
                B: { min: 83, max: 86.99 },
                'B-': { min: 80, max: 82.99 },
                'C+': { min: 77, max: 79.99 },
                C: { min: 73, max: 76.99 },
                'C-': { min: 70, max: 72.99 },
                D: { min: 60, max: 69.99 },
                F: { min: 0, max: 59.99 }
              }),
              is_template: false,
              template_name: null,
              created_by: projects[0].created_by,
              created_at: new Date(),
              updated_at: new Date()
            }
          ];

          // Add more rubrics if we have more projects
          if (projects.length > 1) {
            rubrics.push({
              id: uuidv4(),
              project_id: projects[1].id, // Machine Learning Classification
              title: 'Sample Rubric 2: Machine Learning Project Evaluation',
              description:
                'Rubric for evaluating machine learning classification projects including model development and evaluation.',
              criteria: JSON.stringify([
                {
                  id: 1,
                  name: 'Data Preprocessing',
                  description:
                    'Quality of data preprocessing and feature engineering',
                  points: 25,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 25,
                      description:
                        'Comprehensive preprocessing with feature engineering and proper handling of categorical variables'
                    },
                    {
                      name: 'Good',
                      points: 20,
                      description: 'Good preprocessing with minor issues'
                    },
                    {
                      name: 'Satisfactory',
                      points: 15,
                      description: 'Basic preprocessing performed'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 10,
                      description: 'Minimal preprocessing'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No meaningful preprocessing'
                    }
                  ]
                },
                {
                  id: 2,
                  name: 'Model Implementation',
                  description:
                    'Correct implementation of multiple classification algorithms',
                  points: 30,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 30,
                      description:
                        'Multiple models implemented correctly with proper parameter tuning'
                    },
                    {
                      name: 'Good',
                      points: 24,
                      description:
                        'Models implemented correctly with basic tuning'
                    },
                    {
                      name: 'Satisfactory',
                      points: 18,
                      description: 'Basic model implementation'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 12,
                      description: 'Models implemented with significant issues'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No proper model implementation'
                    }
                  ]
                },
                {
                  id: 3,
                  name: 'Model Evaluation',
                  description:
                    'Comprehensive evaluation using appropriate metrics',
                  points: 25,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 25,
                      description:
                        'Comprehensive evaluation with multiple metrics and cross-validation'
                    },
                    {
                      name: 'Good',
                      points: 20,
                      description: 'Good evaluation with appropriate metrics'
                    },
                    {
                      name: 'Satisfactory',
                      points: 15,
                      description: 'Basic evaluation performed'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 10,
                      description: 'Limited evaluation'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No meaningful evaluation'
                    }
                  ]
                },
                {
                  id: 4,
                  name: 'Model Comparison and Selection',
                  description:
                    'Systematic comparison and justified model selection',
                  points: 20,
                  levels: [
                    {
                      name: 'Excellent',
                      points: 20,
                      description:
                        'Systematic comparison with clear justification for model selection'
                    },
                    {
                      name: 'Good',
                      points: 16,
                      description: 'Good comparison with adequate justification'
                    },
                    {
                      name: 'Satisfactory',
                      points: 12,
                      description: 'Basic comparison performed'
                    },
                    {
                      name: 'Needs Improvement',
                      points: 8,
                      description: 'Poor comparison or justification'
                    },
                    {
                      name: 'Unsatisfactory',
                      points: 0,
                      description: 'No comparison or justification'
                    }
                  ]
                }
              ]),
              total_points: 100,
              grading_scale: JSON.stringify({
                A: { min: 90, max: 100 },
                B: { min: 80, max: 89.99 },
                C: { min: 70, max: 79.99 },
                D: { min: 60, max: 69.99 },
                F: { min: 0, max: 59.99 }
              }),
              is_template: false,
              template_name: null,
              created_by: projects[1].created_by,
              created_at: new Date(),
              updated_at: new Date()
            });
          }

          // Add a template rubric
          rubrics.push({
            id: uuidv4(),
            project_id: projects[0].id, // Use first project as reference
            title: 'Sample Rubric Template: General Data Science Project',
            description:
              'A reusable template rubric for general data science projects that can be customized for specific assignments.',
            criteria: JSON.stringify([
              {
                id: 1,
                name: 'Problem Understanding',
                description:
                  'Demonstrates clear understanding of the problem and objectives',
                points: 15,
                levels: [
                  {
                    name: 'Excellent',
                    points: 15,
                    description:
                      'Clear problem statement with well-defined objectives and success criteria'
                  },
                  {
                    name: 'Good',
                    points: 12,
                    description: 'Good understanding with minor gaps'
                  },
                  {
                    name: 'Satisfactory',
                    points: 9,
                    description: 'Basic understanding demonstrated'
                  },
                  {
                    name: 'Needs Improvement',
                    points: 6,
                    description: 'Limited understanding'
                  },
                  {
                    name: 'Unsatisfactory',
                    points: 0,
                    description: 'No clear understanding demonstrated'
                  }
                ]
              },
              {
                id: 2,
                name: 'Data Handling',
                description:
                  'Effective data collection, cleaning, and preprocessing',
                points: 25,
                levels: [
                  {
                    name: 'Excellent',
                    points: 25,
                    description:
                      'Comprehensive data handling with proper validation and documentation'
                  },
                  {
                    name: 'Good',
                    points: 20,
                    description: 'Good data handling practices'
                  },
                  {
                    name: 'Satisfactory',
                    points: 15,
                    description: 'Basic data handling performed'
                  },
                  {
                    name: 'Needs Improvement',
                    points: 10,
                    description: 'Poor data handling'
                  },
                  {
                    name: 'Unsatisfactory',
                    points: 0,
                    description: 'No proper data handling'
                  }
                ]
              },
              {
                id: 3,
                name: 'Analysis and Methods',
                description:
                  'Appropriate analytical methods and implementation',
                points: 30,
                levels: [
                  {
                    name: 'Excellent',
                    points: 30,
                    description:
                      'Sophisticated analysis with appropriate methods and validation'
                  },
                  {
                    name: 'Good',
                    points: 24,
                    description: 'Good analysis with appropriate methods'
                  },
                  {
                    name: 'Satisfactory',
                    points: 18,
                    description: 'Basic analysis performed'
                  },
                  {
                    name: 'Needs Improvement',
                    points: 12,
                    description: 'Limited or inappropriate analysis'
                  },
                  {
                    name: 'Unsatisfactory',
                    points: 0,
                    description: 'No meaningful analysis'
                  }
                ]
              },
              {
                id: 4,
                name: 'Results and Interpretation',
                description: 'Clear presentation and interpretation of results',
                points: 20,
                levels: [
                  {
                    name: 'Excellent',
                    points: 20,
                    description:
                      'Clear, well-interpreted results with actionable insights'
                  },
                  {
                    name: 'Good',
                    points: 16,
                    description:
                      'Good results presentation with adequate interpretation'
                  },
                  {
                    name: 'Satisfactory',
                    points: 12,
                    description: 'Basic results presentation'
                  },
                  {
                    name: 'Needs Improvement',
                    points: 8,
                    description: 'Poor presentation or interpretation'
                  },
                  {
                    name: 'Unsatisfactory',
                    points: 0,
                    description: 'No clear results or interpretation'
                  }
                ]
              },
              {
                id: 5,
                name: 'Technical Quality',
                description: 'Code quality, documentation, and reproducibility',
                points: 10,
                levels: [
                  {
                    name: 'Excellent',
                    points: 10,
                    description:
                      'High-quality, well-documented, reproducible code'
                  },
                  {
                    name: 'Good',
                    points: 8,
                    description: 'Good code quality with adequate documentation'
                  },
                  {
                    name: 'Satisfactory',
                    points: 6,
                    description: 'Basic code quality'
                  },
                  {
                    name: 'Needs Improvement',
                    points: 4,
                    description: 'Poor code quality or documentation'
                  },
                  {
                    name: 'Unsatisfactory',
                    points: 0,
                    description: 'No quality standards met'
                  }
                ]
              }
            ]),
            total_points: 100,
            grading_scale: JSON.stringify({
              A: { min: 90, max: 100 },
              B: { min: 80, max: 89.99 },
              C: { min: 70, max: 79.99 },
              D: { min: 60, max: 69.99 },
              F: { min: 0, max: 59.99 }
            }),
            is_template: true,
            template_name: 'General Data Science Project Template',
            created_by: projects[0].created_by,
            created_at: new Date(),
            updated_at: new Date()
          });

          // Insert rubrics
          await queryInterface.bulkInsert('rubrics', rubrics, { transaction });
          console.log(
            `✅ Created ${rubrics.length} sample rubrics (including ${rubrics.filter(r => r.is_template).length} template)`
          );
        }
      }

      await transaction.commit();
      console.log(
        '✅ Role permissions and rubrics seeding completed successfully!'
      );
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error seeding role permissions and rubrics:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Delete sample rubrics
      await queryInterface.bulkDelete(
        'rubrics',
        {
          title: { [Sequelize.Op.like]: 'Sample Rubric%' }
        },
        { transaction }
      );

      // Delete all role permissions (since we're managing the complete set)
      await queryInterface.bulkDelete('role_permissions', {}, { transaction });

      await transaction.commit();
      console.log('✅ Role permissions and rubrics data removed successfully!');
    } catch (error) {
      await transaction.rollback();
      console.error(
        '❌ Error removing role permissions and rubrics data:',
        error
      );
      throw error;
    }
  }
};
