'use strict';

import { v4 as uuidv4 } from 'uuid';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // First, check if we already have sample data to make this idempotent
      const existingProjects = await queryInterface.sequelize.query(
        "SELECT COUNT(*) as count FROM projects WHERE title LIKE 'Sample Project%'",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (existingProjects[0].count > 0) {
        console.log('Sample projects already exist, skipping seeding...');
        await transaction.commit();
        return;
      }

      // Create sample users (instructors, TAs, students)
      const users = [
        {
          id: uuidv4(),
          name: 'Dr. <PERSON>',
          email: '<EMAIL>',
          lms_user_id: 'instructor_001',
          status: 'active',
          preferences: JSON.stringify({ theme: 'light', notifications: true }),
          metadata: JSON.stringify({
            department: 'Computer Science',
            role: 'Professor'
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          name: 'Dr. <PERSON>',
          email: '<EMAIL>',
          lms_user_id: 'instructor_002',
          status: 'active',
          preferences: JSON.stringify({ theme: 'dark', notifications: true }),
          metadata: JSON.stringify({
            department: 'Data Science',
            role: 'Associate Professor'
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          name: 'Alice Smith',
          email: '<EMAIL>',
          lms_user_id: 'ta_001',
          status: 'active',
          preferences: JSON.stringify({ theme: 'light', notifications: true }),
          metadata: JSON.stringify({
            department: 'Computer Science',
            role: 'Teaching Assistant'
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          name: 'Bob Wilson',
          email: '<EMAIL>',
          lms_user_id: 'ta_002',
          status: 'active',
          preferences: JSON.stringify({ theme: 'light', notifications: false }),
          metadata: JSON.stringify({
            department: 'Data Science',
            role: 'Teaching Assistant'
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          name: 'Emma Davis',
          email: '<EMAIL>',
          lms_user_id: 'student_001',
          status: 'active',
          preferences: JSON.stringify({ theme: 'light', notifications: true }),
          metadata: JSON.stringify({ year: '3rd', major: 'Computer Science' }),
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      await queryInterface.bulkInsert('users', users, { transaction });

      // Create sample courses
      const courses = [
        {
          id: uuidv4(),
          lms_course_id: 'CS501_2024_FALL',
          name: 'Introduction to Data Science',
          code: 'CS501',
          description:
            'Comprehensive introduction to data science concepts, tools, and methodologies.',
          term: 'Fall 2024',
          academic_year: '2024-2025',
          instructor_id: users[0].id,
          status: 'active',
          start_date: new Date('2024-08-15'),
          end_date: new Date('2024-12-15'),
          settings: JSON.stringify({
            allow_late_submissions: true,
            max_late_days: 3,
            grade_scale: 'percentage'
          }),
          metadata: JSON.stringify({
            credits: 3,
            prerequisites: ['CS101', 'MATH201'],
            enrollment_limit: 50
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          lms_course_id: 'CS502_2024_FALL',
          name: 'Machine Learning Fundamentals',
          code: 'CS502',
          description:
            'Introduction to machine learning algorithms and their applications.',
          term: 'Fall 2024',
          academic_year: '2024-2025',
          instructor_id: users[1].id,
          status: 'active',
          start_date: new Date('2024-08-15'),
          end_date: new Date('2024-12-15'),
          settings: JSON.stringify({
            allow_late_submissions: false,
            grade_scale: 'points'
          }),
          metadata: JSON.stringify({
            credits: 4,
            prerequisites: ['CS501', 'MATH301'],
            enrollment_limit: 30
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          lms_course_id: 'CS503_2024_FALL',
          name: 'Advanced Analytics',
          code: 'CS503',
          description:
            'Advanced techniques in data analytics and visualization.',
          term: 'Fall 2024',
          academic_year: '2024-2025',
          instructor_id: users[0].id,
          status: 'active',
          start_date: new Date('2024-08-15'),
          end_date: new Date('2024-12-15'),
          settings: JSON.stringify({
            allow_late_submissions: true,
            max_late_days: 2,
            grade_scale: 'percentage'
          }),
          metadata: JSON.stringify({
            credits: 3,
            prerequisites: ['CS502'],
            enrollment_limit: 25
          }),
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      await queryInterface.bulkInsert('courses', courses, { transaction });

      // Create sample projects with comprehensive data (matching actual DB schema)
      const projects = [
        {
          id: uuidv4(),
          title: 'Sample Project 1: Data Exploration with Pandas',
          description:
            'Learn the fundamentals of data exploration using Python Pandas library. This project covers data loading, cleaning, and basic statistical analysis.',
          instructions: `# Data Exploration with Pandas

## Objectives
- Load and inspect datasets using Pandas
- Perform data cleaning operations
- Calculate basic statistics
- Create simple visualizations

## Tasks
1. Load the provided CSV dataset
2. Explore the data structure and identify missing values
3. Clean the data by handling missing values and outliers
4. Calculate descriptive statistics
5. Create basic plots to visualize the data

## Deliverables
- Jupyter notebook with complete analysis
- Summary report of findings
- Cleaned dataset

## Grading Criteria
- Code quality and documentation (30%)
- Data cleaning approach (25%)
- Statistical analysis (25%)
- Visualization quality (20%)`,
          course_id: courses[0].id,
          created_by: users[0].id,
          status: 'published',
          difficulty_level: 'beginner',
          estimated_hours: 8,
          notebook_template_s3_url:
            'https://s3.amazonaws.com/bits-ds-templates/pandas-exploration-template.ipynb',
          dataset_s3_url:
            'https://s3.amazonaws.com/bits-ds-datasets/sample-sales-data.csv',
          additional_files_s3_urls: JSON.stringify([
            'https://s3.amazonaws.com/bits-ds-resources/pandas-cheatsheet.pdf',
            'https://s3.amazonaws.com/bits-ds-resources/data-cleaning-guide.md'
          ]),
          due_date: new Date('2024-09-15'),
          late_submission_allowed: true,
          late_penalty_percent: 10.0,
          max_attempts: 2,
          auto_grading_enabled: false,
          learning_objectives: JSON.stringify([
            'Students will learn to manipulate data using Pandas, identify data quality issues, and perform basic exploratory data analysis.'
          ]),
          prerequisites: JSON.stringify([
            'Basic Python programming knowledge, familiarity with Jupyter notebooks.'
          ]),
          tags: JSON.stringify([
            'pandas',
            'data-exploration',
            'python',
            'beginner',
            'eda'
          ]),
          settings: JSON.stringify({
            allow_collaboration: false,
            submission_format: 'notebook',
            required_files: ['analysis.ipynb', 'cleaned_data.csv', 'report.md']
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          title: 'Sample Project 2: Machine Learning Classification',
          description:
            'Build and evaluate machine learning classification models using scikit-learn. This project covers data preprocessing, model training, and performance evaluation.',
          instructions: `# Machine Learning Classification Project

## Objectives
- Implement classification algorithms using scikit-learn
- Perform feature engineering and selection
- Evaluate model performance using various metrics
- Compare different classification algorithms

## Tasks
1. Load and preprocess the classification dataset
2. Perform exploratory data analysis
3. Engineer relevant features
4. Split data into training and testing sets
5. Train multiple classification models (Logistic Regression, Random Forest, SVM)
6. Evaluate and compare model performance
7. Tune hyperparameters for the best model

## Deliverables
- Jupyter notebook with complete ML pipeline
- Model comparison report
- Best model saved as pickle file

## Grading Criteria
- Data preprocessing (20%)
- Feature engineering (20%)
- Model implementation (25%)
- Performance evaluation (25%)
- Code quality and documentation (10%)`,
          course_id: courses[1].id,
          created_by: users[1].id,
          status: 'published',
          difficulty_level: 'intermediate',
          estimated_hours: 12,
          notebook_template_s3_url:
            'https://s3.amazonaws.com/bits-ds-templates/ml-classification-template.ipynb',
          dataset_s3_url:
            'https://s3.amazonaws.com/bits-ds-datasets/customer-churn.csv',
          additional_files_s3_urls: JSON.stringify([
            'https://s3.amazonaws.com/bits-ds-resources/sklearn-guide.pdf',
            'https://s3.amazonaws.com/bits-ds-resources/model-evaluation-metrics.md'
          ]),
          due_date: new Date('2024-10-01'),
          late_submission_allowed: false,
          late_penalty_percent: 0.0,
          max_attempts: 3,
          auto_grading_enabled: true,
          learning_objectives: JSON.stringify([
            'Students will learn to implement and evaluate machine learning classification models, understand feature engineering, and compare model performance.'
          ]),
          prerequisites: JSON.stringify([
            'Python programming, basic statistics, familiarity with NumPy and Pandas.'
          ]),
          tags: JSON.stringify([
            'machine-learning',
            'classification',
            'scikit-learn',
            'intermediate',
            'supervised-learning'
          ]),
          settings: JSON.stringify({
            allow_collaboration: false,
            submission_format: 'notebook',
            required_files: [
              'ml_classification.ipynb',
              'best_model.pkl',
              'evaluation_report.md'
            ],
            auto_grading_tests: [
              'test_model_accuracy',
              'test_feature_engineering',
              'test_cross_validation'
            ]
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          title: 'Sample Project 3: Time Series Analysis',
          description:
            'Analyze time series data and build forecasting models. This project covers time series decomposition, trend analysis, and predictive modeling.',
          instructions: `# Time Series Analysis and Forecasting

## Objectives
- Understand time series data characteristics
- Perform time series decomposition
- Build forecasting models
- Evaluate forecast accuracy

## Tasks
1. Load and visualize time series data
2. Perform time series decomposition (trend, seasonality, residuals)
3. Check for stationarity and apply transformations if needed
4. Build ARIMA and exponential smoothing models
5. Generate forecasts and confidence intervals
6. Evaluate forecast accuracy using appropriate metrics

## Deliverables
- Jupyter notebook with complete time series analysis
- Forecast visualization plots
- Model comparison and recommendations

## Grading Criteria
- Time series exploration (25%)
- Decomposition and stationarity analysis (25%)
- Model building (30%)
- Forecast evaluation (20%)`,
          course_id: courses[2].id,
          created_by: users[0].id,
          status: 'published',
          difficulty_level: 'advanced',
          estimated_hours: 15,
          notebook_template_s3_url:
            'https://s3.amazonaws.com/bits-ds-templates/time-series-template.ipynb',
          dataset_s3_url:
            'https://s3.amazonaws.com/bits-ds-datasets/stock-prices-daily.csv',
          additional_files_s3_urls: JSON.stringify([
            'https://s3.amazonaws.com/bits-ds-resources/time-series-guide.pdf',
            'https://s3.amazonaws.com/bits-ds-resources/forecasting-methods.md'
          ]),
          due_date: new Date('2024-10-20'),
          late_submission_allowed: true,
          late_penalty_percent: 15.0,
          max_attempts: 2,
          auto_grading_enabled: false,
          learning_objectives: JSON.stringify([
            'Students will learn to analyze time series data, understand temporal patterns, and build forecasting models for business applications.'
          ]),
          prerequisites: JSON.stringify([
            'Statistics, Python programming, experience with Pandas and Matplotlib.'
          ]),
          tags: JSON.stringify([
            'time-series',
            'forecasting',
            'arima',
            'advanced',
            'statistics'
          ]),
          settings: JSON.stringify({
            allow_collaboration: true,
            max_team_size: 2,
            submission_format: 'notebook',
            required_files: [
              'time_series_analysis.ipynb',
              'forecasts.csv',
              'team_report.pdf'
            ]
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          title: 'Sample Project 4: Deep Learning with Neural Networks',
          description:
            'Introduction to deep learning using TensorFlow/Keras. Build neural networks for image classification and natural language processing.',
          instructions: `# Deep Learning Project

## Objectives
- Understand neural network fundamentals
- Build CNN for image classification
- Implement RNN for text analysis
- Compare different architectures

## Tasks
1. Build a CNN for image classification
2. Implement data augmentation techniques
3. Create an RNN for sentiment analysis
4. Compare model architectures and performance
5. Visualize model training progress

## Deliverables
- Two Jupyter notebooks (CNN and RNN)
- Trained models saved in appropriate format
- Performance comparison report

## Grading Criteria
- CNN implementation (30%)
- RNN implementation (30%)
- Model evaluation (25%)
- Code quality and documentation (15%)`,
          course_id: courses[1].id,
          created_by: users[1].id,
          status: 'draft',
          difficulty_level: 'advanced',
          estimated_hours: 20,
          notebook_template_s3_url:
            'https://s3.amazonaws.com/bits-ds-templates/deep-learning-template.ipynb',
          dataset_s3_url:
            'https://s3.amazonaws.com/bits-ds-datasets/image-classification-dataset.zip',
          additional_files_s3_urls: JSON.stringify([
            'https://s3.amazonaws.com/bits-ds-resources/tensorflow-guide.pdf',
            'https://s3.amazonaws.com/bits-ds-resources/neural-networks-primer.md'
          ]),
          due_date: new Date('2024-11-10'),
          late_submission_allowed: true,
          late_penalty_percent: 20.0,
          max_attempts: 1,
          auto_grading_enabled: false,
          learning_objectives: JSON.stringify([
            'Students will learn to build and train deep neural networks for computer vision and NLP tasks.'
          ]),
          prerequisites: JSON.stringify([
            'Machine learning fundamentals, Python programming, linear algebra.'
          ]),
          tags: JSON.stringify([
            'deep-learning',
            'neural-networks',
            'tensorflow',
            'cnn',
            'rnn',
            'advanced'
          ]),
          settings: JSON.stringify({
            allow_collaboration: false,
            submission_format: 'notebook',
            required_files: [
              'cnn_classification.ipynb',
              'rnn_sentiment.ipynb',
              'models.zip',
              'comparison_report.pdf'
            ],
            gpu_required: true
          }),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: uuidv4(),
          title: 'Sample Project 5: Data Visualization Dashboard',
          description:
            'Create an interactive data visualization dashboard using Plotly and Dash. Focus on storytelling with data and user experience.',
          instructions: `# Interactive Data Visualization Dashboard

## Objectives
- Create interactive visualizations using Plotly
- Build a web dashboard with Dash
- Implement user controls and filters
- Tell a compelling data story

## Tasks
1. Design dashboard layout and user interface
2. Create multiple chart types (bar, line, scatter, heatmap)
3. Implement interactive filters and controls
4. Add real-time data updates
5. Deploy dashboard to cloud platform

## Deliverables
- Complete dashboard application
- Source code with documentation
- User guide and demo video

## Grading Criteria
- Visualization design (30%)
- Interactivity and UX (25%)
- Code quality (20%)
- Data storytelling (25%)`,
          course_id: courses[2].id,
          created_by: users[0].id,
          status: 'published',
          difficulty_level: 'intermediate',
          estimated_hours: 16,
          notebook_template_s3_url:
            'https://s3.amazonaws.com/bits-ds-templates/dashboard-template.py',
          dataset_s3_url:
            'https://s3.amazonaws.com/bits-ds-datasets/business-metrics.csv',
          additional_files_s3_urls: JSON.stringify([
            'https://s3.amazonaws.com/bits-ds-resources/plotly-dash-guide.pdf',
            'https://s3.amazonaws.com/bits-ds-resources/dashboard-design-principles.md'
          ]),
          due_date: new Date('2024-11-05'),
          late_submission_allowed: true,
          late_penalty_percent: 12.5,
          max_attempts: 2,
          auto_grading_enabled: false,
          learning_objectives: JSON.stringify([
            'Students will learn to create interactive data visualizations and build web-based dashboards for data presentation.'
          ]),
          prerequisites: JSON.stringify([
            'Python programming, data analysis experience, basic web development concepts.'
          ]),
          tags: JSON.stringify([
            'visualization',
            'dashboard',
            'plotly',
            'dash',
            'interactive',
            'web-development'
          ]),
          settings: JSON.stringify({
            allow_collaboration: true,
            max_team_size: 3,
            submission_format: 'application',
            required_files: [
              'app.py',
              'requirements.txt',
              'README.md',
              'demo_video.mp4'
            ],
            deployment_required: true
          }),
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      await queryInterface.bulkInsert('projects', projects, { transaction });

      await transaction.commit();
      console.log('✅ Sample projects and related data seeded successfully!');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error seeding sample projects:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Delete in reverse order to maintain referential integrity
      await queryInterface.bulkDelete(
        'projects',
        {
          title: { [Sequelize.Op.like]: 'Sample Project%' }
        },
        { transaction }
      );

      await queryInterface.bulkDelete(
        'courses',
        {
          lms_course_id: {
            [Sequelize.Op.in]: [
              'CS501_2024_FALL',
              'CS502_2024_FALL',
              'CS503_2024_FALL'
            ]
          }
        },
        { transaction }
      );

      await queryInterface.bulkDelete(
        'users',
        {
          lms_user_id: {
            [Sequelize.Op.in]: [
              'instructor_001',
              'instructor_002',
              'ta_001',
              'ta_002',
              'student_001'
            ]
          }
        },
        { transaction }
      );

      await transaction.commit();
      console.log('✅ Sample projects data removed successfully!');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error removing sample projects data:', error);
      throw error;
    }
  }
};
