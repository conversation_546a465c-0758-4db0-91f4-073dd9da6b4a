'use strict';

import { v4 as uuidv4 } from 'uuid';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Check if comprehensive sample data already exists
      const existingSubmissions = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count FROM submissions',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (existingSubmissions[0].count > 0) {
        console.log(
          'Comprehensive sample data already exists, skipping seeding...'
        );
        await transaction.commit();
        return;
      }

      // Get existing data to reference
      const [students] = await queryInterface.sequelize.query(
        "SELECT id, name FROM users WHERE lms_user_id LIKE '%student%' OR lms_user_id LIKE '%_003' OR lms_user_id LIKE '%_004'",
        { transaction }
      );

      const [instructors] = await queryInterface.sequelize.query(
        "SELECT id, name FROM users WHERE lms_user_id LIKE 'instructor_%'",
        { transaction }
      );

      const [tas] = await queryInterface.sequelize.query(
        "SELECT id, name FROM users WHERE lms_user_id LIKE 'ta_%'",
        { transaction }
      );

      const [projects] = await queryInterface.sequelize.query(
        "SELECT id, title, created_by FROM projects WHERE title LIKE 'Sample Project%'",
        { transaction }
      );

      const [courses] = await queryInterface.sequelize.query(
        "SELECT id, name, code FROM courses WHERE lms_course_id LIKE 'CS%_2024_FALL'",
        { transaction }
      );

      if (
        students.length === 0 ||
        projects.length === 0 ||
        courses.length === 0
      ) {
        console.log(
          'Required base data not found. Please run previous seeders first.'
        );
        await transaction.commit();
        return;
      }

      // Create sample submissions
      const submissions = [];
      const submissionIds = [];

      // Create submissions for each student for first 3 projects
      students.slice(0, 3).forEach((student, studentIndex) => {
        projects.slice(0, 3).forEach((project, projectIndex) => {
          const submissionId = uuidv4();
          submissionIds.push(submissionId);

          const isLate = Math.random() > 0.8; // 20% chance of late submission
          const daysLate = isLate ? Math.floor(Math.random() * 5) + 1 : 0;
          const submittedDate = new Date();
          submittedDate.setDate(
            submittedDate.getDate() - Math.floor(Math.random() * 14) - daysLate
          );

          submissions.push({
            id: submissionId,
            user_id: student.id,
            project_id: project.id,
            attempt_number: 1,
            notebook_s3_url: `https://s3.amazonaws.com/bits-submissions/${student.id}/${project.id}/notebook_v1.ipynb`,
            additional_files_s3_urls: JSON.stringify([
              `https://s3.amazonaws.com/bits-submissions/${student.id}/${project.id}/data_analysis.py`,
              `https://s3.amazonaws.com/bits-submissions/${student.id}/${project.id}/results.csv`
            ]),
            status: 'submitted',
            submitted_at: submittedDate,
            late_submission: isLate,
            days_late: daysLate,
            auto_save_data: JSON.stringify({
              last_saved: submittedDate.toISOString(),
              cell_count: Math.floor(Math.random() * 20) + 10,
              execution_count: Math.floor(Math.random() * 50) + 20
            }),
            execution_results: JSON.stringify({
              total_cells: Math.floor(Math.random() * 20) + 10,
              executed_cells: Math.floor(Math.random() * 15) + 8,
              errors: Math.random() > 0.7 ? Math.floor(Math.random() * 3) : 0,
              warnings: Math.floor(Math.random() * 5)
            }),
            student_comments: `Submission for ${project.title}. ${isLate ? 'Sorry for the late submission.' : 'Completed on time.'}`,
            metadata: JSON.stringify({
              browser: 'Chrome',
              os: 'macOS',
              jupyter_version: '6.4.0',
              python_version: '3.9.7'
            }),
            created_at: new Date(submittedDate.getTime() - 86400000), // Created 1 day before submission
            updated_at: submittedDate
          });
        });
      });

      await queryInterface.bulkInsert('submissions', submissions, {
        transaction
      });
      console.log(`✅ Created ${submissions.length} sample submissions`);

      // Create sample grades for submissions
      const grades = [];
      const [rubrics] = await queryInterface.sequelize.query(
        "SELECT id, criteria, total_points FROM rubrics WHERE title LIKE 'Sample Rubric%' AND is_template = false",
        { transaction }
      );

      submissionIds.forEach((submissionId, index) => {
        const evaluator = Math.random() > 0.5 ? instructors[0] : tas[0];
        const rubric = rubrics[index % rubrics.length];

        if (rubric) {
          const criteria =
            typeof rubric.criteria === 'string'
              ? JSON.parse(rubric.criteria)
              : rubric.criteria;
          const rubricScores = {};
          let totalScore = 0;

          // Generate scores for each criterion
          criteria.forEach(criterion => {
            const scorePercentage = 0.6 + Math.random() * 0.4; // 60-100% range
            const score = Math.floor(criterion.points * scorePercentage);
            rubricScores[criterion.id] = {
              score: score,
              max_points: criterion.points,
              feedback: `Good work on ${criterion.name.toLowerCase()}. ${score >= criterion.points * 0.8 ? 'Excellent execution.' : 'Room for improvement.'}`
            };
            totalScore += score;
          });

          const percentage = (totalScore / rubric.total_points) * 100;
          let letterGrade = 'F';
          if (percentage >= 90) letterGrade = 'A';
          else if (percentage >= 80) letterGrade = 'B';
          else if (percentage >= 70) letterGrade = 'C';
          else if (percentage >= 60) letterGrade = 'D';

          const gradedDate = new Date();
          gradedDate.setDate(
            gradedDate.getDate() - Math.floor(Math.random() * 7)
          );

          grades.push({
            id: uuidv4(),
            submission_id: submissionId,
            evaluator_id: evaluator.id,
            rubric_scores: JSON.stringify(rubricScores),
            total_score: totalScore,
            max_score: rubric.total_points,
            percentage: percentage,
            letter_grade: letterGrade,
            feedback: `Overall ${letterGrade} work. ${percentage >= 80 ? 'Strong performance across most criteria.' : 'Please review the feedback for each section and improve in future submissions.'}`,
            detailed_feedback: JSON.stringify({
              strengths: ['Good code organization', 'Clear documentation'],
              improvements:
                percentage < 80
                  ? ['More thorough analysis needed', 'Better error handling']
                  : ['Minor formatting improvements'],
              suggestions: [
                'Consider using more advanced visualization techniques',
                'Add more statistical tests'
              ]
            }),
            auto_graded_components: JSON.stringify({
              code_style: Math.floor(Math.random() * 20) + 80,
              test_coverage: Math.floor(Math.random() * 30) + 70,
              documentation: Math.floor(Math.random() * 25) + 75
            }),
            grading_time_minutes: Math.floor(Math.random() * 45) + 15,
            is_final: true,
            graded_at: gradedDate,
            released_at: new Date(gradedDate.getTime() + 3600000), // Released 1 hour after grading
            created_at: gradedDate,
            updated_at: gradedDate
          });
        }
      });

      await queryInterface.bulkInsert('grades', grades, { transaction });
      console.log(`✅ Created ${grades.length} sample grades`);

      // Create sample announcements
      const announcements = [];
      const announcementTypes = [
        'general',
        'project_update',
        'deadline_reminder',
        'course_update',
        'important',
        'urgent'
      ];
      const priorities = ['low', 'normal', 'high', 'urgent'];

      courses.forEach((course, courseIndex) => {
        // Create 2-3 announcements per course
        for (let i = 0; i < 3; i++) {
          const createdDate = new Date();
          createdDate.setDate(
            createdDate.getDate() - Math.floor(Math.random() * 30)
          );

          const isPublished = Math.random() > 0.2; // 80% published
          const publishedDate = isPublished
            ? new Date(
                createdDate.getTime() + Math.floor(Math.random() * 86400000)
              )
            : null;

          announcements.push({
            id: uuidv4(),
            course_id: course.id,
            title: `${course.code} - ${['Welcome to the Course', 'Project Update', 'Deadline Reminder', 'Course Update', 'Important Notice'][i]}`,
            content: `This is an important announcement for ${course.name}. ${['Please review the syllabus and course materials.', 'Project requirements have been updated.', 'Remember that the deadline is approaching.', 'Course schedule has been updated - please check the calendar.', 'Important information regarding the course.'][i]}`,
            announcement_type: announcementTypes[i % announcementTypes.length],
            priority: priorities[Math.floor(Math.random() * priorities.length)],
            status: isPublished ? 'published' : 'draft',
            is_pinned: i === 0, // Pin the first announcement
            scheduled_for: publishedDate,
            expires_at: publishedDate
              ? new Date(publishedDate.getTime() + 30 * 86400000)
              : null, // Expires in 30 days
            target_audience: JSON.stringify(i === 0 ? ['all'] : ['students']),
            attachments: JSON.stringify(
              i === 1
                ? [
                    {
                      name: 'project_guidelines.pdf',
                      url: 'https://s3.amazonaws.com/bits-attachments/project_guidelines.pdf',
                      size: 245760
                    }
                  ]
                : []
            ),
            metadata: JSON.stringify({
              views: Math.floor(Math.random() * 100),
              reactions: Math.floor(Math.random() * 20)
            }),
            created_by: instructors[courseIndex % instructors.length].id,
            published_by: isPublished
              ? instructors[courseIndex % instructors.length].id
              : null,
            published_at: publishedDate,
            created_at: createdDate,
            updated_at: publishedDate || createdDate
          });
        }
      });

      await queryInterface.bulkInsert('announcements', announcements, {
        transaction
      });
      console.log(`✅ Created ${announcements.length} sample announcements`);

      // Create sample activities
      const activities = [];
      const activityTypes = [
        'project_created',
        'project_published',
        'checkpoint_created',
        'checkpoint_submitted',
        'student_submitted',
        'student_graded',
        'grade_assigned'
      ];

      // Create activities for all users over the past 30 days
      [...students, ...instructors, ...tas].forEach(user => {
        // Generate 5-15 activities per user
        const activityCount = Math.floor(Math.random() * 10) + 5;

        for (let i = 0; i < activityCount; i++) {
          const activityDate = new Date();
          activityDate.setDate(
            activityDate.getDate() - Math.floor(Math.random() * 30)
          );

          const activityType =
            activityTypes[Math.floor(Math.random() * activityTypes.length)];
          const randomProject =
            projects[Math.floor(Math.random() * projects.length)];
          const randomCourse =
            courses[Math.floor(Math.random() * courses.length)];

          let description = '';
          let metadata = {};

          switch (activityType) {
            case 'project_created':
              description = `User ${user.name} created project: ${randomProject.title}`;
              metadata = {
                project_type: 'data_science',
                estimated_hours: Math.floor(Math.random() * 20) + 5
              };
              break;
            case 'project_published':
              description = `User ${user.name} published project: ${randomProject.title}`;
              metadata = {
                publication_date: new Date().toISOString(),
                visibility: 'students'
              };
              break;
            case 'checkpoint_created':
              description = `User ${user.name} created checkpoint for project: ${randomProject.title}`;
              metadata = {
                checkpoint_number: Math.floor(Math.random() * 3) + 1,
                due_date: new Date(
                  Date.now() + 7 * 24 * 60 * 60 * 1000
                ).toISOString()
              };
              break;
            case 'checkpoint_submitted':
              description = `User ${user.name} submitted checkpoint for project: ${randomProject.title}`;
              metadata = {
                completion_percentage: Math.floor(Math.random() * 40) + 60,
                submission_time: new Date().toISOString()
              };
              break;
            case 'student_submitted':
              description = `User ${user.name} submitted assignment for project: ${randomProject.title}`;
              metadata = {
                file_count: Math.floor(Math.random() * 5) + 1,
                submission_size_mb: Math.floor(Math.random() * 50) + 5
              };
              break;
            case 'student_graded':
              description = `User ${user.name} was graded for project: ${randomProject.title}`;
              metadata = {
                score: Math.floor(Math.random() * 40) + 60,
                grader: 'instructor'
              };
              break;
            case 'grade_assigned':
              description = `User ${user.name} assigned grade for project: ${randomProject.title}`;
              metadata = {
                grade_value: Math.floor(Math.random() * 40) + 60,
                grading_time_minutes: Math.floor(Math.random() * 30) + 10
              };
              break;
          }

          activities.push({
            id: uuidv4(),
            user_id: user.id,
            project_id: [
              'project_created',
              'project_published',
              'checkpoint_created',
              'checkpoint_submitted',
              'student_submitted',
              'student_graded',
              'grade_assigned'
            ].includes(activityType)
              ? randomProject.id
              : null,
            course_id: randomCourse.id,
            activity_type: activityType,
            description: description,
            metadata: JSON.stringify(metadata),
            ip_address: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
            user_agent:
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            created_at: activityDate,
            updated_at: activityDate
          });
        }
      });

      await queryInterface.bulkInsert('activities', activities, {
        transaction
      });
      console.log(`✅ Created ${activities.length} sample activities`);

      // Create sample checkpoints
      const checkpoints = [];
      const checkpointGoals = [];
      // Skip checkpoint progress for now

      // Create 2-3 checkpoints per project
      projects.forEach((project, projectIndex) => {
        for (let i = 0; i < 3; i++) {
          const checkpointId = uuidv4();
          const dueDate = new Date();
          dueDate.setDate(dueDate.getDate() + (i + 1) * 7); // Due in 1, 2, 3 weeks

          const checkpoint = {
            id: checkpointId,
            project_id: project.id,
            title: `Checkpoint ${i + 1}: ${['Data Collection & Exploration', 'Analysis & Modeling', 'Results & Documentation'][i]}`,
            description: `Complete ${['initial data exploration and cleaning', 'core analysis and model development', 'final documentation and presentation'][i]} for the project.`,
            checkpoint_number: i + 1,
            due_date: dueDate,
            weight_percentage: [30, 40, 30][i], // Different weights for each checkpoint
            is_required: true,
            status: 'published',
            created_by: project.created_by,
            metadata: JSON.stringify({
              estimated_hours: [8, 12, 6][i],
              difficulty: ['beginner', 'intermediate', 'beginner'][i],
              resources: [`resource_${i + 1}.pdf`]
            }),
            created_at: new Date(),
            updated_at: new Date()
          };

          checkpoints.push(checkpoint);

          // Create goals for each checkpoint
          const goals = [
            [
              'Load and inspect dataset',
              'Identify data quality issues',
              'Perform basic EDA'
            ],
            [
              'Implement core algorithms',
              'Validate model performance',
              'Optimize parameters'
            ],
            [
              'Create final report',
              'Prepare presentation',
              'Submit deliverables'
            ]
          ][i];

          const goalTypes = [
            'file_upload',
            'code_completion',
            'analysis',
            'documentation',
            'presentation',
            'quiz',
            'discussion'
          ];

          goals.forEach((goalText, goalIndex) => {
            const goalType = goalTypes[goalIndex % goalTypes.length];
            checkpointGoals.push({
              id: uuidv4(),
              checkpoint_id: checkpointId,
              goal_type: goalType,
              title: goalText,
              description: `Detailed description for: ${goalText}`,
              required_files: JSON.stringify([
                `${goalText.toLowerCase().replace(/\s+/g, '_')}.py`
              ]),
              completion_criteria: JSON.stringify([
                'Code runs without errors',
                'Meets all requirements',
                'Includes proper documentation'
              ]),
              points: Math.floor(100 / goals.length),
              order_index: goalIndex + 1,
              is_required: true,
              estimated_time_minutes: (Math.floor(Math.random() * 4) + 1) * 60,
              metadata: JSON.stringify({
                difficulty: ['easy', 'medium', 'hard'][
                  Math.floor(Math.random() * 3)
                ],
                resources: [`guide_${goalIndex + 1}.md`]
              }),
              created_at: new Date(),
              updated_at: new Date()
            });
          });

          // Skip checkpoint progress for now - complex table structure
        }
      });

      await queryInterface.bulkInsert('checkpoints', checkpoints, {
        transaction
      });
      console.log(`✅ Created ${checkpoints.length} sample checkpoints`);

      await queryInterface.bulkInsert('checkpoint_goals', checkpointGoals, {
        transaction
      });
      console.log(
        `✅ Created ${checkpointGoals.length} sample checkpoint goals`
      );

      // Skip checkpoint progress insertion for now
      console.log('⏭️ Skipped checkpoint progress (complex table structure)');

      await transaction.commit();
      console.log(
        '✅ Comprehensive sample data seeding completed successfully!'
      );
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error seeding comprehensive sample data:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Delete in reverse order of dependencies
      await queryInterface.bulkDelete(
        'checkpoint_progress',
        {},
        { transaction }
      );
      await queryInterface.bulkDelete('checkpoint_goals', {}, { transaction });
      await queryInterface.bulkDelete(
        'checkpoints',
        {
          title: { [Sequelize.Op.like]: 'Checkpoint%' }
        },
        { transaction }
      );
      await queryInterface.bulkDelete('activities', {}, { transaction });
      await queryInterface.bulkDelete('grades', {}, { transaction });
      await queryInterface.bulkDelete('submissions', {}, { transaction });
      await queryInterface.bulkDelete(
        'announcements',
        {
          title: { [Sequelize.Op.like]: '%CS%-%' }
        },
        { transaction }
      );

      await transaction.commit();
      console.log('✅ Comprehensive sample data removed successfully!');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error removing comprehensive sample data:', error);
      throw error;
    }
  }
};
