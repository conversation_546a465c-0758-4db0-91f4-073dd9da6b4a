import redis from 'ioredis';
import logger from '../config/logger.config.js';

class TokenBlacklistService {
  constructor() {
    this.redis = new redis(process.env.REDIS_URL);
    this.blacklistPrefix = 'blacklisted_token:';
    this.blacklistExpiry = 24 * 60 * 60; // 24 hours in seconds
  }

  /**
   * Add a token to the blacklist
   * @param {string} token - JWT token to blacklist
   * @param {number} expiresIn - Token expiration time in seconds
   */
  async blacklistToken(token, expiresIn = this.blacklistExpiry) {
    try {
      const key = `${this.blacklistPrefix}${token}`;
      await this.redis.setex(key, expiresIn, '1');
      logger.info(`Token blacklisted successfully`);
    } catch (error) {
      logger.error('Error blacklisting token:', error);
      throw error;
    }
  }

  /**
   * Check if a token is blacklisted
   * @param {string} token - JWT token to check
   * @returns {boolean} - True if token is blacklisted
   */
  async isTokenBlacklisted(token) {
    try {
      const key = `${this.blacklistPrefix}${token}`;
      const result = await this.redis.get(key);
      return result !== null;
    } catch (error) {
      logger.error('Error checking token blacklist:', error);
      return false; // Fail open - allow token if Redis is down
    }
  }

  /**
   * Remove a token from the blacklist (for testing purposes)
   * @param {string} token - JWT token to remove from blacklist
   */
  async removeFromBlacklist(token) {
    try {
      const key = `${this.blacklistPrefix}${token}`;
      await this.redis.del(key);
      logger.info(`Token removed from blacklist`);
    } catch (error) {
      logger.error('Error removing token from blacklist:', error);
      throw error;
    }
  }

  /**
   * Get blacklist statistics
   * @returns {Object} - Blacklist statistics
   */
  async getBlacklistStats() {
    try {
      const keys = await this.redis.keys(`${this.blacklistPrefix}*`);
      let memoryUsage = 0;
      
      try {
        memoryUsage = await this.redis.memory('USAGE');
      } catch (memoryError) {
        logger.warn('Memory command not supported, using 0 for memory usage');
      }
      
      return {
        totalBlacklistedTokens: keys.length,
        memoryUsage: memoryUsage
      };
    } catch (error) {
      logger.error('Error getting blacklist stats:', error);
      return { totalBlacklistedTokens: 0, memoryUsage: 0 };
    }
  }

  /**
   * Clean up expired blacklist entries
   */
  async cleanupExpiredEntries() {
    try {
      const keys = await this.redis.keys(`${this.blacklistPrefix}*`);
      let cleanedCount = 0;

      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl <= 0) {
          await this.redis.del(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        logger.info(`Cleaned up ${cleanedCount} expired blacklist entries`);
      }
    } catch (error) {
      logger.error('Error cleaning up expired blacklist entries:', error);
    }
  }

  /**
   * Close Redis connection
   */
  async close() {
    try {
      await this.redis.quit();
      logger.info('Token blacklist service connection closed');
    } catch (error) {
      logger.error('Error closing token blacklist service:', error);
    }
  }
}

// Export singleton instance
export default new TokenBlacklistService();
