import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import logger from '../config/logger.config.js';
import ltiTokenCache from './ltiTokenCache.service.js';
import multiTenantCache from './multiTenantCache.service.js';
import observabilityService from './observabilityService.js';

/**
 * LTI Service Access Token Service
 * 
 * Implements client-credentials flow for LTI Advantage services (AGS/NRPS)
 * with proper Brightspace integration and token caching.
 */
class LtiTokenService {
  constructor() {
    this.clientId = process.env.LTI_CLIENT_ID;
    this.privateKeyPem = process.env.LTI_PRIVATE_KEY_PEM;
    this.privateKeyKid = process.env.LTI_PRIVATE_KEY_KID;
    this.brightspaceTokenUrl = process.env.BS_AUTH_TOKEN_URL || 'https://auth.brightspace.com/core/connect/token';
    this.brightspaceAudience = process.env.BS_AUDIENCE || 'https://api.brightspace.com/auth/token';
    
    // Validate required configuration
    this.validateConfiguration();
  }

  /**
   * Validate required environment configuration
   */
  validateConfiguration() {
    const required = [
      'LTI_CLIENT_ID',
      'LTI_PRIVATE_KEY_PEM', 
      'LTI_PRIVATE_KEY_KID'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      const errorMsg = `❌ LTI Configuration Error: Missing required environment variables: ${missing.join(', ')}`;
      logger.error(errorMsg, { missing, available: Object.keys(process.env).filter(k => k.startsWith('LTI_')) });
      throw new Error(errorMsg);
    }

    // Validate private key format
    try {
      crypto.createPrivateKey(this.privateKeyPem);
    } catch (error) {
      throw new Error(`Invalid LTI_PRIVATE_KEY_PEM format: ${error.message}`);
    }

    logger.info('[LTI Token Service] Configuration validated successfully', {
      clientId: this.clientId,
      keyId: this.privateKeyKid,
      tokenUrl: this.brightspaceTokenUrl,
      audience: this.brightspaceAudience
    });
  }

  /**
   * Build client assertion JWT for Brightspace token request
   * @param {string} deploymentId - LTI deployment ID (optional)
   * @returns {string} Signed JWT client assertion
   */
  buildClientAssertion(deploymentId = null) {
    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      iss: this.clientId,           // Issuer = client_id
      sub: this.clientId,           // Subject = client_id  
      aud: this.brightspaceTokenUrl, // Audience = Brightspace token URL
      exp: now + 300,               // Expires in 5 minutes
      iat: now,                     // Issued at
      jti: uuidv4()                 // Unique token ID
    };

    // Add deployment_id if provided (for per-tenant caching)
    if (deploymentId) {
      payload.deployment_id = deploymentId;
    }

    const options = {
      algorithm: 'RS256',
      keyid: this.privateKeyKid
    };

    logger.debug('[LTI Token Service] Building client assertion', {
      iss: payload.iss,
      aud: payload.aud,
      exp: new Date(payload.exp * 1000).toISOString(),
      jti: payload.jti,
      deploymentId: deploymentId || 'none'
    });

    return jwt.sign(payload, this.privateKeyPem, options);
  }

  /**
   * Generate cache key for tenant + scopes
   * @param {string} issuer - Platform issuer URL
   * @param {string} clientId - Client ID
   * @param {string} deploymentId - Deployment ID
   * @param {Array<string>} scopes - Token scopes
   * @returns {string} Cache key
   */
  generateCacheKey(issuer, clientId, deploymentId, scopes) {
    const scopeHash = crypto
      .createHash('sha256')
      .update(scopes.sort().join(','))
      .digest('hex')
      .substring(0, 16);
    
    return `lti:service:${issuer}:${clientId}:${deploymentId}:${scopeHash}`;
  }

  /**
   * Get LTI service access token with caching
   * @param {Object} platform - Platform configuration
   * @param {Array<string>} scopes - Required scopes (IMS URIs)
   * @param {string} deploymentId - Optional deployment ID
   * @returns {Promise<string>} Access token
   */
  async getLtiServiceAccessToken(platform, scopes, deploymentId = null) {
    try {
      const _log = (level, msg, details) => {
        const safe = (obj) => { 
          try { return JSON.stringify(obj); } 
          catch { return '"<unserializable>"'; } 
        };
        logger[level](`msg=${msg} | details=${details ? safe(details) : '{}'}`);
      };

      // Validate scopes are IMS URIs
      this.validateScopes(scopes);

      // Generate cache key
      const cacheKey = this.generateCacheKey(
        platform.platformId || platform.issuer,
        platform.clientId,
        deploymentId || 'default',
        scopes
      );

      _log('info', '[LTI Token Service] Checking cache', {
        cacheKey: cacheKey.substring(0, 50) + '...',
        scopes: scopes.join(' '),
        deploymentId: deploymentId || 'default'
      });

      // Check cache first
      const cachedToken = await ltiTokenCache.getCachedToken(cacheKey);
      if (cachedToken) {
        _log('info', '[LTI Token Service] Using cached token', {
          expiresAt: new Date(cachedToken.expires_at * 1000).toISOString(),
          cacheKey: cacheKey.substring(0, 50) + '...'
        });
        return cachedToken.access_token;
      }

      // No valid cached token, request new one
      _log('info', '[LTI Token Service] Requesting new token', {
        tokenUrl: this.brightspaceTokenUrl,
        scopes: scopes.join(' '),
        deploymentId: deploymentId || 'default'
      });

      const clientAssertion = this.buildClientAssertion(deploymentId);
      
      const tokenRequest = new URLSearchParams({
        grant_type: 'client_credentials',
        client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: clientAssertion,
        scope: scopes.join(' '),
        audience: this.brightspaceAudience
      });

      const response = await axios.post(this.brightspaceTokenUrl, tokenRequest, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        timeout: 10000
      });

      if (!response.data.access_token) {
        throw new Error('No access_token in response');
      }

      const { access_token, expires_in, token_type } = response.data;
      
      // Cache token with 60-second buffer before expiry
      const cacheExpiry = Math.max(expires_in - 60, 60); // At least 1 minute
      await ltiTokenCache.cacheToken(
        cacheKey,
        access_token,
        cacheExpiry,
        {
          token_type,
          expires_in,
          scopes,
          deploymentId: deploymentId || 'default',
          platformId: platform.platformId || platform.issuer
        }
      );

      _log('info', '[LTI Token Service] Token obtained and cached', {
        tokenType: token_type,
        expiresIn: expires_in,
        cacheExpiry,
        cacheKey: cacheKey.substring(0, 50) + '...'
      });

      return access_token;

    } catch (error) {
      const _log = (level, msg, details) => {
        const safe = (obj) => { 
          try { return JSON.stringify(obj); } 
          catch { return '"<unserializable>"'; } 
        };
        logger[level](`msg=${msg} | details=${details ? safe(details) : '{}'}`);
      };

      // Handle 401 errors by invalidating cache and retrying once
      if (error.response?.status === 401) {
        _log('warn', '[LTI Token Service] 401 error, invalidating cache and retrying', {
          status: error.response.status,
          data: error.response.data
        });

        // Invalidate cache for this key
        const cacheKey = this.generateCacheKey(
          platform.platformId || platform.issuer,
          platform.clientId,
          deploymentId || 'default',
          scopes
        );
        await ltiTokenCache.invalidateToken(cacheKey);

        // Retry once
        try {
          const clientAssertion = this.buildClientAssertion(deploymentId);
          const tokenRequest = new URLSearchParams({
            grant_type: 'client_credentials',
            client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
            client_assertion: clientAssertion,
            scope: scopes.join(' '),
            audience: this.brightspaceAudience
          });

          const retryResponse = await axios.post(this.brightspaceTokenUrl, tokenRequest, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'Accept': 'application/json'
            },
            timeout: 10000
          });

          if (retryResponse.data.access_token) {
            _log('info', '[LTI Token Service] Retry successful after cache invalidation');
            return retryResponse.data.access_token;
          }
        } catch (retryError) {
          _log('error', '[LTI Token Service] Retry failed', {
            error: retryError.message,
            status: retryError.response?.status
          });
        }
      }

      _log('error', '[LTI Token Service] Failed to get access token', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        scopes: scopes.join(' ')
      });

      throw new Error(`Failed to get LTI service access token: ${error.message}`);
    }
  }

  /**
   * Validate that scopes are proper IMS URIs
   * @param {Array<string>} scopes - Scopes to validate
   */
  validateScopes(scopes) {
    if (!Array.isArray(scopes) || scopes.length === 0) {
      throw new Error('Scopes must be a non-empty array');
    }

    const validScopes = [
      'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
      'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem.readonly',
      'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly',
      'https://purl.imsglobal.org/spec/lti-ags/scope/score',
      'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
    ];

    const invalidScopes = scopes.filter(scope => !validScopes.includes(scope));
    if (invalidScopes.length > 0) {
      throw new Error(`Invalid IMS scopes: ${invalidScopes.join(', ')}`);
    }
  }

  /**
   * Invalidate cached tokens for a specific tenant
   * @param {string} issuer - Platform issuer URL
   * @param {string} clientId - Client ID
   * @param {string} deploymentId - Deployment ID
   */
  async invalidateTenantTokens(issuer, clientId, deploymentId = null) {
    try {
      const pattern = `lti:service:${issuer}:${clientId}:${deploymentId || 'default'}:*`;
      await ltiTokenCache.invalidatePattern(pattern);
      
      logger.info('[LTI Token Service] Invalidated tenant tokens', {
        issuer,
        clientId,
        deploymentId: deploymentId || 'default'
      });
    } catch (error) {
      logger.error('[LTI Token Service] Failed to invalidate tenant tokens:', {
        error: error.message,
        issuer,
        clientId,
        deploymentId: deploymentId || 'default'
      });
    }
  }

  /**
   * Get service token for AGS operations
   * @param {Object} platform - Platform configuration
   * @param {string} deploymentId - Optional deployment ID
   * @returns {Promise<string>} Access token
   */
  async getAgsToken(platform, deploymentId = null) {
    const scopes = [
      'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
      'https://purl.imsglobal.org/spec/lti-ags/scope/score'
    ];
    
    return this.getLtiServiceAccessToken(platform, scopes, deploymentId);
  }

  /**
   * Get service token for NRPS operations
   * @param {Object} platform - Platform configuration
   * @param {string} deploymentId - Optional deployment ID
   * @returns {Promise<string>} Access token
   */
  async getNrpsToken(platform, deploymentId = null) {
    const scopes = [
      'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
    ];
    
    return this.getLtiServiceAccessToken(platform, scopes, deploymentId);
  }
}

// Export singleton instance
export default new LtiTokenService();

