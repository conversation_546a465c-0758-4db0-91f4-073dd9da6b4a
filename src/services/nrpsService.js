import axios from 'axios';
import logger from '../config/logger.config.js';
import ltiTokenService from './ltiTokenService.js';
import rateLimitRetryService from './rateLimitRetry.service.js';
import observabilityService from './observabilityService.js';

/**
 * NRPS (Names and Role Provisioning Services) Service
 * 
 * Implements LTI 1.3 NRPS specification for retrieving course rosters
 * and membership information from the LMS.
 */
class NrpsService {
  constructor() {
    this.defaultTimeout = 10000; // 10 seconds
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
  }

  /**
   * Parse NRPS claim from LTI launch id_token
   * @param {Object} launchData - LTI launch data from id_token
   * @returns {Object|null} NRPS service configuration
   */
  parseNrpsClaim(launchData) {
    try {
      const nrpsClaim = launchData['https://purl.imsglobal.org/spec/lti-nrps/claim/namesroleservice'];
      
      if (!nrpsClaim) {
        logger.warn('[NRPS] No NRPS claim found in launch data');
        return null;
      }

      const nrpsConfig = {
        contextMembershipsUrl: nrpsClaim.context_memberships_url,
        serviceVersions: nrpsClaim.service_versions || ['2.0'],
        context: {
          id: nrpsClaim.context?.id,
          title: nrpsClaim.context?.title,
          label: nrpsClaim.context?.label
        }
      };

      logger.info('[NRPS] Parsed NRPS claim', {
        contextMembershipsUrl: nrpsConfig.contextMembershipsUrl,
        serviceVersions: nrpsConfig.serviceVersions,
        contextId: nrpsConfig.context.id
      });

      return nrpsConfig;
    } catch (error) {
      logger.error('[NRPS] Failed to parse NRPS claim:', {
        error: error.message,
        launchData: Object.keys(launchData)
      });
      return null;
    }
  }

  /**
   * List members from NRPS endpoint
   * @param {string} contextId - LTI context ID
   * @param {string} nrpsUrl - NRPS membership URL
   * @param {Object} platform - Platform configuration
   * @param {Object} options - Query options
   * @param {string} options.resourceLinkId - Optional resource link ID filter
   * @param {string} options.role - Optional role filter
   * @param {number} options.limit - Optional limit
   * @param {string} options.deploymentId - Optional deployment ID
   * @returns {Promise<Object>} NRPS membership container
   */
  async listMembers(contextId, nrpsUrl, platform, options = {}) {
    const _log = (level, msg, details) => {
      const safe = (obj) => { 
        try { return JSON.stringify(obj); } 
        catch { return '"<unserializable>"'; } 
      };
      logger[level](`msg=${msg} | details=${details ? safe(details) : '{}'}`);
    };

    try {
      const { resourceLinkId, role, limit, deploymentId } = options;

      _log('info', '[NRPS] listMembers: Starting request', {
        contextId,
        nrpsUrl,
        resourceLinkId: resourceLinkId || 'none',
        role: role || 'none',
        limit: limit || 'none'
      });

      // Validate NRPS URL
      if (!nrpsUrl) {
        throw new Error('NRPS URL is required');
      }

      // Get NRPS access token
      const token = await ltiTokenService.getNrpsToken(platform, deploymentId);
      _log('info', '[NRPS] listMembers: Token obtained', {
        tokenLength: token?.length
      });

      // Build query parameters
      const params = {};
      if (resourceLinkId) {
        params.rlid = resourceLinkId;
      }
      if (role) {
        params.role = role;
      }
      if (limit) {
        params.limit = limit;
      }

      _log('info', '[NRPS] listMembers: Making request', {
        url: nrpsUrl,
        params
      });

      // Make NRPS request with proper IMS media type
      const response = await this._makeNrpsRequest(nrpsUrl, token, params);

      const membersCount = Array.isArray(response.data?.members) ? response.data.members.length : 0;
      _log('info', '[NRPS] listMembers: Request successful', {
        status: response.status,
        membersCount,
        hasNext: !!response.data?.next,
        linkHeader: response.headers?.link || null
      });

      return {
        success: true,
        contextId,
        members: response.data.members || [],
        next: response.data.next || null,
        totalMembers: membersCount,
        linkHeader: response.headers?.link || null,
        metadata: {
          serviceVersion: '2.0',
          requestedAt: new Date().toISOString(),
          resourceLinkId: resourceLinkId || null,
          role: role || null,
          limit: limit || null
        }
      };

    } catch (error) {
      _log('error', '[NRPS] listMembers: Request failed', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        contextId,
        nrpsUrl
      });

      throw new Error(`NRPS request failed: ${error.message}`);
    }
  }

  /**
   * Get members by role
   * @param {string} contextId - LTI context ID
   * @param {string} nrpsUrl - NRPS membership URL
   * @param {Object} platform - Platform configuration
   * @param {string} role - LTI role URI
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Filtered members
   */
  async getMembersByRole(contextId, nrpsUrl, platform, role, options = {}) {
    try {
      logger.info('[NRPS] getMembersByRole: Filtering by role', {
        contextId,
        role
      });

      const result = await this.listMembers(contextId, nrpsUrl, platform, {
        ...options,
        role
      });

      return {
        ...result,
        metadata: {
          ...result.metadata,
          filteredByRole: role
        }
      };
    } catch (error) {
      logger.error('[NRPS] getMembersByRole: Failed', {
        error: error.message,
        contextId,
        role
      });
      throw error;
    }
  }

  /**
   * Get instructors from course roster
   * @param {string} contextId - LTI context ID
   * @param {string} nrpsUrl - NRPS membership URL
   * @param {Object} platform - Platform configuration
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Instructor members
   */
  async getInstructors(contextId, nrpsUrl, platform, options = {}) {
    const instructorRole = 'http://purl.imsglobal.org/vocab/lis/v2/membership#Instructor';
    return this.getMembersByRole(contextId, nrpsUrl, platform, instructorRole, options);
  }

  /**
   * Get students from course roster
   * @param {string} contextId - LTI context ID
   * @param {string} nrpsUrl - NRPS membership URL
   * @param {Object} platform - Platform configuration
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Student members
   */
  async getStudents(contextId, nrpsUrl, platform, options = {}) {
    const learnerRole = 'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner';
    return this.getMembersByRole(contextId, nrpsUrl, platform, learnerRole, options);
  }

  /**
   * Get teaching assistants from course roster
   * @param {string} contextId - LTI context ID
   * @param {string} nrpsUrl - NRPS membership URL
   * @param {Object} platform - Platform configuration
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} TA members
   */
  async getTeachingAssistants(contextId, nrpsUrl, platform, options = {}) {
    const taRole = 'http://purl.imsglobal.org/vocab/lis/v2/membership#TeachingAssistant';
    return this.getMembersByRole(contextId, nrpsUrl, platform, taRole, options);
  }

  /**
   * Validate NRPS response structure
   * @param {Object} response - NRPS response data
   * @returns {boolean} Whether response is valid
   */
  validateNrpsResponse(response) {
    try {
      if (!response || typeof response !== 'object') {
        return false;
      }

      // Check for required fields
      if (!Array.isArray(response.members)) {
        return false;
      }

      // Validate member structure
      for (const member of response.members) {
        if (!member.user_id || !member.status) {
          return false;
        }
      }

      return true;
    } catch (error) {
      logger.error('[NRPS] validateNrpsResponse: Validation failed', {
        error: error.message
      });
      return false;
    }
  }

  /**
   * Make NRPS HTTP request with enhanced retry logic and observability
   * @param {string} url - NRPS endpoint URL
   * @param {string} token - Access token
   * @param {Object} params - Query parameters
   * @param {Object} context - Additional context for logging
   * @returns {Promise<Object>} HTTP response
   */
  async _makeNrpsRequest(url, token, params = {}, context = {}) {
    const startTime = Date.now();
    
    try {
      const config = {
        method: 'GET',
        url,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.ims.lti-nrps.v2.membershipcontainer+json',
          'Content-Type': 'application/json'
        },
        params,
        timeout: this.defaultTimeout
      };

      const response = await rateLimitRetryService.makeRequest(config, 'NRPS', {
        ...context,
        url,
        params
      });

      // Validate response structure
      if (!this.validateNrpsResponse(response.data)) {
        throw new Error('Invalid NRPS response structure');
      }

      const duration = Date.now() - startTime;
      observabilityService.logPerformance('nrps_request', duration, {
        url,
        status: response.status,
        memberCount: response.data?.members?.length || 0
      });

      return response;

    } catch (error) {
      const duration = Date.now() - startTime;
      observabilityService.logError(error, {
        service: 'NRPS',
        url,
        params,
        duration
      });
      throw error;
    }
  }

  /**
   * Get NRPS service status
   * @param {string} nrpsUrl - NRPS endpoint URL
   * @param {Object} platform - Platform configuration
   * @returns {Promise<Object>} Service status
   */
  async getServiceStatus(nrpsUrl, platform) {
    try {
      logger.info('[NRPS] getServiceStatus: Checking service availability', {
        nrpsUrl
      });

      const token = await ltiTokenService.getNrpsToken(platform);
      
      const response = await axios.get(nrpsUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.ims.lti-nrps.v2.membershipcontainer+json'
        },
        timeout: 5000,
        validateStatus: () => true // Accept any status for health check
      });

      return {
        available: response.status < 500,
        status: response.status,
        serviceVersion: '2.0',
        checkedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('[NRPS] getServiceStatus: Service check failed', {
        error: error.message,
        nrpsUrl
      });

      return {
        available: false,
        error: error.message,
        checkedAt: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
export default new NrpsService();

