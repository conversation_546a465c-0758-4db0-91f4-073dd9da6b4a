// services/jupyterService.js
import config from '../config/database.config.js';
import axios from 'axios';
import logger from '../config/logger.config.js';
import NodeCache from 'node-cache';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';
import jupyterExecutionService from './jupyterExecution.service.js';
import { setTimeout as sleep } from 'timers/promises';
import http from 'http';
import https from 'https';

const userToken = new NodeCache({ stdTTL: 5 * 60 });
const JUPYTERHUB_URL = `${config.jupyterhub.url}/hub/api`;

// ------------------ Axios Client ------------------
const jupyterhubApi = axios.create({
  baseURL: JUPYTERHUB_URL,
  timeout: 60000,
  headers: {
    Authorization: `token ${config.jupyterhub.apiToken}`
  },
  httpAgent: new http.Agent({ keepAlive: true }),
  httpsAgent: new https.Agent({ keepAlive: true })
});

function getUserApi(username, token) {
  return axios.create({
    baseURL: `${config.jupyterhub.url}/user/${encodeURIComponent(username)}/api`,
    params: { token },
    timeout: 60000,
    httpAgent: new http.Agent({ keepAlive: true }),
    httpsAgent: new https.Agent({ keepAlive: true })
  });
}

const delay = ms => sleep(ms);

// ------------------ Retry Helpers ------------------
const isTransientNetworkError = err => {
  const status = err?.response?.status;
  const code = err?.code;
  return (
    !status ||
    status >= 500 ||
    [
      'ECONNRESET',
      'ECONNREFUSED',
      'EPIPE',
      'ETIMEDOUT',
      'ECONNABORTED'
    ].includes(code)
  );
};

const withRetry = async (fn, options = {}) => {
  const retries = options.retries ?? 5;
  const baseDelay = options.baseDelayMs ?? 500;
  const alsoRetryStatus = options.alsoRetryStatus ?? [];
  let lastError;
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fn();
    } catch (e) {
      lastError = e;
      const status = e.response?.status;
      const shouldRetry =
        isTransientNetworkError(e) ||
        (status && alsoRetryStatus.includes(status));
      if (!shouldRetry || attempt === retries) {
        throw e;
      }
      const sleepMs = baseDelay * Math.pow(2, attempt - 1);
      logger.warn(
        `Transient error (attempt ${attempt}/${retries}): ${status || e.code || e.message} — retrying in ${sleepMs}ms`
      );
      await delay(sleepMs);
    }
  }
  throw lastError;
};

// ------------------ User Management ------------------
const getUser = async username => {
  try {
    const response = await jupyterhubApi.get(
      `/users/${encodeURIComponent(username)}`
    );
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      return null; // User not found
    }
    throw error;
  }
};

const createUser = async username => {
  try {
    const response = await jupyterhubApi.post(
      `/users/${encodeURIComponent(username)}`,
      {
        name: username
      }
    );
    logger.info(`User '${username}' created successfully.`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`Failed to create user ${username}: ${errorMessage}`);
    throw new Error('Could not create Jupyter user.');
  }
};

// ------------------ Server Management ------------------
const getServerStatus = async username => {
  const user = await getUser(username);
  return user?.servers?.[''] || null;
};

const startServer = async (username, serverName = '') => {
  try {
    const response = await jupyterhubApi.post(
      `/users/${encodeURIComponent(username)}/servers/${serverName}`,
      { name: serverName }
    );
    logger.info(`JupyterHub server start initiated for user: ${username}`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `Failed to start JupyterHub server for ${username}: ${errorMessage}`
    );
    throw new Error('Could not start Jupyter server.');
  }
};

const stopServer = async user => {
  const username = user.jupiterUserName;
  try {
    const response = await jupyterhubApi.delete(
      `/users/${encodeURIComponent(username)}/server`
    );
    logger.info(`JupyterHub server stopped for user: ${username}`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `Failed to stop JupyterHub server for ${username}: ${errorMessage}`
    );
    throw new Error('Could not stop Jupyter server.');
  }
};

const getUserToken = async username => {
  try {
    // 0) Return cached if present
    const cachedToken = userToken.get(username);
    if (cachedToken) {
      logger.info(`Using cached token for user: ${username}`);
      return cachedToken;
    }

    // 1) Ensure user exists in JupyterHub (create if needed)
    let user = await getUser(username);
    if (!user) {
      logger.warn(`JupyterHub user '${username}' not found. Creating...`);
      try {
        user = await createUser(username);
        // small delay to let JupyterHub register the user before token creation
        await delay(300);
      } catch (createErr) {
        const createMsg =
          createErr.response?.data?.message || createErr.message;
        logger.error(
          `Failed to ensure JupyterHub user '${username}': ${createMsg}`
        );
        throw new Error(
          `Could not create Jupyter user '${username}' to generate token.`
        );
      }
    }

    // 2) Create a user token via admin API token
    const response = await jupyterhubApi.post(
      `/users/${encodeURIComponent(username)}/tokens`,
      {
        note: `User token for ${username}`
      }
    );
    logger.info(
      `Token generated for user: ${username} -- ${JSON.stringify(
        response.data,
        null,
        2
      )}`
    );
    const token = response.data.token;
    userToken.set(username, token);
    return token;
  } catch (error) {
    const status = error.response?.status;
    const errorMessage = error.response?.data?.message || error.message;
    if (status === 403) {
      logger.error(
        `Insufficient permissions to generate token for '${username}': ${errorMessage}`
      );
      throw new Error(
        'JupyterHub API token lacks permission to create user tokens. Please configure an admin API token.'
      );
    }
    if (status === 404) {
      logger.error(
        `JupyterHub user '${username}' not found when generating token: ${errorMessage}`
      );
      throw new Error(
        `JupyterHub user '${username}' not found when generating token.`
      );
    }
    logger.error(`Failed to generate token for ${username}: ${errorMessage}`);
    throw new Error('Could not generate user token.');
  }
};

// ------------------ Ensure Running Server ------------------
const ensureServerIsRunning = async username => {
  const POLL_INTERVAL = 2000; // 2 sec
  const START_TIMEOUT = 120000; // 2 min
  const startTime = Date.now();

  logger.info(`Ensuring Jupyter server is running for user: ${username}`);

  try {
    // 1. Ensure user exists
    let user = await getUser(username);
    if (!user) {
      user = await createUser(username);
    }

    // 2. Poll until server is ready
    while (Date.now() - startTime < START_TIMEOUT) {
      const serverStatus = await getServerStatus(username);

      if (serverStatus?.ready) {
        const userToken = await getUserToken(username);
        logger.info(`Server for '${username}' is ready.`);
        const iframeUrl = `${config.jupyterhub.url}/user/${encodeURIComponent(
          username
        )}/lab?token=${encodeURIComponent(userToken)}`;
        serverStatus.url = iframeUrl;
        return serverStatus; // ✅ Always return final server object
      }

      if (!serverStatus) {
        logger.info(`Server not found for '${username}'. Requesting start...`);
        await startServer(username, user.id);
      } else {
        logger.info(`Server for '${username}' is pending. Waiting...`);
      }

      await delay(POLL_INTERVAL);
    }

    throw new Error(`Server for ${username} did not become ready in time.`);
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`JupyterHub API error for ${username}: ${errorMessage}`);
    throw new Error('Could not ensure Jupyter server is running.');
  }
};

// ------------------ Wait for Single-User API ------------------
const waitForUserServerReady = async (username, token, opts = {}) => {
  const interval = opts.intervalMs ?? 1500;
  const timeout = opts.timeoutMs ?? 60000;
  const start = Date.now();
  const userApi = getUserApi(username, token);

  logger.info(
    `Waiting for single-user API to be reachable for '${username}' (timeout ${timeout}ms)`
  );

  /*
    We probe a lightweight endpoint. '/sessions' is reasonable and should return 200.
    We treat network errors and 5xx as transient, 401/403 as fatal (bad token),
    and others as transient unless clearly permanent.
  */
  // eslint-disable-next-line no-constant-condition
  // eslint-disable-next-line no-constant-condition
  // eslint-disable-next-line no-constant-condition
  for (; Date.now() - start <= timeout; ) {
    try {
      const resp = await userApi.get('/sessions', { timeout: 5000 });
      if (resp.status >= 200 && resp.status < 300) {
        logger.info(`Single-user API is reachable for '${username}'.`);
        return;
      }
    } catch (e) {
      const status = e.response?.status;
      const code = e.code;
      const msg = e.response?.data?.message || e.message;

      if (status === 401 || status === 403) {
        logger.error(
          `Auth error reaching single-user API for '${username}': ${msg}`
        );
        throw new Error('Unauthorized to access Jupyter single-user API');
      }

      // Transient network issues (e.g., ECONNRESET: socket hang up)
      if (
        !status ||
        status >= 500 ||
        [
          'ECONNRESET',
          'ECONNREFUSED',
          'EPIPE',
          'ETIMEDOUT',
          'ECONNABORTED'
        ].includes(code)
      ) {
        if (Date.now() - start > timeout) {
          logger.error(
            `Timed out waiting for single-user API for '${username}': ${msg}`
          );
          throw new Error(
            'Jupyter single-user API did not become ready in time'
          );
        }
        await delay(interval);
        continue;
      }

      // Other statuses (e.g., 404 if route not up yet) -> keep waiting until timeout
      if (Date.now() - start > timeout) {
        logger.error(
          `Timed out with status ${status} waiting for single-user API for '${username}': ${msg}`
        );
        throw new Error('Jupyter single-user API did not become ready in time');
      }
      await delay(interval);
    }
  }
};

// ------------------ Wait for Kernelspecs ------------------
const waitForKernelspecsReady = async (username, token, opts = {}) => {
  const interval = opts.intervalMs ?? 1500;
  const timeout = opts.timeoutMs ?? 60000;
  const deadline = Date.now() + timeout;
  const userApi = getUserApi(username, token);

  logger.info(
    `Waiting for kernelspecs to be available for '${username}' (timeout ${timeout}ms)`
  );

  while (Date.now() <= deadline) {
    try {
      const resp = await userApi.get('/kernelspecs', { timeout: 8000 });
      if (resp.status >= 200 && resp.status < 300 && resp.data?.kernelspecs) {
        logger.info(`Kernelspecs available for '${username}'.`);
        return resp.data;
      }
    } catch (e) {
      const status = e.response?.status;
      const code = e.code;
      const msg = e.response?.data?.message || e.message;

      if (status === 401 || status === 403) {
        logger.error(
          `Auth error reaching kernelspecs for '${username}': ${msg}`
        );
        throw new Error('Unauthorized to access Jupyter kernelspecs');
      }

      if (
        !status ||
        status >= 500 ||
        [
          'ECONNRESET',
          'ECONNREFUSED',
          'EPIPE',
          'ETIMEDOUT',
          'ECONNABORTED'
        ].includes(code)
      ) {
        await delay(interval);
        continue;
      }

      await delay(interval);
    }
  }
  logger.error(`Timed out waiting for kernelspecs for '${username}'.`);
  throw new Error('Kernelspecs did not become ready in time');
};

// ------------------ Create Workspace ------------------
const createWorkspace = async (projectId, userDetails) => {
  if (!projectId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'projectId is required');
  }
  const username = userDetails.jupiterUserName;
  const token = userDetails.jupyterUserToken;

  if (!username || !token) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Missing Jupyter user context');
  }

  logger.info(
    `Creating workspace for project: ${projectId}, user: ${username}`
  );

  // 1) Ensure server is running
  const server = await ensureServerIsRunning(username);
  // 1.1) Ensure the single-user server API is reachable to avoid socket hang ups
  await waitForUserServerReady(username, token);
  // 1.2) Ensure kernelspecs are ready to reduce session POST timeouts
  await waitForKernelspecsReady(username, token);

  // User-scoped Jupyter API client (equivalent to proxy rewrite)
  const userApi = getUserApi(username, token);

  const folderPath = `${projectId}`; // relative to user root
  const notebookName = 'Untitled.ipynb';
  const notebookPath = `${folderPath}/${notebookName}`;

  // 2) Create folder /:projectId (idempotent, with retries)
  await withRetry(
    async () => {
      try {
        await userApi.put(
          `/contents/${encodeURIComponent(folderPath)}`,
          { type: 'directory' },
          { timeout: 10000 }
        );
        return true;
      } catch (e) {
        if (e.response?.status === 409) {
          // already exists; treat as success
          return true;
        }
        throw e;
      }
    },
    { alsoRetryStatus: [404] }
  );

  // 3) Create notebook at /:projectId/Untitled.ipynb (idempotent-ish)
  const projectData = await withRetry(
    () =>
      userApi.get(`/contents/${encodeURIComponent(folderPath)}`, {
        timeout: 10000
      }),
    { alsoRetryStatus: [404] }
  );
  const notebookExists = projectData.data.content.some(
    file => file.type === 'notebook'
  );
  if (!notebookExists) {
    await withRetry(
      async () => {
        try {
          await userApi.put(
            `/contents/${encodeURIComponent(notebookPath)}`,
            {
              type: 'notebook',
              format: 'json',
              content: {
                cells: [],
                metadata: {},
                nbformat: 4,
                nbformat_minor: 5
              }
            },
            { timeout: 10000 }
          );
          return true;
        } catch (e) {
          if (e.response?.status === 409) {
            // already exists; treat as success
            return true;
          }
          throw e;
        }
      },
      { alsoRetryStatus: [404] }
    );
  }

  // 4) Start a session bound to the notebook with kernel python3
  const sessionResp = await withRetry(
    () =>
      userApi.post(
        '/sessions',
        {
          kernel: { name: 'python3' },
          name: '',
          type: 'notebook',
          path: notebookPath
        },
        { timeout: 60000 }
      ),
    { alsoRetryStatus: [404], retries: 6, baseDelayMs: 1000 }
  );

  const session = sessionResp.data;
  const kernel = session.kernel;

  return {
    folderPath: `/${folderPath}`,
    notebookPath: `/${notebookPath}`,
    server,
    session,
    kernel
  };
};

// ------------------ Execute Code ------------------
const executeCode = async (kernelId, code, options) => {
  if (!code) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Code is required');
  }

  logger.info(`Executing code in kernel: ${kernelId}`);

  const result = await jupyterExecutionService.executeCode(
    kernelId,
    code,
    options
  );

  return {
    kernel_id: kernelId,
    execution_count: result.execution_count,
    status: result.status,
    outputs: result.outputs,
    error: result.error || null
  };
};

// ------------------ Execute Notebook ------------------
const executeNotebook = async (kernelId, notebook, options) => {
  if (!notebook || !notebook.cells) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Notebook JSON with cells is required'
    );
  }

  logger.info(
    `Executing notebook with ${notebook.cells.length} cells in kernel: ${kernelId}`
  );

  const results = [];
  for (const [index, cell] of notebook.cells.entries()) {
    if (cell.cell_type !== 'code') {
      continue;
    } // Skip non-code cells

    const code = Array.isArray(cell.source)
      ? cell.source.join('')
      : String(cell.source || '');
    const cellOptions = {
      ...options,
      silent: false,
      store_history: true,
      user_expressions: {},
      allow_stdin: false,
      stop_on_error: options.stopOnError
    };

    try {
      const result = await jupyterExecutionService.executeCode(
        kernelId,
        code,
        cellOptions
      );
      results.push({
        cell_index: index,
        ...result
      });
    } catch (error) {
      results.push({
        cell_index: index,
        success: false,
        error: error.message
      });
      if (options.stopOnError) {
        break;
      } // Stop on first error if enabled
    }
  }

  return {
    kernel_id: kernelId,
    total_cells: results.length,
    results
  };
};

// ------------------ Exports ------------------
const jupyterService = {
  jupyterhubApi,
  ensureServerIsRunning,
  getUser,
  createUser,
  getServerStatus,
  startServer,
  stopServer,
  getUserToken,
  createWorkspace,
  executeCode,
  executeNotebook
};

export default jupyterService;
