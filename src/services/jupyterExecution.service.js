import WebSocket from 'ws';
import logger from '../config/logger.config.js';
import config from '../config/database.config.js';
import jupyterService from './jupyterhub.service.js';

const BASE_WS_URL = config.jupyterhub.url.replace(/^http/, 'ws');

// Maintain WebSocket connections per user+kernel
const kernelSockets = new Map(); // key: `${username}:${kernelId}`

function buildWsUrl(username, kernelId, token) {
  return `${BASE_WS_URL}/user/${encodeURIComponent(
    username
  )}/api/kernels/${encodeURIComponent(kernelId)}/channels?token=${encodeURIComponent(
    token
  )}`;
}

function getKey(username, kernelId) {
  return `${username}:${kernelId}`;
}

function createKernelWebSocket(username, kernelId, token) {
  const url = buildWsUrl(username, kernelId, token);
  logger.info(
    `Creating WebSocket to kernel ${kernelId} for ${username}: ${url}`
  );
  const ws = new WebSocket(url);

  ws.on('open', () =>
    logger.info(`WS connected kernel=${kernelId} user=${username}`)
  );
  ws.on('close', () => {
    logger.info(`WS closed kernel=${kernelId} user=${username}`);
    kernelSockets.delete(getKey(username, kernelId));
  });
  ws.on('error', err => {
    logger.error(
      `WS error kernel=${kernelId} user=${username}: ${err.message}`
    );
  });
  ws.on('message', data => {
    try {
      const msg = JSON.parse(data.toString());
      logger.debug(
        `WS msg kernel=${kernelId} user=${username} type=${msg.msg_type} parent=${msg.parent_header?.msg_id}`
      );
    } catch (e) {
      logger.error('Failed to parse WS message', e);
    }
  });

  kernelSockets.set(getKey(username, kernelId), ws);
  return ws;
}

async function getKernelWebSocket(username, kernelId, userToken) {
  const key = getKey(username, kernelId);
  let ws = kernelSockets.get(key);
  if (ws && ws.readyState === WebSocket.OPEN) return ws;

  // Ensure user server is up and token available
  await jupyterService.ensureServerIsRunning(username);
  if (
    !ws ||
    ws.readyState === WebSocket.CLOSED ||
    ws.readyState === WebSocket.CLOSING
  ) {
    ws = createKernelWebSocket(username, kernelId, userToken);
  }
  return ws;
}

export async function executeCode(kernelId, code, options = {}) {
  console.log('In executeCode service, options: ', options);
  return new Promise(async (resolve, reject) => {
    const username = options.username || 'anonymous';
    const userToken = options.userToken || null;
    try {
      const ws = await getKernelWebSocket(username, kernelId, userToken);

      const msgId = `execute_${Date.now()}_${Math.random()
        .toString(36)
        .slice(2, 11)}`;
      const message = {
        header: {
          msg_id: msgId,
          username,
          session: options.session || `session_${Date.now()}`,
          date: new Date().toISOString(),
          msg_type: 'execute_request',
          version: '5.3'
        },
        parent_header: {},
        metadata: {},
        content: {
          code,
          silent: options.silent || false,
          store_history: options.store_history !== false,
          user_expressions: options.user_expressions || {},
          allow_stdin: options.allow_stdin || false,
          stop_on_error: options.stop_on_error !== false
        }
      };

      const results = {
        execution_count: null,
        outputs: [],
        status: 'pending'
      };

      const timeoutMs = options.timeout || 30000;
      const timeoutHandle = setTimeout(() => {
        ws.removeListener('message', handler);
        reject(new Error('Code execution timeout'));
      }, timeoutMs);

      function finish(result) {
        clearTimeout(timeoutHandle);
        ws.removeListener('message', handler);
        resolve(result);
      }

      function handler(raw) {
        let resp;
        try {
          resp = JSON.parse(raw.toString());
        } catch (e) {
          logger.error('Execution parse error', e.message);
          return;
        }
        if (resp.parent_header?.msg_id !== msgId) return; // Not our message

        switch (resp.msg_type) {
          case 'execute_reply':
            results.execution_count = resp.content.execution_count;
            results.status = resp.content.status;
            if (resp.content.status === 'error') {
              results.error = {
                ename: resp.content.ename,
                evalue: resp.content.evalue,
                traceback: resp.content.traceback
              };
            }
            finish(results);
            break;
          case 'stream':
            results.outputs.push({
              output_type: 'stream',
              name: resp.content.name,
              text: resp.content.text
            });
            break;
          case 'execute_result':
            results.outputs.push({
              output_type: 'execute_result',
              execution_count: resp.content.execution_count,
              data: resp.content.data,
              metadata: resp.content.metadata
            });
            break;
          case 'display_data':
            results.outputs.push({
              output_type: 'display_data',
              data: resp.content.data,
              metadata: resp.content.metadata
            });
            break;
          case 'error':
            results.outputs.push({
              output_type: 'error',
              ename: resp.content.ename,
              evalue: resp.content.evalue,
              traceback: resp.content.traceback
            });
            break;
        }
      }

      ws.on('message', handler);

      const sendMessage = () => {
        try {
          ws.send(JSON.stringify(message));
        } catch (e) {
          logger.error('Failed to send execution request', e.message);
          ws.removeListener('message', handler);
          clearTimeout(timeoutHandle);
          reject(e);
        }
      };

      if (ws.readyState === WebSocket.OPEN) {
        sendMessage();
      } else {
        ws.once('open', sendMessage);
      }
    } catch (err) {
      logger.error(`executeCode failed kernel=${kernelId}: ${err.message}`);
      reject(err);
    }
  });
}

// Cleanup on process exit
process.on('exit', () => {
  for (const ws of kernelSockets.values()) {
    try {
      ws.close();
    } catch (e) {
      /* ignore */
    }
  }
});

export default { executeCode };
