import Redis from 'ioredis';
import logger from '../config/logger.config.js';

/**
 * Redis Session Cache Service
 * 
 * Manages user session data in Redis for fast retrieval and automatic expiration.
 * Stores comprehensive user data including LTI context, roles, and permissions.
 */
class SessionCacheService {
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD,
      db: process.env.REDIS_SESSION_DB || 1, // Use separate DB for sessions
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keyPrefix: 'bits:session:'
    });

    this.defaultTTL = 24 * 60 * 60; // 24 hours in seconds
    this.refreshTokenTTL = 7 * 24 * 60 * 60; // 7 days in seconds

    this.redis.on('error', (err) => {
      logger.error('[Session Cache] Redis connection error:', {
        error: err.message,
        code: err.code
      });
    });

    this.redis.on('connect', () => {
      logger.info('[Session Cache] Redis connected successfully');
    });

    this.redis.on('ready', () => {
      logger.info('[Session Cache] Redis ready for operations');
    });
  }

  /**
   * Store user session data in Redis
   * @param {string} userId - User ID
   * @param {Object} sessionData - Complete session data
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} Success status
   */
  async setUserSession(userId, sessionData, ttl = this.defaultTTL) {
    try {
      const key = `user:${userId}`;
      const data = {
        ...sessionData,
        lastAccessed: new Date().toISOString(),
        createdAt: sessionData.createdAt || new Date().toISOString()
      };

      await this.redis.setex(key, ttl, JSON.stringify(data));
      logger.debug(`[Session Cache] User session stored for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('[Session Cache] Error storing user session:', {
        userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Retrieve user session data from Redis
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} Session data or null
   */
  async getUserSession(userId) {
    try {
      const key = `user:${userId}`;
      const data = await this.redis.get(key);
      
      if (!data) {
        logger.debug(`[Session Cache] No session found for user: ${userId}`);
        return null;
      }

      const sessionData = JSON.parse(data);
      
      // Update last accessed time
      await this.updateLastAccessed(userId);
      
      logger.debug(`[Session Cache] Session retrieved for user: ${userId}`);
      return sessionData;
    } catch (error) {
      logger.error('[Session Cache] Error retrieving user session:', {
        userId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Update last accessed time for a user session
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success status
   */
  async updateLastAccessed(userId) {
    try {
      const key = `user:${userId}`;
      const data = await this.redis.get(key);
      
      if (data) {
        const sessionData = JSON.parse(data);
        sessionData.lastAccessed = new Date().toISOString();
        
        // Get current TTL and preserve it
        const ttl = await this.redis.ttl(key);
        if (ttl > 0) {
          await this.redis.setex(key, ttl, JSON.stringify(sessionData));
        }
      }
      
      return true;
    } catch (error) {
      logger.error('[Session Cache] Error updating last accessed:', {
        userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Store refresh token with user ID mapping
   * @param {string} refreshToken - Refresh token
   * @param {string} userId - User ID
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} Success status
   */
  async setRefreshToken(refreshToken, userId, ttl = this.refreshTokenTTL) {
    try {
      const key = `refresh:${refreshToken}`;
      await this.redis.setex(key, ttl, userId);
      logger.debug(`[Session Cache] Refresh token stored for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('[Session Cache] Error storing refresh token:', {
        userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Retrieve user ID from refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<string|null>} User ID or null
   */
  async getUserIdFromRefreshToken(refreshToken) {
    try {
      const key = `refresh:${refreshToken}`;
      const userId = await this.redis.get(key);
      
      if (userId) {
        logger.debug(`[Session Cache] User ID retrieved from refresh token`);
      }
      
      return userId;
    } catch (error) {
      logger.error('[Session Cache] Error retrieving user ID from refresh token:', {
        error: error.message
      });
      return null;
    }
  }

  /**
   * Delete user session
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteUserSession(userId) {
    try {
      const key = `user:${userId}`;
      await this.redis.del(key);
      logger.debug(`[Session Cache] Session deleted for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('[Session Cache] Error deleting user session:', {
        userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Delete refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<boolean>} Success status
   */
  async deleteRefreshToken(refreshToken) {
    try {
      const key = `refresh:${refreshToken}`;
      await this.redis.del(key);
      logger.debug(`[Session Cache] Refresh token deleted`);
      return true;
    } catch (error) {
      logger.error('[Session Cache] Error deleting refresh token:', {
        error: error.message
      });
      return false;
    }
  }

  /**
   * Delete all sessions for a user (logout from all devices)
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteAllUserSessions(userId) {
    try {
      // Delete user session
      await this.deleteUserSession(userId);
      
      // Find and delete all refresh tokens for this user
      const refreshKeys = await this.redis.keys('refresh:*');
      const pipeline = this.redis.pipeline();
      
      for (const key of refreshKeys) {
        const storedUserId = await this.redis.get(key);
        if (storedUserId === userId) {
          pipeline.del(key);
        }
      }
      
      await pipeline.exec();
      logger.debug(`[Session Cache] All sessions deleted for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('[Session Cache] Error deleting all user sessions:', {
        userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Extend session TTL
   * @param {string} userId - User ID
   * @param {number} ttl - New TTL in seconds
   * @returns {Promise<boolean>} Success status
   */
  async extendSession(userId, ttl = this.defaultTTL) {
    try {
      const key = `user:${userId}`;
      const exists = await this.redis.exists(key);
      
      if (exists) {
        await this.redis.expire(key, ttl);
        logger.debug(`[Session Cache] Session extended for user: ${userId}`);
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('[Session Cache] Error extending session:', {
        userId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Get session TTL
   * @param {string} userId - User ID
   * @returns {Promise<number>} TTL in seconds (-1 if no expiry, -2 if key doesn't exist)
   */
  async getSessionTTL(userId) {
    try {
      const key = `user:${userId}`;
      return await this.redis.ttl(key);
    } catch (error) {
      logger.error('[Session Cache] Error getting session TTL:', {
        userId,
        error: error.message
      });
      return -2;
    }
  }

  /**
   * Check if Redis is connected
   * @returns {boolean} Connection status
   */
  isConnected() {
    return this.redis.status === 'ready';
  }

  /**
   * Close Redis connection
   * @returns {Promise<void>}
   */
  async close() {
    try {
      await this.redis.quit();
      logger.info('[Session Cache] Redis connection closed');
    } catch (error) {
      logger.error('[Session Cache] Error closing Redis connection:', error);
    }
  }
}

export default new SessionCacheService();
