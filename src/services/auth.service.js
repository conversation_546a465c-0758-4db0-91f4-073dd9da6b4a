import httpStatus from 'http-status';
import { User, Role, Permission } from '../models/associations.js';
import ApiError from '../utils/ApiError.utils.js';
import logger from '../config/logger.config.js';
import jwtService from './jwt.service.js';
import sessionCacheService from './sessionCache.service.js';

class authService {
  /**
   * login
   */
  async login(req) {
    const { email, password } = req.body;

    if (!email || !password)
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Email and password are required'
      );

    // Find user with roles and permissions
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [
        {
          model: Role,
          as: 'roles',
          include: [
            {
              model: Permission,
              as: 'permissions'
            }
          ]
        }
      ]
    });

    if (!user)
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'No user found with this email address'
      );
    if (user.status !== 'active')
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Your account is not active. Please contact administrator.'
      );

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid)
      throw new ApiError(httpStatus.BAD_REQUEST, 'Incorrect password');

    // Update last login
    await user.updateLastLogin();

    // Prepare user data for JWT and session
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      lms_user_id: user.lms_user_id,
      google_id: user.google_id,
      profile_picture: user.profile_picture,
      last_login: user.last_login,
      status: user.status,
      preferences: user.preferences || {},
      metadata: user.metadata || {},
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      deletedAt: user.deletedAt,
      roles: user.roles || []
    };

    // Generate JWT token pair
    const tokenPair = jwtService.generateTokenPair(userData, {
      sessionType: 'manual'
    });

    // Prepare session data for Redis storage
    const sessionData = {
      cookie: {
        originalMaxAge: 86400000,
        expires: new Date(Date.now() + 86400000).toISOString(),
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        path: '/'
      },
      user: userData,
      sessionType: 'manual',
      ltiContext: null,
      ltiResourceLink: null,
      ltiLaunchData: null,
      createdAt: new Date().toISOString()
    };

    // Store session data in Redis
    await sessionCacheService.setUserSession(user.id, sessionData);

    // Store refresh token mapping
    await sessionCacheService.setRefreshToken(tokenPair.refreshToken, user.id);

    // Prepare user response (without sensitive data)
    const userResponse = {
      id: user.id,
      name: user.name,
      email: user.email,
      profilePicture: user.profile_picture,
      lastLogin: user.last_login,
      roles:
        user.roles?.map(role => ({
          id: role.id,
          name: role.name,
          permissions: role.permissions?.map(permission => permission.key) || []
        })) || []
    };

    logger.info(`User logged in: ${user.email}`);

    return {
      accessToken: tokenPair.accessToken,
      refreshToken: tokenPair.refreshToken,
      expiresIn: tokenPair.expiresIn,
      tokenType: tokenPair.tokenType,
      user: userResponse
    };
  }
}

export default new authService();
