import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { URL, URLSearchParams } from 'url';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
} from '../models/ltiAssociations.models.js';
import {
  User,
  Course,
  Project,
  Role,
  UserRole
} from '../models/associations.js';
import logger from '../config/logger.config.js';
import ltiTokenCache from './ltiTokenCache.service.js';
import ltiTokenService from './ltiTokenService.js';
import httpStatus from 'http-status';
import ApiError from '../utils/ApiError.utils.js';

class LtiService {
  constructor() {
    this.toolUrl = process.env.LTI_TOOL_URL || 'https://your-tool-domain.com';
    this.keyId = process.env.LTI_KEY_ID || 'bits-lti-key-1';
    this.privateKey = process.env.LTI_PRIVATE_KEY;
    this.publicKey = process.env.LTI_PUBLIC_KEY;

    // Generate keys if not provided
    if (!this.privateKey || !this.publicKey) {
      this.generateKeyPair();
    }
  }

  /**
   * Ensure a local + remote line item exists for a given resource link + project.
   * If local exists but missing remote identifier, attempt remote creation.
   */
  async ensureLineItem({
    platform,
    context,
    resourceLink,
    project,
    scoreMaximum = 100
  }) {
    if (!platform || !context || !resourceLink || !project) {
      throw new Error('Missing required entities to ensure line item');
    }

    // Find existing local record
    let lineItem = await LtiLineItem.findOne({
      where: { resourceLinkId: resourceLink.id, projectId: project.id }
    });

    if (lineItem && lineItem.lineItemId) {
      return lineItem; // Already have remote mapping
    }

    // If no local record, create it (will assign a generated lineItemId placeholder)
    if (!lineItem) {
      lineItem = await this.createLineItem(resourceLink, project, scoreMaximum);
    }

    // Remote creation is handled by createRemoteLineItem when needed.
    return lineItem;
  }

  /**
   * Create remote line item at platform AGS lineitems endpoint.
   * Returns remote response (line item object) and updates local db record.
   */
  async createRemoteLineItem({
    platform,
    lineItemsUrl,
    label,
    scoreMaximum = 100,
    resourceLink,
    project,
    tag = 'score',
    resourceId
  }) {
    if (!platform || !lineItemsUrl) {
      throw new Error('Missing platform or lineItemsUrl');
    }
    const scopes = ['https://purl.imsglobal.org/spec/lti-ags/scope/lineitem'];
    const accessToken = await this.getAGSAccessToken(platform, scopes);
    const body = {
      label: label || project?.title || 'Activity',
      scoreMaximum,
      tag,
      resourceId: resourceId || project?.id,
      resourceLinkId: resourceLink?.resourceLinkId
    };
    const resp = await axios.post(lineItemsUrl, body, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/vnd.ims.lti-ags.v2.lineitem+json'
      }
    });
    logger.debug(
      'createRemoteLineItem: resp -> %s',
      JSON.stringify(resp?.data)
    );

    // Update/create local
    if (resourceLink && project) {
      const [local] = await LtiLineItem.findOrCreate({
        where: { resourceLinkId: resourceLink.id, projectId: project.id },
        defaults: {
          platformId: resourceLink.platformId,
          contextId: resourceLink.contextId,
          resourceLinkId: resourceLink.id,
          projectId: project.id,
          lineItemId:
            resp.data.id ||
            resp.data?.lineItemId ||
            resp.data?.identifier ||
            uuidv4(),
          scoreMaximum,
          label: body.label,
          tag,
          resourceId: body.resourceId
        }
      });
      // If existing, update remote id/label
      await local.update({
        lineItemId: resp.data.id || resp.data?.lineItemId || local.lineItemId,
        label: body.label,
        scoreMaximum,
        tag
      });
    }
    return resp.data;
  }

  /**
   * Post a score to a specific remote line item scores sub-endpoint.
   */
  async postScoreToLineItem({
    platform,
    lineItem,
    userLmsId,
    scoreGiven,
    scoreMaximum,
    comment
  }) {
    if (!platform || !lineItem) {
      throw new Error('Missing platform or line item');
    }
    const scopes = ['https://purl.imsglobal.org/spec/lti-ags/scope/score'];
    const accessToken = await this.getAGSAccessToken(platform, scopes);
    // Normally the line item object has an "id" which is a URL; fallback build
    const lineItemUrl = lineItem.lineItemId?.startsWith('http')
      ? lineItem.lineItemId
      : null;
    if (!lineItemUrl) {
      logger.warn(
        'postScoreToLineItem: lineItemId not a URL, remote post skipped'
      );
      return { skipped: true, reason: 'lineItemId not URL' };
    }
    const scoreUrl = `${lineItemUrl}/scores`;
    const body = {
      userId: userLmsId,
      scoreGiven,
      scoreMaximum: scoreMaximum || lineItem.scoreMaximum || 100,
      activityProgress: 'Completed',
      gradingProgress: 'FullyGraded',
      timestamp: new Date().toISOString(),
      comment: comment || undefined
    };
    const resp = await axios.post(scoreUrl, body, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/vnd.ims.lis.v1.score+json'
      }
    });
    return resp.data || { success: true };
  }

  /**
   * Get all results for a line item (results sub-endpoint)
   */
  async getResults({ platform, lineItem }) {
    if (!platform || !lineItem) {
      throw new Error('Missing platform or line item');
    }
    const scopes = [
      'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly'
    ];
    const accessToken = await this.getAGSAccessToken(platform, scopes);
    const lineItemUrl = lineItem.lineItemId?.startsWith('http')
      ? lineItem.lineItemId
      : null;
    if (!lineItemUrl) {
      throw new Error('Line item remote id not a URL');
    }
    const resultsUrl = `${lineItemUrl}/results`;
    const resp = await axios.get(resultsUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/vnd.ims.lis.v2.resultcontainer+json'
      }
    });
    return resp.data;
  }

  /**
   * Get result for single user for a line item (filtering client-side if platform lacks single-user endpoint)
   */
  async getResultForUser({ platform, lineItem, userLmsId }) {
    const data = await this.getResults({ platform, lineItem });
    if (Array.isArray(data)) {
      return data.find(r => r.userId === userLmsId) || null;
    }
    if (Array.isArray(data?.results)) {
      return data.results.find(r => r.userId === userLmsId) || null;
    }
    return null;
  }

  /**
   * Generate RSA key pair for JWT signing
   */
  generateKeyPair() {
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    this.privateKey = privateKey;
    this.publicKey = publicKey;

    logger.info('Generated new RSA key pair for LTI');
  }

  /**
   * Get JWKS (JSON Web Key Set) for the tool
   */
  getJWKS() {
    try {
      const pubKey = crypto.createPublicKey(this.publicKey);
      const jwk = pubKey.export({ format: 'jwk' });
      const n = jwk.n; // base64url
      const e = jwk.e; // base64url

      if (!n || !e) {
        logger.error('getJWKS: Failed to extract n/e from public key JWK');
        throw new Error('Invalid public key JWK');
      }

      return {
        keys: [
          {
            kty: jwk.kty || 'RSA',
            use: 'sig',
            kid: this.keyId,
            alg: 'RS256',
            n,
            e
          }
        ]
      };
    } catch (err) {
      logger.error('getJWKS: Error exporting JWKS', {
        message: err?.message,
        name: err?.name,
        stack: err?.stack
      });
      throw err;
    }
  }

  /**
   * Verify JWT token from platform
   */
  async verifyJWT(token, platform) {
    try {
      logger.info('[LTI] verifyJWT: Starting JWT verification');

      // Get platform's public keys
      const platformKeys = await this.getPlatformKeys(platform.keySetUrl);

      // Verify and decode token
      const decoded = jwt.verify(token, platformKeys, {
        algorithms: ['RS256'],
        issuer: platform.platformId,
        audience: platform.clientId
      });

      logger.info('[LTI] verifyJWT: JWT signature verified, validating claims');

      // Validate LTI-specific claims
      this.validateLtiClaims(decoded);

      logger.info('[LTI] verifyJWT: JWT verification successful');
      return decoded;
    } catch (error) {
      logger.error('[LTI] verifyJWT: JWT verification failed:', error);
      throw new Error(`Invalid JWT token: ${error.message}`);
    }
  }

  /**
   * Validate LTI-specific claims
   */
  validateLtiClaims(payload) {
    const requiredClaims = [
      'iss',
      'aud',
      'exp',
      'iat',
      'nonce',
      'https://purl.imsglobal.org/spec/lti/claim/message_type',
      'https://purl.imsglobal.org/spec/lti/claim/version'
    ];

    for (const claim of requiredClaims) {
      if (!payload[claim]) {
        throw new Error(`Missing required claim: ${claim}`);
      }
    }

    // Validate message type
    const messageType =
      payload['https://purl.imsglobal.org/spec/lti/claim/message_type'];
    if (
      !['LtiResourceLinkRequest', 'LtiDeepLinkingRequest'].includes(messageType)
    ) {
      throw new Error(`Invalid message type: ${messageType}`);
    }

    // Validate version
    const version =
      payload['https://purl.imsglobal.org/spec/lti/claim/version'];
    if (version !== '1.3.0') {
      throw new Error(`Invalid LTI version: ${version}`);
    }

    // Validate expiration
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw new Error('Token has expired');
    }

    // Validate not before
    if (payload.nbf && payload.nbf > now) {
      throw new Error('Token not yet valid');
    }
  }

  /**
   * Get platform's public keys from JWKS endpoint
   */
  async getPlatformKeys(keySetUrl) {
    try {
      logger.info(`[LTI] getPlatformKeys: Fetching keys from ${keySetUrl}`);

      const response = await axios.get(keySetUrl, {
        timeout: 10000,
        headers: {
          Accept: 'application/json'
        }
      });

      const jwks = response.data;

      if (!jwks.keys || !Array.isArray(jwks.keys) || jwks.keys.length === 0) {
        throw new Error('No keys found in JWKS');
      }

      logger.info(`[LTI] getPlatformKeys: Found ${jwks.keys.length} keys`);

      // Convert JWKS to PEM format for verification
      // For now, use the first key - in production, match by kid
      const key = jwks.keys[0];
      const pemKey = this.jwkToPem(key);

      logger.info('[LTI] getPlatformKeys: Successfully converted JWK to PEM');
      return pemKey;
    } catch (error) {
      logger.error(
        '[LTI] getPlatformKeys: Failed to fetch platform keys:',
        error
      );
      throw new Error(`Could not fetch platform keys: ${error.message}`);
    }
  }

  /**
   * Convert JWK to PEM format
   */
  jwkToPem(jwk) {
    // This is a simplified implementation
    // In production, use the node-jose or jwk-to-pem library

    // Create public key from JWK directly with string values
    const keyObject = crypto.createPublicKey({
      key: {
        kty: 'RSA',
        n: jwk.n,
        e: jwk.e
      },
      format: 'jwk'
    });

    return keyObject.export({ format: 'pem', type: 'spki' });
  }

  /**
   * Generate authentication request for LTI launch
   */
  generateAuthRequest(platform, targetLinkUri, loginHint, ltiMessageHint) {
    const state = this.generateState();
    const nonce = this.generateNonce();

    const authParams = new URLSearchParams({
      response_type: 'id_token',
      response_mode: 'form_post',
      scope: 'openid',
      client_id: platform.clientId,
      redirect_uri: `${this.toolUrl}/api/lti/oidc/callback`,
      login_hint: loginHint,
      state,
      nonce,
      prompt: 'none'
    });

    if (ltiMessageHint) {
      authParams.append('lti_message_hint', ltiMessageHint);
    }

    // Store session data
    this.storeLaunchSession({
      platformId: platform.id,
      state,
      nonce,
      targetLinkUri,
      loginHint,
      ltiMessageHint
    });

    return `${platform.authLoginUrl}?${authParams.toString()}`;
  }

  /**
   * Store launch session data
   */
  async storeLaunchSession(sessionData) {
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await LtiLaunchSession.create({
      platformId: sessionData.platformId,
      sessionId,
      state: sessionData.state,
      nonce: sessionData.nonce,
      launchData: {
        targetLinkUri: sessionData.targetLinkUri,
        loginHint: sessionData.loginHint,
        ltiMessageHint: sessionData.ltiMessageHint
      },
      expiresAt
    });

    return sessionId;
  }

  /**
   * Process LTI launch request
   */
  async processLaunch(idToken, state) {
    try {
      // Find launch session
      const session = await LtiLaunchSession.findOne({
        where: { state, isUsed: false },
        include: [{ model: LtiPlatform, as: 'platform' }]
      });

      if (!session || new Date() > session.expiresAt) {
        throw new Error('Invalid or expired launch session');
      }

      // Verify JWT
      const launchData = await this.verifyJWT(idToken, session.platform);

      // Validate nonce
      if (launchData.nonce !== session.nonce) {
        throw new Error('Nonce mismatch');
      }

      // Mark session as used
      await session.update({
        isUsed: true,
        idToken,
        launchData: { ...session.launchData, ...launchData }
      });

      // Process launch data
      const processedData = await this.processLaunchData(
        launchData,
        session.platform
      );

      return {
        user: processedData.user,
        context: processedData.context,
        resourceLink: processedData.resourceLink,
        launchData: launchData
      };
    } catch (error) {
      logger.error('LTI launch processing failed:', error);
      throw error;
    }
  }

  /**
   * Process launch data and sync with local database
   */
  async processLaunchData(launchData, platform) {
    // Extract user information
    const user = await this.syncUser(launchData, platform);

    // Extract context information
    const context = await this.syncContext(launchData, platform);

    const resourceLinkClaim =
      launchData['https://purl.imsglobal.org/spec/lti/claim/resource_link'];

    // Extract resource link information
    const resourceLink = await this.syncResourceLink(
      resourceLinkClaim,
      platform,
      context
    );

    let lineItem;
    if (resourceLinkClaim?.id) {
      lineItem = await this.syncLineItem(
        launchData,
        context,
        resourceLinkClaim
      );
    }

    return { user, context, resourceLink, lineItem };
  }

  /**
   * Sync user from LTI launch data
   */
  async syncUser(launchData, platform) {
    const userData = {
      lms_user_id: launchData.sub,
      email: launchData.email,
      name:
        launchData.name || `${launchData.given_name} ${launchData.family_name}`,
      firstName: launchData.given_name,
      lastName: launchData.family_name,
      role: this.mapLtiRoles(
        launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
      )
    };

    const [user, created] = await User.findOrCreate({
      where: { lms_user_id: userData.lms_user_id },
      defaults: {
        name: userData.name,
        email: userData.email,
        lms_user_id: userData.lms_user_id,
        status: 'active',
        metadata: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          ltiRoles:
            launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
        }
      }
    });

    if (!created) {
      // Update existing user
      await user.update({
        name: userData.name,
        email: userData.email,
        metadata: {
          ...user.metadata,
          firstName: userData.firstName,
          lastName: userData.lastName,
          ltiRoles:
            launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
        }
      });
    }

    // Handle role assignment for both new and existing users
    await this.assignUserRolesFromLti(user, launchData, created);

    return user;
  }

  /**
   * Assign user roles based on LTI launch data
   */
  async assignUserRolesFromLti(user, launchData, isNewUser) {
    try {
      const ltiRoles =
        launchData['https://purl.imsglobal.org/spec/lti/claim/roles'] || [];

      logger.info('[LTI] Assigning user roles from LTI launch', {
        userId: user.id,
        ltiRoles,
        isNewUser
      });

      // Map LTI roles to internal role names (matching your roles table)
      const roleMapping = {
        'http://purl.imsglobal.org/vocab/lis/v2/membership#Instructor':
          'instructor',
        'http://purl.imsglobal.org/vocab/lis/v2/membership#TeachingAssistant':
          'ta',
        'http://purl.imsglobal.org/vocab/lis/v2/membership#Administrator':
          'admin',
        'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner': 'student',
        'http://purl.imsglobal.org/vocab/lis/v2/system/person#Administrator':
          'admin',
        'http://purl.imsglobal.org/vocab/lis/v2/institution/person#Administrator':
          'admin',
        'http://purl.imsglobal.org/vocab/lis/v2/institution/person#Instructor':
          'instructor',
        'http://purl.imsglobal.org/vocab/lis/v2/institution/person#Student':
          'student'
      };

      // Get unique role names to assign
      const rolesToAssign = new Set();

      for (const ltiRole of ltiRoles) {
        const mappedRole = roleMapping[ltiRole];
        if (mappedRole) {
          rolesToAssign.add(mappedRole);
        } else {
          // Check for partial matches (case-insensitive)
          const lowerRole = ltiRole.toLowerCase();
          if (
            lowerRole.includes('instructor') ||
            lowerRole.includes('teacher')
          ) {
            rolesToAssign.add('instructor');
          } else if (
            lowerRole.includes('administrator') ||
            lowerRole.includes('admin')
          ) {
            rolesToAssign.add('admin');
          } else if (
            lowerRole.includes('teachingassistant') ||
            lowerRole.includes('ta')
          ) {
            rolesToAssign.add('ta');
          } else if (
            lowerRole.includes('learner') ||
            lowerRole.includes('student')
          ) {
            rolesToAssign.add('student');
          }
        }
      }

      // Default to student if no roles mapped
      if (rolesToAssign.size === 0) {
        rolesToAssign.add('student');
      }

      logger.info('[LTI] Mapped roles for assignment', {
        userId: user.id,
        rolesToAssign: Array.from(rolesToAssign)
      });

      // Find role IDs from database
      const roles = await Role.findAll({
        where: {
          name: Array.from(rolesToAssign)
        }
      });

      const roleMap = {};
      roles.forEach(role => {
        roleMap[role.name] = role.id;
      });

      // Create user role assignments
      const userRolePromises = [];
      let isPrimary = true; // First role assigned becomes primary

      for (const roleName of rolesToAssign) {
        const roleId = roleMap[roleName];

        if (!roleId) {
          logger.warn('[LTI] Role not found in database', {
            roleName,
            userId: user.id
          });
          continue;
        }

        // Check if user already has this role
        const existingUserRole = await UserRole.findOne({
          where: {
            user_id: user.id,
            role_id: roleId
          }
        });

        if (!existingUserRole) {
          userRolePromises.push(
            UserRole.create({
              user_id: user.id,
              role_id: roleId,
              assigned_by: null, // System assigned from LTI
              is_primary: isPrimary,
              context: {
                source: 'lti_launch',
                lti_roles: ltiRoles,
                assigned_at: new Date().toISOString()
              }
            })
          );

          logger.info('[LTI] Creating user role assignment', {
            userId: user.id,
            roleId,
            roleName,
            isPrimary
          });

          isPrimary = false; // Only first role is primary
        } else {
          logger.info('[LTI] User already has role', {
            userId: user.id,
            roleId,
            roleName
          });

          // Update context with latest LTI info
          await existingUserRole.update({
            context: {
              ...existingUserRole.context,
              lti_roles: ltiRoles,
              last_lti_sync: new Date().toISOString()
            }
          });
        }
      }

      // Execute all role assignments
      if (userRolePromises.length > 0) {
        await Promise.all(userRolePromises);

        logger.info('[LTI] Successfully assigned roles to user', {
          userId: user.id,
          assignedRoles: userRolePromises.length
        });
      }
    } catch (error) {
      logger.error('[LTI] Failed to assign user roles from LTI launch', {
        userId: user.id,
        error: error.message,
        stack: error.stack
      });

      // Don't throw error - user creation should still succeed even if role assignment fails
      // Fall back to default Student role if no roles assigned
      try {
        const existingRoles = await UserRole.count({
          where: { user_id: user.id }
        });

        if (existingRoles === 0) {
          const studentRole = await Role.findOne({
            where: { name: 'student' }
          });

          if (studentRole) {
            await UserRole.create({
              user_id: user.id,
              role_id: studentRole.id,
              assigned_by: null,
              is_primary: true,
              context: {
                source: 'lti_fallback',
                reason: 'role_assignment_failed'
              }
            });

            logger.info('[LTI] Assigned fallback Student role', {
              userId: user.id
            });
          }
        }
      } catch (fallbackError) {
        logger.error('[LTI] Failed to assign fallback role', {
          userId: user.id,
          error: fallbackError.message
        });
      }
    }
  }

  /**
   * Sync context from LTI launch data
   */
  async syncContext(launchData, platform) {
    const contextClaim =
      launchData['https://purl.imsglobal.org/spec/lti/claim/context'];
    const deploymentId =
      launchData['https://purl.imsglobal.org/spec/lti/claim/deployment_id'];

    if (!contextClaim) {
      return null;
    }

    // Find or create deployment
    const [deployment] = await LtiDeployment.findOrCreate({
      where: {
        platformId: platform.id,
        deploymentId: deploymentId
      },
      defaults: {
        platformId: platform.id,
        deploymentId: deploymentId,
        deploymentName: `Deployment ${deploymentId}`
      }
    });

    // Find or create context
    const [context] = await LtiContext.findOrCreate({
      where: {
        platformId: platform.id,
        contextId: contextClaim.id
      },
      defaults: {
        platformId: platform.id,
        deploymentId: deployment.id,
        contextId: contextClaim.id,
        contextType: contextClaim.type?.[0] || 'Course',
        contextTitle: contextClaim.title,
        contextLabel: contextClaim.label
      }
    });

    // Try to match with existing course
    if (!context.courseId && contextClaim.title) {
      const course = await Course.findOne({
        where: { name: contextClaim.title }
      });

      if (course) {
        await context.update({ courseId: course.id });
      }
    }

    return context;
  }

  /**
   * Sync resource link from LTI launch data
   */
  async syncResourceLink(resourceLinkClaim, platform, context) {
    if (!resourceLinkClaim || !context) {
      return null;
    }

    const [resourceLink] = await LtiResourceLink.findOrCreate({
      where: {
        platformId: platform.id,
        resourceLinkId: resourceLinkClaim.id
      },
      defaults: {
        platformId: platform.id,
        contextId: context.contextId,
        resourceLinkId: resourceLinkClaim.id,
        resourceLinkTitle: resourceLinkClaim.title,
        resourceLinkDescription: resourceLinkClaim.description
      }
    });

    if (resourceLink && resourceLinkClaim?.id) {
      const needsUpdate =
        (!resourceLink.contextId && context?.contextId) ||
        (resourceLinkClaim.title &&
          resourceLink.resourceLinkTitle !== resourceLinkClaim.title) ||
        (resourceLinkClaim.description &&
          resourceLink.resourceLinkDescription !==
            resourceLinkClaim.description);
      if (needsUpdate) {
        await resourceLink.update({
          contextId: resourceLink.contextId || context?.contextId,
          resourceLinkTitle:
            resourceLink.resourceLinkTitle || resourceLinkClaim.title || null,
          resourceLinkDescription:
            resourceLink.resourceLinkDescription ||
            resourceLinkClaim.description ||
            null
        });
      }
    }

    return resourceLink;
  }

  async syncLineItem(launchData, context, resourceLink) {
    try {
      const { found, lineItem } = await parseLineItemByResourceLinkId(
        launchData,
        context,
        resourceLink.id
      );

      if (found && lineItem?.id) {
        const [localLineItem, created] = await LtiLineItem.findOrCreate({
          where: { lineItemId: lineItem.id, platformId: platform.id },
          defaults: {
            platformId: platform.id,
            contextId: context?.id,
            resourceLinkId: resourceLink.id,
            projectId: null,
            lineItemId: lineItem.id,
            scoreMaximum: lineItem.scoreMaximum || 100,
            label: lineItem.label || null,
            tag: lineItem.tag || null,
            resourceId: lineItem.resourceId || null,
            startDateTime: lineItem.startDateTime
              ? new Date(lineItem.startDateTime)
              : null,
            endDateTime: lineItem.endDateTime
              ? new Date(lineItem.endDateTime)
              : null
          }
        });

        if (!created) {
          const updates = {};
          if (!localLineItem.contextId && context?.id) {
            updates.contextId = context.id;
          }
          if (!localLineItem.resourceLinkId && resourceLink?.id) {
            updates.resourceLinkId = resourceLink.id;
          }
          if (
            (lineItem.label && localLineItem.label !== lineItem.label) ||
            (lineItem.scoreMaximum &&
              Number(localLineItem.scoreMaximum) !==
                Number(lineItem.scoreMaximum)) ||
            (lineItem.tag && localLineItem.tag !== lineItem.tag)
          ) {
            updates.label = lineItem.label || localLineItem.label;
            updates.scoreMaximum =
              lineItem.scoreMaximum || localLineItem.scoreMaximum;
            updates.tag = lineItem.tag || localLineItem.tag;
          }
          if (Object.keys(updates).length) {
            await localLineItem.update(updates);
          }
        }
      }
    } catch (agsErr) {
      logger.warn(
        `[LTI Service] processLaunchData: Failed to upsert AGS line item by resource_link_id ${agsErr?.message}`
      );
    }
  }

  /**
   * Map LTI roles to internal roles using enhanced role mapping
   */
  async mapLtiRoles(ltiRoles) {
    try {
      // Import the role service
      const ltiRoleService = (await import('./ltiRoleService.js')).default;

      // Parse roles from launch data
      const roleInfo = ltiRoleService.parseRolesFromLaunch({
        'https://purl.imsglobal.org/spec/lti/claim/roles': ltiRoles
      });

      logger.info('[LTI Service] Mapped LTI roles to internal roles', {
        ltiRoles,
        internalRoles: roleInfo.internalRoles,
        highestRole: roleInfo.highestRole
      });

      return roleInfo;
    } catch (error) {
      logger.error('[LTI Service] Failed to map LTI roles:', {
        error: error.message,
        ltiRoles
      });

      // Fallback to legacy mapping
      return this.mapLtiRoleLegacy(ltiRoles);
    }
  }

  /**
   * Legacy role mapping (for backward compatibility)
   */
  mapLtiRoleLegacy(ltiRoles) {
    if (!ltiRoles || !Array.isArray(ltiRoles)) {
      return {
        internalRoles: ['Student'],
        highestRole: 'Student',
        permissions: []
      };
    }

    for (const role of ltiRoles) {
      if (role.includes('Instructor') || role.includes('TeachingAssistant')) {
        return {
          internalRoles: ['ProjectOwner'],
          highestRole: 'ProjectOwner',
          permissions: []
        };
      }
      if (role.includes('Administrator')) {
        return {
          internalRoles: ['Administrator'],
          highestRole: 'Administrator',
          permissions: []
        };
      }
    }

    return {
      internalRoles: ['Student'],
      highestRole: 'Student',
      permissions: []
    };
  }

  /**
   * Create or update line item for grade passback
   */
  async createLineItem(resourceLink, project, scoreMaximum = 100) {
    const [lineItem] = await LtiLineItem.findOrCreate({
      where: {
        resourceLinkId: resourceLink.id,
        projectId: project.id
      },
      defaults: {
        platformId: resourceLink.platformId,
        contextId: resourceLink.contextId,
        resourceLinkId: resourceLink.id,
        projectId: project.id,
        lineItemId: uuidv4(),
        scoreMaximum,
        label: project.title,
        tag: 'score',
        resourceId: project.id
      }
    });

    return lineItem;
  }

  /**
   * Send grade to platform via AGS
   */
  async sendGrade(userId, lineItem, score, platform) {
    try {
      // Get access token for AGS
      const accessToken = await this.getAGSAccessToken(platform);

      // Prepare score data
      const scoreData = {
        userId: userId,
        scoreGiven: score,
        scoreMaximum: lineItem.scoreMaximum,
        activityProgress: 'Completed',
        gradingProgress: 'FullyGraded',
        timestamp: new Date().toISOString()
      };

      // Send to platform AGS endpoint
      const agsUrl = `${platform.platformId}/api/lti/ags/scores`;

      await axios.post(agsUrl, scoreData, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/vnd.ims.lis.v1.score+json'
        }
      });

      logger.info(
        `Grade sent to platform: ${score}/${lineItem.scoreMaximum} for user ${userId}`
      );
    } catch (error) {
      logger.error('Failed to send grade to platform:', error);
      throw error;
    }
  }

  /**
   * Get access token for LTI services (AGS/NRPS)
   */
  async getAGSAccessToken(
    platform,
    scopes = ['https://purl.imsglobal.org/spec/lti-ags/scope/score']
  ) {
    try {
      // Local logging helper to ensure inline msg + details are printed in server logs
      const _log = (level, msg, details) => {
        const safe = obj => {
          try {
            return JSON.stringify(obj);
          } catch {
            return '"<unserializable>"';
          }
        };
        logger[level](`msg=${msg} | details=${details ? safe(details) : '{}'}`);
      };

      _log('info', '[LTI] getAGSAccessToken: Creating client assertion');

      const clientAssertion = this.createClientAssertion(platform);

      const tokenRequest = new URLSearchParams({
        grant_type: 'client_credentials',
        client_assertion_type:
          'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: clientAssertion,
        scope: scopes.join(' '),
        audience: 'https://api.brightspace.com/auth/token'
      });

      _log('info', '[LTI] getAGSAccessToken: Sending token request', {
        url: platform.authTokenUrl,
        grant_type: 'client_credentials',
        scope: scopes.join(' '),
        audience: 'https://api.brightspace.com/auth/token'
      });

      const response = await axios.post(platform.authTokenUrl, tokenRequest, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Accept: 'application/json'
        },
        timeout: 10000
      });

      if (!response.data.access_token) {
        throw new Error('No access token in response');
      }

      _log(
        'info',
        '[LTI] getAGSAccessToken: Successfully obtained access token',
        {
          tokenLength: response.data.access_token?.length
        }
      );
      return response.data.access_token;
    } catch (error) {
      const _log = (level, msg, details) => {
        const safe = obj => {
          try {
            return JSON.stringify(obj);
          } catch {
            return '"<unserializable>"';
          }
        };
        logger[level](`msg=${msg} | details=${details ? safe(details) : '{}'}`);
      };
      _log('error', '[LTI] getAGSAccessToken: Failed to get access token', {
        error: error.message,
        platformId: platform.id,
        scopes: Array.isArray(scopes) ? scopes.join(' ') : undefined
      });
      throw new Error(
        `Failed to get LTI service access token: ${error.message}`
      );
    }
  }

  /**
   * Create client assertion JWT for service authentication
   */
  createClientAssertion(platform) {
    const now = Math.floor(Date.now() / 1000);

    const payload = {
      iss: platform.clientId,
      sub: platform.clientId,
      aud: 'https://auth.brightspace.com/core/connect/token', // Brightspace token endpoint
      exp: now + 300, // 5 minutes
      iat: now,
      jti: uuidv4()
    };

    logger.info('[LTI] createClientAssertion: Created client assertion JWT', {
      iss: payload.iss,
      aud: payload.aud,
      exp: payload.exp
    });

    return jwt.sign(payload, this.privateKey, {
      algorithm: 'RS256',
      keyid: this.keyId
    });
  }

  /**
   * Generate secure random state
   */
  generateState() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate secure random nonce
   */
  generateNonce() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Deep linking service - create content items
   */
  createDeepLinkingResponse(projects, deepLinkingClaim, platform) {
    const contentItems = projects.map(project => ({
      type: 'ltiResourceLink',
      title: project.title,
      text: project.description,
      url: `${this.toolUrl}/lti/launch?project=${project.id}`,
      custom: {
        project_id: project.id,
        project_type: project.difficultyLevel
      }
    }));

    const payload = {
      iss: platform.clientId,
      aud: platform.platformId,
      exp: Math.floor(Date.now() / 1000) + 600, // 10 minutes
      iat: Math.floor(Date.now() / 1000),
      nonce: this.generateNonce(),
      'https://purl.imsglobal.org/spec/lti-dl/claim/content_items':
        contentItems,
      'https://purl.imsglobal.org/spec/lti-dl/claim/data': deepLinkingClaim.data
    };

    return jwt.sign(payload, this.privateKey, {
      algorithm: 'RS256',
      keyid: this.keyId
    });
  }

  /**
   * Get line items for a context
   */
  async getLineItems(platform, contextId) {
    try {
      const accessToken = await this.getAGSAccessToken(platform);

      const response = await axios.get(
        `${platform.lineItemsUrl}?contextId=${contextId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/vnd.ims.lis.v2.lineitemcontainer+json'
          }
        }
      );

      return response.data;
    } catch (error) {
      logger.error('Failed to get line items:', error);
      throw error;
    }
  }

  /**
   * Get scores for a line item
   */
  async getScores(platform, lineItemId) {
    try {
      const accessToken = await this.getAGSAccessToken(platform);

      const response = await axios.get(
        `${platform.scoresUrl}?lineItemId=${lineItemId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/vnd.ims.lis.v2.scorecontainer+json'
          }
        }
      );

      return response.data;
    } catch (error) {
      logger.error('Failed to get scores:', error);
      throw error;
    }
  }

  /**
   * Invalidate cached tokens for a platform
   * @param {string} platformId - Platform identifier
   * @param {Array<string>} scopes - Optional specific scopes to invalidate
   */
  async invalidateCachedTokens(platformId, scopes = null) {
    try {
      if (scopes) {
        await ltiTokenCache.invalidateToken(platformId, scopes);
        logger.info('[LTI] Invalidated specific cached tokens', {
          platformId,
          scopes: scopes.join(' ')
        });
      } else {
        await ltiTokenCache.invalidatePlatformTokens(platformId);
        logger.info('[LTI] Invalidated all cached tokens for platform', {
          platformId
        });
      }
    } catch (error) {
      logger.error('[LTI] Failed to invalidate cached tokens:', {
        error: error.message,
        platformId,
        scopes: scopes?.join(' ')
      });
    }
  }

  /**
   * Get token cache statistics
   * @returns {Promise<Object>} Cache statistics
   */
  async getTokenCacheStats() {
    try {
      return await ltiTokenCache.getCacheStats();
    } catch (error) {
      logger.error('[LTI] Failed to get token cache stats:', {
        error: error.message
      });
      return {
        error: error.message
      };
    }
  }

  /**
   * Clean up expired tokens
   * @returns {Promise<number>} Number of tokens cleaned up
   */
  async cleanupExpiredTokens() {
    try {
      const cleanedCount = await ltiTokenCache.cleanupExpiredTokens();
      logger.info('[LTI] Token cleanup completed', {
        cleanedCount
      });
      return cleanedCount;
    } catch (error) {
      logger.error('[LTI] Failed to cleanup expired tokens:', {
        error: error.message
      });
      return 0;
    }
  }

  /**
   * Get AGS (Assignment and Grade Services) token
   * @param {Object} platform - Platform configuration
   * @param {string} deploymentId - Optional deployment ID
   * @returns {Promise<string>} Access token for AGS operations
   */
  async getAgsToken(platform, deploymentId = null) {
    return ltiTokenService.getAgsToken(platform, deploymentId);
  }

  /**
   * Get NRPS (Names and Role Provisioning Services) token
   * @param {Object} platform - Platform configuration
   * @param {string} deploymentId - Optional deployment ID
   * @returns {Promise<string>} Access token for NRPS operations
   */
  async getNrpsToken(platform, deploymentId = null) {
    return ltiTokenService.getNrpsToken(platform, deploymentId);
  }

  /**
   * Invalidate cached tokens for a specific tenant
   * @param {string} issuer - Platform issuer URL
   * @param {string} clientId - Client ID
   * @param {string} deploymentId - Optional deployment ID
   */
  async invalidateTenantTokens(issuer, clientId, deploymentId = null) {
    return ltiTokenService.invalidateTenantTokens(
      issuer,
      clientId,
      deploymentId
    );
  }

  /**
   * Update a remote line item (PUT) to set resourceId to provided value.
   * Falls back gracefully if lineItemId is not a URL.
   */
  async updateRemoteLineItemResourceId({
    platform,
    lineItem,
    resourceId,
    resourceLinkLmsId
  }) {
    if (!platform || !lineItem) {
      throw new Error('Missing platform or line item');
    }
    const lineItemUrl = lineItem.lineItemId?.startsWith('http')
      ? lineItem.lineItemId
      : null;
    if (!lineItemUrl) {
      logger.warn(
        'updateRemoteLineItemResourceId: lineItemId not a URL, remote PUT skipped'
      );
      return { skipped: true, reason: 'lineItemId not URL' };
    }

    const scopes = ['https://purl.imsglobal.org/spec/lti-ags/scope/lineitem'];
    const accessToken = await this.getAGSAccessToken(platform, scopes);

    // Build payload preserving known values
    const payload = {
      label: lineItem.label || 'Activity',
      scoreMaximum: Number(lineItem.scoreMaximum) || 100,
      resourceId: resourceId,
      // Brightspace supports resourceLinkId filter and field - pass LMS resource link id if provided
      ...(resourceLinkLmsId ? { resourceLinkId: resourceLinkLmsId } : {}),
      ...(lineItem.tag ? { tag: lineItem.tag } : {})
    };

    const resp = await axios.put(lineItemUrl, payload, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/vnd.ims.lti-ags.v2.lineitem+json'
      }
    });
    return resp?.data || { success: true };
  }
}

export default new LtiService();

// Individual function exports for testing
export const generateClientAssertion = async config => {
  const service = new LtiService();
  return service.createClientAssertion({
    clientId: config.clientId,
    authTokenUrl: config.tokenUrl,
    privateKey: config.privateKey
  });
};

export const getAccessToken = async config => {
  const service = new LtiService();
  return service.getAGSAccessToken({
    authTokenUrl: config.tokenUrl,
    clientId: config.clientId,
    clientAssertion: config.clientAssertion
  });
};

export const createLineItem = async (platform, lineItem) => {
  const service = new LtiService();
  return service.createLineItem(platform, lineItem);
};

export const getLineItems = async (platform, contextId) => {
  const service = new LtiService();
  return service.getLineItems(platform, contextId);
};

export const createScore = async (platform, lineItemId, score) => {
  const service = new LtiService();
  return service.sendGrade(
    score.userId,
    { id: lineItemId },
    score.scoreGiven,
    platform
  );
};

export const getScores = async (platform, lineItemId) => {
  const service = new LtiService();
  return service.getScores(platform, lineItemId);
};

export const validateLtiToken = async (token, platform) => {
  const service = new LtiService();
  return service.verifyJWT(token, platform);
};

export const parseLtiRequest = request => {
  const body = request.body || {};

  return {
    messageType: body['https://purl.imsglobal.org/spec/lti/claim/message_type'],
    version: body['https://purl.imsglobal.org/spec/lti/claim/version'],
    resourceLinkId:
      body['https://purl.imsglobal.org/spec/lti/claim/resource_link']?.id,
    contextId: body['https://purl.imsglobal.org/spec/lti/claim/context']?.id,
    contextLabel:
      body['https://purl.imsglobal.org/spec/lti/claim/context']?.label,
    contextTitle:
      body['https://purl.imsglobal.org/spec/lti/claim/context']?.title,
    returnUrl:
      body['https://purl.imsglobal.org/spec/lti/claim/launch_presentation']
        ?.return_url,
    roles: body['https://purl.imsglobal.org/spec/lti/claim/roles'],
    userId: body.sub,
    userName: body.name,
    userEmail: body.email
  };
};

/*************  ✨ Windsurf Command ⭐  *************/
/**
 * @function getLineItemByResourceLinkId
 * @description Retrieve a line item from the learning platform by resource link ID
 * @param {LtiPlatform} platform - LTI platform object
 * @param {string} resourceLinkId - Resource link ID of the line item to retrieve
 * @returns {Promise<object>} - Line item object with properties: id, label, scoreMaximum, resourceLinkId, projectId
 */
/*******  e05d1bef-4185-4574-8a17-d11449ca8e03 *******/
export const parseLineItemByResourceLinkId = async (
  launchData,
  context,
  resourceLinkId
) => {
  try {
    const service = new LtiService();

    if (!resourceLinkId) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Resource link ID is required'
      );
    }

    if (!launchData || !context) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'LTI launch context not found in session'
      );
    }

    const platform = await LtiPlatform.findByPk(context.platformId);
    if (!platform) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Platform Not Found');
    }

    // Extract AGS line items URL
    const agsClaim =
      launchData['https://purl.imsglobal.org/spec/lti-ags/claim/endpoint'];
    const lineItemsUrl = agsClaim?.lineitems;
    if (!lineItemsUrl) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'AGS Line Items service URL missing in launch claims'
      );
    }

    // Get access token with AGS scope for reading line items
    const scopes = ['https://purl.imsglobal.org/spec/lti-ags/scope/lineitem'];
    const token = await service.getAGSAccessToken(platform, scopes);

    // Call AGS with resource_link_id filter (spec uses snake_case)
    const params = { resource_link_id: resourceLinkId, limit: 10 };

    const response = await axios.get(lineItemsUrl, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/vnd.ims.lti-ags.v2.lineitem+json'
      },
      params
    });

    const data = response.data;
    const items = Array.isArray(data) ? data : data?.lineItems || [];
    const lineItem = items?.[0] || null;

    return {
      found: Boolean(lineItem),
      lineItem,
      metadata: {
        timestamp: new Date().toISOString(),
        source: 'brightspace_ags',
        filters: { resourceLinkId }
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Retrieve NRPS roster members using launch claims and optional filters
 */
export const parseRosterMembers = async (launchData, context, query = {}) => {
  if (!launchData || !context) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'LTI launch context not found in session'
    );
  }

  const platform = await LtiPlatform.findByPk(context.platformId);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform Not Found');
  }

  const nrpsClaim =
    launchData[
      'https://purl.imsglobal.org/spec/lti-nrps/claim/namesroleservice'
    ];
  const nrpsUrl = nrpsClaim?.context_memberships_url;
  if (!nrpsUrl) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'NRPS service URL missing in launch claims'
    );
  }

  const scopes = [
    'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
  ];
  const token = await new LtiService().getAGSAccessToken(platform, scopes);

  const params = {};
  if (query.rlid) {
    params.rlid = query.rlid;
  }
  if (query.role) {
    params.role = query.role;
  }
  if (query.limit) {
    params.limit = query.limit;
  }

  const response = await axios.get(nrpsUrl, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/vnd.ims.lti-nrps.v2.membershipcontainer+json'
    },
    params
  });

  return {
    success: true,
    ...response.data
  };
};

/**
 * Retrieve AGS line items from launch claims with optional filters
 */
export const parseLineItemsFromLaunch = async (
  launchData,
  context,
  query = {}
) => {
  if (!launchData || !context) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'LTI launch context not found in session'
    );
  }

  const platform = await LtiPlatform.findByPk(context.platformId);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform Not Found');
  }

  const agsClaim =
    launchData['https://purl.imsglobal.org/spec/lti-ags/claim/endpoint'];
  const lineItemsUrl = agsClaim?.lineitems;
  if (!lineItemsUrl) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'AGS Line Items service URL missing in launch claims'
    );
  }

  const scopes = ['https://purl.imsglobal.org/spec/lti-ags/scope/lineitem'];
  const token = await new LtiService().getAGSAccessToken(platform, scopes);

  const params = {};
  if (query.resource_link_id) {
    params.resource_link_id = query.resource_link_id;
  }
  if (query.tag) {
    params.tag = query.tag;
  }
  if (query.limit) {
    params.limit = parseInt(query.limit, 10);
  }

  const response = await axios.get(lineItemsUrl, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/vnd.ims.lti-ags.v2.lineitem+json'
    },
    params
  });

  const lineItems = Array.isArray(response.data)
    ? response.data
    : response.data?.lineItems || [];

  return {
    success: true,
    lineItems,
    totalItems: lineItems.length,
    metadata: {
      timestamp: new Date().toISOString(),
      source: 'brightspace_ags',
      filters: params
    }
  };
};

/**
 * Retrieve only students from NRPS
 */
export const parseStudentsFromNRPS = async (launchData, context) => {
  const roster = await parseRosterMembers(launchData, context);
  const members = Array.isArray(roster?.members)
    ? roster.members.filter(m =>
        (m.roles || []).some(r => /Learner|Student/i.test(r))
      )
    : [];
  return { success: true, members, total: members.length };
};

/**
 * Retrieve only instructors from NRPS
 */
export const parseInstructorsFromNRPS = async (launchData, context) => {
  const roster = await parseRosterMembers(launchData, context);
  const members = Array.isArray(roster?.members)
    ? roster.members.filter(m =>
        (m.roles || []).some(r =>
          /Instructor|TeachingAssistant|Faculty|Administrator/i.test(r)
        )
      )
    : [];
  return { success: true, members, total: members.length };
};

/**
 * Create a line item (local + remote) using session context
 */
export const createLineItemFromSession = async (
  launchData,
  context,
  resourceLink,
  { projectId, label, scoreMaximum = 100, tag }
) => {
  if (!launchData || !context || !resourceLink) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'No Launch Context');
  }
  const agsClaim =
    launchData['https://purl.imsglobal.org/spec/lti-ags/claim/endpoint'];
  const lineItemsUrl = agsClaim?.lineitems;
  if (!lineItemsUrl) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'LineItems URL missing in launch'
    );
  }
  const platform = await LtiPlatform.findByPk(context.platformId);
  let project = null;
  if (projectId) {
    project = await Project.findByPk(projectId);
  }

  let localLine = null;
  if (project) {
    localLine = await new LtiService().ensureLineItem({
      platform,
      context,
      resourceLink,
      project,
      scoreMaximum
    });
  }
  const remote = await new LtiService().createRemoteLineItem({
    platform,
    lineItemsUrl,
    label: label || project?.title,
    scoreMaximum,
    resourceLink,
    project,
    tag
  });

  return { success: true, remote, local: localLine };
};

/**
 * Post a score (grade passback) using submission id
 */
export const postScoreFromSubmission = async (
  launchData,
  context,
  resourceLink,
  { submission_id, scoreGiven, scoreMaximum, comment }
) => {
  if (!launchData || !context || !resourceLink) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'No Launch Context');
  }
  const submission = await (
    await import('../models/associations.js')
  ).Submission.findByPk(submission_id, {
    include: [
      { model: (await import('../models/associations.js')).User, as: 'user' },
      {
        model: (await import('../models/associations.js')).Project,
        as: 'project'
      }
    ]
  });
  if (!submission) {
    throw new ApiError(httpStatus.NOT_FOUND, 'SubmissionNotFound');
  }
  if (!submission.user?.lms_user_id) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'UserMissingLmsId');
  }
  const platform = await LtiPlatform.findByPk(context.platformId);
  const project = submission.project;
  const lineItem = await new LtiService().ensureLineItem({
    platform,
    context,
    resourceLink,
    project,
    scoreMaximum: scoreMaximum || 100
  });
  const result = await new LtiService().postScoreToLineItem({
    platform,
    lineItem,
    userLmsId: submission.user.lms_user_id,
    scoreGiven,
    scoreMaximum,
    comment
  });
  return { success: true, result };
};

/**
 * List all results for a line item (by projectId or lineItemId)
 */
export const listResultsForLineItem = async (
  context,
  { projectId, lineItemId }
) => {
  if (!context) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'No Launch Context');
  }
  const platform = await LtiPlatform.findByPk(context.platformId);
  let lineItem = null;
  if (lineItemId) {
    lineItem = await LtiLineItem.findOne({ where: { lineItemId } });
  } else if (projectId) {
    lineItem = await LtiLineItem.findOne({ where: { projectId } });
  }
  if (!lineItem) {
    throw new ApiError(httpStatus.NOT_FOUND, 'LineItemNotFound');
  }
  const results = await new LtiService().getResults({ platform, lineItem });
  return { success: true, results };
};

/**
 * Get current user result for line item
 */
export const getMyResultForLineItem = async (
  context,
  user,
  { projectId, lineItemId }
) => {
  if (!user?.lms_user_id) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'NoUserLmsId');
  }
  if (!context) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'No Launch Context');
  }
  const platform = await LtiPlatform.findByPk(context.platformId);
  let lineItem = null;
  if (lineItemId) {
    lineItem = await LtiLineItem.findOne({ where: { lineItemId } });
  } else if (projectId) {
    lineItem = await LtiLineItem.findOne({ where: { projectId } });
  }
  if (!lineItem) {
    throw new ApiError(httpStatus.NOT_FOUND, 'LineItemNotFound');
  }
  const result = await new LtiService().getResultForUser({
    platform,
    lineItem,
    userLmsId: user.lms_user_id
  });
  return { success: true, result };
};

/**
 * Get summarized LTI service/session status
 */
export const getLTIServiceStatusSummary = async session => {
  const launchData = session?.ltiLaunchData;
  const context = session?.ltiContext;
  const resourceLink = session?.ltiResourceLink;
  const user = session?.user;
  const scopes =
    launchData?.['https://purl.imsglobal.org/spec/lti-ags/claim/endpoint']
      ?.scope || [];
  return {
    success: true,
    hasLaunch: Boolean(launchData),
    hasContext: Boolean(context),
    hasResourceLink: Boolean(resourceLink),
    user: user
      ? { id: user.id, lms_user_id: user.lms_user_id, name: user.name }
      : null,
    context: context ? { id: context.id, contextId: context.contextId } : null,
    resourceLink: resourceLink
      ? {
          id: resourceLink.id,
          resourceLinkId: resourceLink.resourceLinkId,
          projectId: resourceLink.projectId
        }
      : null,
    agsScopes: scopes,
    timestamp: new Date().toISOString()
  };
};

/**
 * Build LTI platform registration configuration payload
 */
export const getLTIConfigurationData = toolUrl => ({
  title: 'BITS-DataScience Projects Platform',
  description:
    'Interactive data science projects and assignments platform for BITS Pilani',
  target_link_uri: `${toolUrl}/api/lti/oidc/callback`,
  oidc_initiation_url: `${toolUrl}/api/lti/login`,
  public_jwk_url: `${toolUrl}/api/lti/jwks`,
  scopes: [
    'openid',
    'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
    'https://purl.imsglobal.org/spec/lti-ags/scope/score',
    'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly',
    'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
  ],
  extensions: [
    {
      domain: new URL(toolUrl).hostname,
      tool_id: 'bits-datascience-platform',
      platform: 'bits.edu',
      settings: {
        text: 'BITS DataScience Platform',
        icon_url: `${toolUrl}/assets/bits-logo.png`,
        selection_width: 800,
        selection_height: 600
      },
      privacy_level: 'public'
    }
  ],
  custom_fields: {
    project_id: '$ResourceLink.id',
    context_id: '$Context.id',
    user_id: '$User.id'
  },
  claims: [
    'iss',
    'aud',
    'exp',
    'iat',
    'nonce',
    'https://purl.imsglobal.org/spec/lti/claim/deployment_id',
    'https://purl.imsglobal.org/spec/lti/claim/message_type',
    'https://purl.imsglobal.org/spec/lti/claim/version',
    'https://purl.imsglobal.org/spec/lti/claim/resource_link',
    'https://purl.imsglobal.org/spec/lti/claim/context',
    'https://purl.imsglobal.org/spec/lti/claim/tool_platform',
    'https://purl.imsglobal.org/spec/lti/claim/roles',
    'https://purl.imsglobal.org/spec/lti/claim/custom'
  ]
});

/**
 * Admin: fetch platforms list with deployments
 */
export const fetchPlatformsData = async () => {
  const platforms = await LtiPlatform.findAll({
    attributes: [
      'id',
      'platformId',
      'platformName',
      'clientId',
      'isActive',
      'createdAt',
      'updatedAt'
    ],
    include: [
      {
        model: LtiDeployment,
        as: 'deployments',
        attributes: ['id', 'deploymentId', 'deploymentName', 'isActive']
      }
    ]
  });
  return {
    success: true,
    platforms: platforms.map(platform => ({
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      clientId: platform.clientId,
      isActive: platform.isActive,
      deploymentCount: platform.deployments?.length || 0,
      deployments: platform.deployments || [],
      createdAt: platform.createdAt,
      updatedAt: platform.updatedAt
    }))
  };
};

/**
 * Admin: update platform by id
 */
export const updatePlatformById = async (id, payload) => {
  const platform = await LtiPlatform.findByPk(id);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform not found');
  }
  const {
    platformName,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    isActive,
    settings
  } = payload;
  const updateData = {};
  if (platformName) {
    updateData.platformName = platformName;
  }
  if (authLoginUrl) {
    updateData.authLoginUrl = authLoginUrl;
  }
  if (authTokenUrl) {
    updateData.authTokenUrl = authTokenUrl;
  }
  if (keySetUrl) {
    updateData.keySetUrl = keySetUrl;
  }
  if (isActive !== undefined) {
    updateData.isActive = isActive;
  }
  if (settings) {
    updateData.settings = { ...platform.settings, ...settings };
  }
  await platform.update(updateData);
  return {
    success: true,
    message: 'Platform updated successfully',
    platform: {
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      isActive: platform.isActive,
      updatedAt: platform.updatedAt
    }
  };
};

/**
 * Admin: delete platform by id
 */
export const deletePlatformById = async id => {
  const platform = await LtiPlatform.findByPk(id);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform not found');
  }
  const activeContexts = await LtiContext.count({
    where: { platformId: id, isActive: true }
  });
  if (activeContexts > 0) {
    throw new ApiError(
      httpStatus.CONFLICT,
      'Cannot delete platform with active contexts'
    );
  }
  await platform.destroy();
  return { success: true, message: 'Platform deleted successfully' };
};

/**
 * Admin: register a new platform
 */
export const registerPlatformData = async payload => {
  const {
    platformId,
    platformName,
    clientId,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    settings = {}
  } = payload;

  const existingPlatform = await LtiPlatform.findOne({ where: { platformId } });
  if (existingPlatform) {
    throw new ApiError(httpStatus.CONFLICT, 'Platform is already registered');
  }

  const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: { type: 'spki', format: 'pem' },
    privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
  });

  const platform = await LtiPlatform.create({
    platformId,
    platformName,
    clientId,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    privateKey,
    publicKey,
    keyId: `${platformId}-${Date.now()}`,
    settings
  });

  return {
    success: true,
    message: 'Platform registered successfully',
    platform: {
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      clientId: platform.clientId,
      isActive: platform.isActive
    }
  };
};

/**
 * Course contexts by courseId
 */
export const fetchCourseContextsData = async courseId => {
  const contexts = await LtiContext.findAll({
    where: { courseId, isActive: true },
    include: [
      {
        model: LtiPlatform,
        as: 'platform',
        attributes: ['id', 'platformName', 'platformId']
      },
      {
        model: LtiResourceLink,
        as: 'resourceLinks',
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'status']
          }
        ]
      }
    ]
  });

  return {
    success: true,
    contexts: contexts.map(context => ({
      id: context.id,
      contextId: context.contextId,
      contextTitle: context.contextTitle,
      contextLabel: context.contextLabel,
      platform: context.platform,
      resourceLinks:
        context.resourceLinks?.map(link => ({
          id: link.id,
          resourceLinkId: link.resourceLinkId,
          title: link.resourceLinkTitle,
          project: link.project
        })) || []
    }))
  };
};

/**
 * Handle Deep Linking request: process launch, build deep-linking JWT and auto-post HTML
 */
export const handleDeepLinkingRequest = async (idToken, state) => {
  const service = new LtiService();
  // Process launch to get context and claims
  const launchResult = await service.processLaunch(idToken, state);

  const deepLinkingClaim =
    launchResult.launchData[
      'https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings'
    ];

  if (!deepLinkingClaim) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Invalid deep linking request: missing deep_linking_settings claim'
    );
  }

  // Resolve platform for signing
  const platform = await LtiPlatform.findByPk(launchResult.context.platformId);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform Not Found');
  }

  // Fetch available projects (optionally scoped to course)
  const projects = await Project.findAll({
    where: { status: 'published' },
    include: [
      {
        model: Course,
        as: 'course',
        where: launchResult.context?.courseId
          ? { id: launchResult.context.courseId }
          : undefined,
        required: false
      }
    ]
  });

  const deepLinkingJWT = service.createDeepLinkingResponse(
    projects,
    deepLinkingClaim,
    platform
  );

  const returnUrl = deepLinkingClaim.deep_link_return_url;
  const html = `
      <html>
        <body>
          <form id="deepLinkingForm" action="${returnUrl}" method="post">
            <input type="hidden" name="JWT" value="${deepLinkingJWT}">
          </form>
          <script>
            document.getElementById('deepLinkingForm').submit();
          </script>
        </body>
      </html>
    `;

  return { success: true, returnUrl, deepLinkingJWT, html };
};

/**
 * Send a grade to LMS by submission and grade identifiers
 */
export const sendGradeToLMSUsingIds = async ({ submissionId, gradeId }) => {
  // Load submission and grade with required associations
  const {
    Submission,
    Grade,
    User: AssocUser,
    Project: AssocProject
  } = await import('../models/associations.js');

  const submission = await Submission.findByPk(submissionId, {
    include: [
      {
        model: AssocProject,
        as: 'project',
        include: [
          {
            model: LtiResourceLink,
            as: 'ltiResourceLinks'
          }
        ]
      },
      {
        model: AssocUser,
        as: 'user'
      }
    ]
  });

  const grade = await Grade.findByPk(gradeId);

  if (!submission || !grade) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Submission or grade not found');
  }

  const resourceLink = submission.project?.ltiResourceLinks?.[0];
  if (!resourceLink) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'This project is not linked to an LTI resource'
    );
  }

  // Ensure local line item exists (and attempt to map remote if present)
  const lineItem = await new LtiService().createLineItem(
    resourceLink,
    submission.project,
    Number(submission?.grade?.max_score || grade.max_score) || 100
  );

  // Resolve platform
  const platform = await LtiPlatform.findByPk(resourceLink.platformId);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform Not Found');
  }

  // Send grade using AGS helper (falls back to platform endpoint per existing impl)
  const scoreGiven = Number(grade.total_score);
  await new LtiService().sendGrade(
    submission.user?.lms_user_id,
    lineItem,
    scoreGiven,
    platform
  );

  return {
    success: true,
    message: 'Grade sent to LMS successfully',
    data: {
      submissionId,
      gradeId,
      scoreGiven,
      scoreMaximum: Number(grade.max_score)
    }
  };
};

/**
 * Link project to resource link and line item, update project fields, and PUT LMS lineitem.resourceId
 */
export const linkProjectWithLmsLineItem = async ({
  resourceLinkId,
  projectId,
  session
}) => {
  if (!resourceLinkId || !projectId) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'resourceLinkId and projectId are required'
    );
  }

  // Validate project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
  }

  // We need launchData/context to access AGS line items URL/token
  const launchData = session?.ltiLaunchData;
  const context = session?.ltiContext;
  if (!launchData || !context) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'LTI launch context not found in session'
    );
  }

  const platform = await LtiPlatform.findByPk(context.platformId);
  if (!platform) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Platform Not Found');
  }

  // Find the local LtiResourceLink row by LMS resourceLinkId string and platformId
  const localResourceLink = await LtiResourceLink.findOne({
    where: { resourceLinkId: resourceLinkId, platformId: platform.id }
  });
  if (!localResourceLink) {
    throw new ApiError(httpStatus.NOT_FOUND, 'LTI Resource Link not found');
  }

  // Update lti_resource_links.project_id
  await localResourceLink.update({ projectId });

  // Fetch remote line item for this resource link from LMS using existing helper
  const { found, lineItem: remoteLineItem } =
    await parseLineItemByResourceLinkId(launchData, context, resourceLinkId);

  if (!found || !remoteLineItem) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      'No AGS line item found for provided resourceLinkId'
    );
  }

  // Upsert local lti_line_items with projectId and identifiers
  const [localLineItem] = await LtiLineItem.findOrCreate({
    where: { lineItemId: remoteLineItem.id, platformId: platform.id },
    defaults: {
      platformId: platform.id,
      contextId: context.id,
      resourceLinkId: localResourceLink.id,
      projectId,
      lineItemId: remoteLineItem.id,
      scoreMaximum: Number(remoteLineItem.scoreMaximum) || 100,
      label: remoteLineItem.label || project.title,
      tag: remoteLineItem.tag || 'score',
      resourceId: remoteLineItem.resourceId || null,
      startDateTime: remoteLineItem.startDateTime
        ? new Date(remoteLineItem.startDateTime)
        : null,
      endDateTime: remoteLineItem.endDateTime
        ? new Date(remoteLineItem.endDateTime)
        : null
    }
  });
  // Ensure projectId set and sync fields
  await localLineItem.update({
    projectId,
    scoreMaximum:
      Number(remoteLineItem.scoreMaximum) || localLineItem.scoreMaximum,
    label: remoteLineItem.label || localLineItem.label,
    tag: remoteLineItem.tag || localLineItem.tag
  });

  // Update project.course_id and total_points using details from local lti_line_items table
  // Map course_id from the local line item's contextId -> LtiContext.courseId
  let courseId = project.course_id || null;
  if (localLineItem.contextId) {
    const lineItemContext = await LtiContext.findByPk(localLineItem.contextId);
    if (lineItemContext?.courseId) {
      courseId = lineItemContext.courseId;
    }
  }
  const totalPoints = Number(localLineItem.scoreMaximum) || null;
  await project.update({
    course_id: courseId,
    total_points: totalPoints
  });

  // PUT to LMS to update resourceId to projectId
  const service = new LtiService();
  const updatedRemote = await service.updateRemoteLineItemResourceId({
    platform,
    lineItem: localLineItem,
    resourceId: projectId,
    resourceLinkLmsId: resourceLinkId
  });

  // Sync local resourceId if remote returned value
  if (updatedRemote?.resourceId) {
    await localLineItem.update({ resourceId: updatedRemote.resourceId });
  } else {
    await localLineItem.update({ resourceId: projectId });
  }

  return {
    success: true,
    message: 'Project linked, line item updated, LMS resourceId set',
    data: {
      resourceLinkId,
      projectId,
      lineItemId: localLineItem.lineItemId,
      totalPoints,
      courseId
    }
  };
};
