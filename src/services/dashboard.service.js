import {
  Project,
  Course,
  CourseEnrollment,
  Submission,
  Grade,
  Checkpoint,
  CheckpointProgress,
  Activity,
  ProjectStatistics,
  User
} from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';

class DashboardService {
  /**
   * Calculate comprehensive dashboard statistics for an instructor
   */
  async calculateDashboardStats(courseId, instructorId) {
    try {
      const stats = {
        totalProjects: 0,
        activeProjects: 0,
        draftProjects: 0,
        archivedProjects: 0,
        totalStudents: 0,
        activeStudents: 0,
        totalSubmissions: 0,
        pendingGrades: 0,
        averageGrade: 0,
        recentActivity: [],
        courseOverview: [],
        projectProgress: []
      };

      // Build where clause for course filtering
      const courseWhere = courseId ? { id: courseId } : {};
      const instructorWhere = instructorId
        ? { instructor_id: instructorId }
        : {};

      // Get courses for the instructor
      const courses = await Course.findAll({
        where: { ...courseWhere, ...instructorWhere },
        attributes: ['id', 'name', 'code', 'term']
      });

      if (courses.length === 0) {
        return stats;
      }

      const courseIds = courses.map(c => c.id);

      // Calculate project statistics
      const projects = await Project.findAll({
        where: { course_id: { [Op.in]: courseIds } },
        include: [
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      stats.totalProjects = projects.length;
      stats.activeProjects = projects.filter(
        p => p.status === 'published'
      ).length;
      stats.draftProjects = projects.filter(p => p.status === 'draft').length;
      stats.archivedProjects = projects.filter(
        p => p.status === 'archived'
      ).length;

      // Calculate student statistics
      const enrollments = await CourseEnrollment.findAll({
        where: {
          course_id: { [Op.in]: courseIds }
        },
        include: [
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      stats.totalStudents = enrollments.length;
      stats.activeStudents = enrollments.filter(
        p => p.status === 'active'
      ).length; // All active enrollments

      // Calculate submission and grading statistics
      const projectIds = projects.map(p => p.id);

      if (projectIds.length > 0) {
        const submissions = await Submission.findAll({
          where: { project_id: { [Op.in]: projectIds } },
          include: [
            {
              model: Grade,
              as: 'grade',
              required: false
            }
          ]
        });

        stats.totalSubmissions = submissions.length;
        stats.pendingGrades = submissions.filter(s => !s.grade).length;

        const gradedSubmissions = submissions.filter(s => s.grade);
        if (gradedSubmissions.length > 0) {
          const totalGrade = gradedSubmissions.reduce(
            (sum, s) => sum + parseFloat(s.grade.percentage),
            0
          );
          stats.averageGrade = parseFloat(
            (totalGrade / gradedSubmissions.length).toFixed(2)
          );
        }

        // Calculate project progress
        stats.projectProgress =
          await this.calculateProjectProgressSummary(projectIds);
      }

      // Get course overview
      stats.courseOverview = await this.calculateCourseOverview(courseIds);

      // Get recent activity
      stats.recentActivity = await this.getRecentActivity(
        courseIds,
        instructorId,
        10
      );

      return stats;
    } catch (error) {
      logger.error('Error calculating dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Calculate project progress summary across all projects
   */
  async calculateProjectProgressSummary(projectIds) {
    try {
      const progressSummary = [];

      for (const projectId of projectIds) {
        const project = await Project.findByPk(projectId, {
          include: [
            {
              model: Course,
              as: 'course',
              attributes: ['id', 'name', 'code']
            }
          ]
        });

        if (!project) continue;

        const projectStats = await this.calculateProjectStats(projectId);

        progressSummary.push({
          projectId: project.id,
          title: project.title,
          status: project.status,
          startDate: project.start_date || project.created_at,
          dueDate: project.due_date,
          course: project.course,
          ...projectStats
        });
      }

      return progressSummary;
    } catch (error) {
      logger.error('Error calculating project progress summary:', error);
      return [];
    }
  }

  /**
   * Calculate comprehensive statistics for a specific project
   */
  async calculateProjectStats(projectId) {
    try {
      const stats = {
        totalStudents: 0,
        activeStudents: 0,
        submissionsCount: 0,
        gradedCount: 0,
        pendingGrades: 0,
        averageGrade: 0,
        progressPercentage: 0,
        checkpointCompletionRate: 0,
        lastActivity: null
      };

      // Get project with course
      const project = await Project.findByPk(projectId, {
        include: [
          {
            model: Course,
            as: 'course'
          }
        ]
      });

      // Get enrollments separately
      const enrollments = await CourseEnrollment.findAll({
        where: {
          course_id: project?.course_id,
          status: 'active'
        }
      });

      if (!project) return stats;

      // Calculate student counts
      stats.totalStudents = enrollments?.length || 0;
      stats.activeStudents = stats.totalStudents;

      // Calculate submission statistics
      const submissions = await Submission.findAll({
        where: { project_id: projectId },
        include: [
          {
            model: Grade,
            as: 'grade',
            required: false
          }
        ]
      });

      stats.submissionsCount = submissions.length;
      stats.gradedCount = submissions.filter(s => s.grade).length;
      stats.pendingGrades = stats.submissionsCount - stats.gradedCount;

      // Calculate average grade
      const gradedSubmissions = submissions.filter(s => s.grade);
      if (gradedSubmissions.length > 0) {
        const totalGrade = gradedSubmissions.reduce(
          (sum, s) => sum + parseFloat(s.grade.percentage),
          0
        );
        stats.averageGrade = parseFloat(
          (totalGrade / gradedSubmissions.length).toFixed(2)
        );
      }

      // Calculate progress percentage
      if (stats.totalStudents > 0) {
        stats.progressPercentage = parseFloat(
          ((stats.submissionsCount / stats.totalStudents) * 100).toFixed(2)
        );
      }

      // Calculate checkpoint completion rate
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId, status: 'published' }
      });

      if (checkpoints.length > 0 && stats.totalStudents > 0) {
        let totalCompletions = 0;

        for (const checkpoint of checkpoints) {
          const completedProgress = await CheckpointProgress.count({
            where: {
              checkpoint_id: checkpoint.id,
              status: 'completed'
            }
          });
          totalCompletions += completedProgress;
        }

        const totalPossibleCompletions =
          checkpoints.length * stats.totalStudents;
        if (totalPossibleCompletions > 0) {
          stats.checkpointCompletionRate = parseFloat(
            ((totalCompletions / totalPossibleCompletions) * 100).toFixed(2)
          );
        }
      }

      // Get last activity
      const lastActivity = await Activity.findOne({
        where: { project_id: projectId },
        order: [['created_at', 'DESC']]
      });

      if (lastActivity) {
        stats.lastActivity = lastActivity.created_at;
      }

      return stats;
    } catch (error) {
      logger.error('Error calculating project stats:', error);
      return {
        totalStudents: 0,
        activeStudents: 0,
        submissionsCount: 0,
        gradedCount: 0,
        pendingGrades: 0,
        averageGrade: 0,
        progressPercentage: 0,
        checkpointCompletionRate: 0,
        lastActivity: null
      };
    }
  }

  /**
   * Calculate course overview statistics
   */
  async calculateCourseOverview(courseIds) {
    try {
      const courseOverview = [];

      for (const courseId of courseIds) {
        const course = await Course.findByPk(courseId, {
          include: [
            {
              model: Project,
              as: 'projects',
              attributes: ['id', 'title', 'status', 'created_at', 'due_date']
            }
          ]
        });

        if (!course) continue;

        const courseStats = {
          courseId: course.id,
          name: course.name,
          code: course.code,
          term: course.term,
          totalProjects: course.projects?.length || 0,
          activeProjects:
            course.projects?.filter(p => p.status === 'published').length || 0,
          // totalStudents: course.enrollments?.length || 0,
          totalSubmissions: 0,
          averageGrade: 0
        };

        // Calculate submission and grade statistics for this course
        if (course.projects && course.projects.length > 0) {
          const projectIds = course.projects.map(p => p.id);

          const submissions = await Submission.findAll({
            where: { project_id: { [Op.in]: projectIds } },
            include: [
              {
                model: Grade,
                as: 'grade',
                required: false
              }
            ]
          });

          courseStats.totalSubmissions = submissions.length;

          const gradedSubmissions = submissions.filter(s => s.grade);
          if (gradedSubmissions.length > 0) {
            const totalGrade = gradedSubmissions.reduce(
              (sum, s) => sum + parseFloat(s.grade.percentage),
              0
            );
            courseStats.averageGrade = parseFloat(
              (totalGrade / gradedSubmissions.length).toFixed(2)
            );
          }
        }

        courseOverview.push(courseStats);
      }

      return courseOverview;
    } catch (error) {
      logger.error('Error calculating course overview:', error);
      return [];
    }
  }

  /**
   * Get recent activity for courses
   */
  async getRecentActivity(courseIds, instructorId, limit = 20) {
    try {
      const activities = await Activity.findAll({
        where: { course_id: { [Op.in]: courseIds } },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['created_at', 'DESC']],
        limit
      });

      return activities.map(activity => ({
        id: activity.id,
        type: activity.activity_type,
        description: activity.description,
        user: activity.user,
        project: activity.project,
        course: activity.course,
        timestamp: activity.created_at,
        metadata: activity.metadata
      }));
    } catch (error) {
      logger.error('Error getting recent activity:', error);
      return [];
    }
  }

  /**
   * Get detailed project statistics for instructor view
   */
  async getProjectDetailedStats(projectId, instructorId) {
    try {
      // Verify instructor has access to this project
      const project = await Project.findByPk(projectId, {
        include: [
          {
            model: Course,
            as: 'course',
            where: instructorId ? { instructor_id: instructorId } : {},
            required: true
          }
        ]
      });

      if (!project) {
        throw new Error('Project not found or access denied');
      }

      const projectStats = await this.calculateProjectStats(projectId);

      // Get checkpoint breakdown
      const checkpoints = await Checkpoint.findAll({
        where: { project_id: projectId, status: 'published' },
        order: [['checkpoint_number', 'ASC']],
        include: [
          {
            model: CheckpointProgress,
            as: 'progress',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ]
      });

      const checkpointBreakdown = checkpoints.map(checkpoint => {
        const progress = checkpoint.progress || [];
        const totalStudents = progress.length;
        const completed = progress.filter(p => p.status === 'completed').length;
        const inProgress = progress.filter(
          p => p.status === 'in_progress'
        ).length;
        const submitted = progress.filter(p => p.status === 'submitted').length;

        return {
          id: checkpoint.id,
          title: checkpoint.title,
          checkpointNumber: checkpoint.checkpoint_number,
          totalStudents,
          completed,
          inProgress,
          submitted,
          notStarted: totalStudents - completed - inProgress - submitted,
          completionRate:
            totalStudents > 0
              ? parseFloat(((completed / totalStudents) * 100).toFixed(2))
              : 0
        };
      });

      return {
        project: {
          id: project.id,
          title: project.title,
          status: project.status,
          startDate: project.start_date || project.created_at,
          dueDate: project.due_date,
          course: project.course
        },
        statistics: projectStats,
        checkpointBreakdown,
        recentSubmissions: await this.getRecentProjectSubmissions(projectId, 10)
      };
    } catch (error) {
      logger.error('Error getting project detailed stats:', error);
      throw error;
    }
  }

  /**
   * Get recent submissions for a project
   */
  async getRecentProjectSubmissions(projectId, limit = 10) {
    try {
      const submissions = await Submission.findAll({
        where: { project_id: projectId },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Grade,
            as: 'grade',
            required: false
          }
        ],
        order: [['submitted_at', 'DESC']],
        limit
      });

      return submissions.map(submission => ({
        id: submission.id,
        status: submission.status,
        submittedAt: submission.submitted_at,
        student: submission.user,
        grade: submission.grade
          ? {
              totalScore: submission.grade.total_score,
              maxScore: submission.grade.max_score,
              percentage: submission.grade.percentage,
              letterGrade: submission.grade.letter_grade
            }
          : null
      }));
    } catch (error) {
      logger.error('Error getting recent project submissions:', error);
      return [];
    }
  }

  /**
   * Update or create project statistics
   */
  async updateProjectStatistics(projectId) {
    try {
      const projectStats = await this.calculateProjectStats(projectId);

      const [stats, created] = await ProjectStatistics.findOrCreate({
        where: { project_id: projectId },
        defaults: {
          project_id: projectId,
          ...projectStats
        }
      });

      if (!created) {
        await stats.update(projectStats);
      }

      return stats;
    } catch (error) {
      logger.error('Error updating project statistics:', error);
      throw error;
    }
  }

  async getAdminDasboardDetails(req) {
    try {
      const stats = {
        totalUsers: 0,
        activeUsers: 0,
        currentMonthUsers: 0,
        totalCourses: 0,
        activeCourses: 0,
        totalProjects: 0,
        publishedProjects: 0,
        totalSubmissions: 0,
        submittedProjects: 0,
        topPerformingCourses: [],
        systemPerformance: {}
      };

      // Check permissions
      const hasPermission = req.userRoles.includes('admin');

      if (!hasPermission)
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Access denied to admin dashboard'
        );

      // Get current month start and end dates
      const now = new Date();
      const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

      // Get all users
      const totalUsers = await User.count();
      
      // Get active users
      const activeUsers = await User.count({
        where: { status: 'active' }
      });
      
      // Get users joined this month
      const currentMonthUsers = await User.count({
        where: {
          created_at: {
            [Op.between]: [currentMonthStart, currentMonthEnd]
          }
        }
      });

      stats.totalUsers = totalUsers;
      stats.activeUsers = activeUsers;
      stats.currentMonthUsers = currentMonthUsers;

      // Get course statistics
      const totalCourses = await Course.count();
      
      const activeCourses = await Course.count({
        where: { status: 'active' }
      });

      stats.totalCourses = totalCourses;
      stats.activeCourses = activeCourses;

      // Get project statistics
      const totalProjects = await Project.count();
      
      const publishedProjects = await Project.count({
        where: { status: 'published' }
      });

      // Get submission statistics
      const totalSubmissions = await Submission.count();
      
      const submittedProjects = await Submission.count({
        where: { status: 'submitted' }
      });

      stats.totalProjects = totalProjects;
      stats.publishedProjects = publishedProjects;
      stats.totalSubmissions = totalSubmissions;
      stats.submittedProjects = submittedProjects;

      // Get top performing courses
      stats.topPerformingCourses = await this.getTopPerformingCourses(5);

      // Get system performance metrics
      stats.systemPerformance = await this.getSystemPerformance();
      return stats;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get top performing courses based on average grades
   */
  async getTopPerformingCourses(limit = 5) {
    try {
      const courses = await Course.findAll({
        include: [
          {
            model: Project,
            as: 'projects',
            include: [
              {
                model: Submission,
                as: 'submissions',
                include: [
                  {
                    model: Grade,
                    as: 'grade',
                    required: true
                  }
                ]
              }
            ]
          }
        ]
      });

      const coursePerformance = courses.map(course => {
        const allGrades = [];
        let totalSubmissions = 0;
        let totalProjects = course.projects?.length || 0;

        course.projects?.forEach(project => {
          totalSubmissions += project.submissions?.length || 0;
          project.submissions?.forEach(submission => {
            if (submission.grade) {
              allGrades.push(parseFloat(submission.grade.percentage));
            }
          });
        });

        const averageGrade = allGrades.length > 0 
          ? allGrades.reduce((sum, grade) => sum + grade, 0) / allGrades.length 
          : 0;

        return {
          id: course.id,
          name: course.name,
          code: course.code,
          term: course.term,
          totalProjects,
          totalSubmissions,
          averageGrade: parseFloat(averageGrade.toFixed(2)),
          totalGradedSubmissions: allGrades.length
        };
      })
      .filter(course => course.averageGrade > 0)
      .sort((a, b) => b.averageGrade - a.averageGrade)
      .slice(0, limit);

      return coursePerformance;
    } catch (error) {
      logger.error('Error getting top performing courses:', error);
      return [];
    }
  }

  /**
   * Get system performance metrics
   */
  async getSystemPerformance() {
    try {
      // Server uptime (in seconds, convert to percentage)
      const uptimeSeconds = process.uptime();
      const uptimeHours = uptimeSeconds / 3600;
      const serverUptime = Math.min(99.9, (uptimeHours / (uptimeHours + 0.1)) * 100);

      // Active users (users active in last 24 hours)
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const activeUsers = await User.count({
        where: {
          updated_at: { [Op.gte]: last24Hours },
          status: 'active'
        }
      });

      // Storage usage (mock data - replace with actual storage API)
      const storageUsed = 750; // GB
      const storageTotal = 1000; // GB
      const storagePercentage = (storageUsed / storageTotal) * 100;

      // API response time (mock - replace with actual monitoring)
      const apiResponseTime = Math.floor(Math.random() * 50) + 120; // 120-170ms

      // Security alerts (mock - replace with actual security monitoring)
      const securityAlerts = Math.floor(Math.random() * 5); // 0-4 alerts

      return {
        serverUptime: parseFloat(serverUptime.toFixed(1)),
        activeUsers,
        storage: {
          used: storageUsed,
          total: storageTotal,
          percentage: parseFloat(storagePercentage.toFixed(1)),
          unit: 'GB'
        },
        apiResponseTime,
        securityAlerts,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Error getting system performance:', error);
      return {
        serverUptime: 0,
        activeUsers: 0,
        storage: { used: 0, total: 0, percentage: 0, unit: 'GB' },
        apiResponseTime: 0,
        securityAlerts: 0,
        lastUpdated: new Date()
      };
    }
  }
}

export default new DashboardService();
