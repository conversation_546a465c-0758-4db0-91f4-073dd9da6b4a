import httpStatus from 'http-status';
import path from 'path';
import {
    Course,
    CourseEnrollment,
    Grade,
    Project,
    Submission,
    User
} from '../models/associations.js';
import ApiError from '../utils/ApiError.utils.js';
import { LoggerError } from '../utils/helpers.utils.js';
import { default as S3Service } from './s3.service.js';



class SubmissionService {
//     /**
//      * Get submissions with filters and pagination
//      */
    static async getSubmissions(req) {
        try{

            const {
                page = 1,
                limit = 10,
                projectId,
                userId,
                status,
                sortBy = 'submitted_at',
                sortOrder = 'desc'
            } = req.query;

            const pageNum = parseInt(page, 10);
            const limitNum = parseInt(limit, 10);
            const offset = (pageNum - 1) * limitNum;

            const whereClause = {};
            if (projectId) whereClause.project_id = projectId;
            if (userId) whereClause.user_id = userId;
            if (status) whereClause.status = status;
            if (req.user.role === 'student') whereClause.user_id = req.user.id;

            const { count, rows } = await Submission.findAndCountAll({
                where: whereClause,
                include: this.getSubmissionIncludes(),
                limit: parseInt(limit),
                offset,
                order: [[sortBy, sortOrder.toUpperCase()]]
            });

            return {
                submissions: this.transformSubmissions(rows),
                pagination: {
                    currentPage: pageNum,
                    totalPages: Math.ceil(count / parseInt(limit)),
                    totalItems: count,
                    itemsPerPage: limitNum
                }
            };
        }catch (error) {
                console.error('Error fetching submissions:', error);
                throw new ApiError(
                httpStatus.INTERNAL_SERVER_ERROR,
                'Failed to fetch submissions'
                );
        }       
    }

//     /**
//      * Get submission by ID
//      */
    static async getSubmissionById(req) {

        try{
        const { id } = req.params;

        const submission = await Submission.findByPk(id, {
            include: this.getSubmissionIncludes()
        });

        if (!submission) {
            throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
        }

        const isAdmin = req.userRoles?.includes('admin');
        const isOwner = submission.user_id === req.user.id;
        const isInstructor = submission.project?.course?.instructor_id === req.user.id;
        const hasAccess = isAdmin || isOwner || isInstructor;

        if (!hasAccess) {
            throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to view this submission');
        }

        let notebookPresignedUrls = [];
        if (submission.metadata?.notebooks?.length) {
        try {
            notebookPresignedUrls = await Promise.all(
            submission.metadata.notebooks.map(async (nb) => {
                try {
                const url = await S3Service.generatePresignedDownloadUrl(nb.key, 3600);
                return { ...nb, presignedUrl: url };
                } catch (err) {
                LoggerError(
                    req,
                    `Error generating presigned URL for ${nb.key}`,
                    'getSubmissionById',
                    httpStatus.INTERNAL_SERVER_ERROR,
                    err
                );
                return { ...nb, presignedUrl: null };
                }
            })
            );
        } catch (error) {
            LoggerError(
            req,
            'Error generating presigned URL',
            'getSubmissionById',
            httpStatus.INTERNAL_SERVER_ERROR,
            error
            );
        }
        }

        return {
                ...this.transformSubmission(submission),
                notebookPresignedUrls
                };
            } catch (error) {
                // Ensure standardized error handling
                if (error instanceof ApiError) throw error;

                LoggerError(
                req,
                'Unexpected error in getSubmissionById',
                'getSubmissionById',
                httpStatus.INTERNAL_SERVER_ERROR,
                error
                );

                throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to retrieve submission');
            }   
    }

//     /**
//      * Create or update submission
//      */
    static async createOrUpdateSubmission(req) {
    try {
        const {
        projectId,
        submissionSummary,
        metadata = {},
        timeSpent,
        // max_attempts
        } = req.body;

        const userId = req.user.id;
        // const MAX_ATTEMPTS = max_attempts;

        // Validate required fields
        if (!projectId || !userId) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Project ID and User ID are required");
        }

        // Validate project access
        const project = await this.validateProjectAccess(projectId, userId);
        if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, "Project not found or access denied");
        }

        // Find existing submission
        let submission = await Submission.findOne({
        where: { project_id: projectId, user_id: userId }
        });

        // if(MAX_ATTEMPTS>0){
        //     if (submission && submission.attempts >= MAX_ATTEMPTS) {
        //         throw new ApiError(
        //             httpStatus.FORBIDDEN,
        //             `You have reached the maximum of ${MAX_ATTEMPTS} submission attempts`
        //         );
        //     }
        // }

        let uploadedFiles = [];

        // Handle uploaded file(s) if any
        if (req.files && req.files.length > 0) {
            uploadedFiles = await Promise.all(
            req.files.map(async (file) => {
            return await S3Service.uploadFile(file, "submission", userId, {
                courseId: project.course_id,
                projectId
            });
            })
        );

        // For notebook submissions, pick first file
        if (submission?.files?.length) {
        await Promise.all(
          submission.files.map(async (oldUrl) => {
            const oldKey = S3Service.extractKeyFromUrl(oldUrl);
            if (oldKey) await S3Service.deleteFile(oldKey);
          })
        );
      }
    }

        // Separate notebooks and other files
        const notebooks = [];
        const otherFiles = [];

        for (const f of uploadedFiles) {
            if (f.key.endsWith(".ipynb")) {
                notebooks.push({ url: f.url, key: f.key });
            } else {
                otherFiles.push({ url: f.url, key: f.key });
            }
        }


        // Prepare submission payload
        const submissionData = {
            project_id: projectId,
            user_id: userId,
            status: "in_progress",
            submission_summary: submissionSummary,
            metadata: {
                ...(submission?.metadata || {}),
                ...metadata,
                files: [
                ...(submission?.metadata?.files || []),
                ...otherFiles
                ],
                notebooks: [
                ...(submission?.metadata?.notebooks || []),
                ...notebooks
                ]
            },
            updated_at: new Date(),
            time_spent: timeSpent ?? submission?.time_spent ?? 0,
            current_progress: metadata?.progress ?? submission?.current_progress ?? 0,
        };

        // // Save all file URLs (instead of just one)
        // if (uploadedFiles.length > 0) {
        // submissionData.files = uploadedFiles.map((f) => f.key); // assumes S3Service.uploadFile returns { url, key }
        // }

        // Update or create submission
        if (submission) {
        submission = await submission.update({
            ...submissionData,
            attempts: (submission.attempts || 0) + 1,
        });
        } else {
        submission = await Submission.create({
            ...submissionData,
            attempts: 1,
            created_at: new Date(),
            updated_at: new Date()
        });
        }

        return {
            id: submission.id,
            status: submission.status,
            projectId: submission.project_id,
            notebooks: submission.metadata?.notebooks || [],
            files: submission.metadata?.files || [],
            submissionSummary: submission.submission_summary,
            metadata: submission.metadata,
            updatedAt: submission.updated_at,
            timeSpent: submission.time_spent,
            attempts: submission.attempts,
            currentProgress: submission.current_progress,
        };
    } catch (error) {
        console.error("createOrUpdateSubmission Error:", error);
        if (error instanceof ApiError) throw error;
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Error processing submission: ${error.message}`);
    }
}


//     /**
//      * Auto-save submission
//      */
    // static async autoSaveSubmission(req) {
    //     try{
    //         const {
    //             projectId,
    //             notebookContent,
    //             currentProgress = 0,
    //             executionOutput = null,
    //             metadata = {},
    //         } = req.body;

    //         const userId = req.user.id;

    //         if (!projectId || !userId) {
    //             throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID and user ID are required');
    //         }

    //         // Validate project access
    //         const project = await this.validateProjectAccess(projectId, userId);
    //         if (!project) {
    //         throw new ApiError(httpStatus.NOT_FOUND, 'Project not found or access denied');
    //         }

    //         // Create or fetch submission
    //         let submission = await Submission.findOne({
    //             where: { project_id: projectId,
    //                     user_id: userId }
    //         });

    //         if (!submission) {
    //             submission = await Submission.create({
    //                 project_id: projectId,
    //                 user_id: userId,
    //                 status: 'in_progress',
    //                 attempts: 1,
    //                 time_spent: 0,
    //                 metadata,
    //                 current_progress: currentProgress,
    //                 execution_output: executionOutput,
    //                 metadata: { ...metadata, autoSaveCount: 0 }
    //             });
    //         }

    //         // Upload notebookContent as file
    //         const notebookBuffer = Buffer.from(notebookContent, 'utf-8');
    //         const notebookFile = {
    //             buffer: notebookBuffer,
    //             originalname: `autosave_${Date.now()}.ipynb`,
    //             mimetype: 'application/x-ipynb+json',
    //             size: notebookBuffer.length
    //         };

    //         const notebookResult = await S3Service.uploadFile(notebookFile, 'notebook-submission', userId, {
    //             courseId: project.course_id,
    //             projectId
    //         });

    //         // Optionally upload any additional files provided via multipart/form-data
    //         const extraFiles = [];
    //         if (Array.isArray(req.files)) {
    //             extraFiles.push(...req.files);
    //         } else if (req.files && typeof req.files === 'object') {
    //             Object.values(req.files).forEach(arr => { if (Array.isArray(arr)) extraFiles.push(...arr); });
    //         } else if (req.file) {
    //                 extraFiles.push(req.file);
    //         }

    //         // Upload notebook to S3
    //         const uploadedExtras = [];
    //         for (const f of extraFiles) {
    //         try {
    //             const type = this.inferS3TypeForFile(f, {});
    //             const res = await S3Service.uploadFile(f, type, req.user.id, { courseId: project.course_id, projectId });
    //             uploadedExtras.push({
    //             type,
    //             originalName: f.originalname,
    //             url: res.url,
    //             key: res.key,
    //             size: res.size,
    //             contentType: res.contentType,
    //             etag: res.etag
    //             });
    //         } catch (err) {
    //             logger.warn('Failed to upload extra autosave file', { file: f.originalname, err });
    //             uploadedExtras.push({ error: `Failed to upload ${f.originalname}`, originalName: f.originalname });
    //         }
    //         }
            
    //         if (submission.notebook_s3_url) {
    //             try {
    //                 const oldKey = S3Service.extractKeyFromUrl(submission.notebook_s3_url);
    //                 if (oldKey) {
    //                     await S3Service.deleteFile(oldKey);
    //                 }
    //             } catch (err) {

    //                 logger.warn('Failed to delete previous autosave notebook', { oldUrl: submission.notebook_s3_url, err });
    //             }
    //         }

    //         // Update metadata.attachments
    //         const baseMetadata = { ...(submission.metadata || {}) };
    //         const attachments = Array.isArray(baseMetadata.attachments) ? [...baseMetadata.attachments] : [];

    //         attachments.push({
    //             type: 'autosave-notebook',
    //             fileName: notebookResult.key?.split('/').pop(),
    //             url: notebookResult.url,
    //             key: notebookResult.key,
    //             uploadedAt: new Date()
    //         });

    //         uploadedExtras.forEach(e => {
    //             if (e && e.url) {
    //                 attachments.push({
    //                 type: e.type,
    //                 fileName: e.originalName,
    //                 url: e.url,
    //                 key: e.key,
    //                 uploadedAt: new Date()
    //                 });
    //             }
    //         });

    //         const timeSpent = this.calculateTimeSpent(submission);
    //         const updatedSubmission = await submission.update({
    //             notebook_s3_url: submission.notebook_s3_url,
    //             time_spent: timeSpent,
    //             current_progress: currentProgress,
    //             execution_output: executionOutput,
    //             metadata: this.buildAutoSaveMetadata(submission, { ...(metadata || {}), attachments })
    //         });

    //         return {
    //             lastSaved: new Date(),
    //             submissionId: updatedSubmission.id,
    //             timeSpent: updatedSubmission.time_spent,
    //             currentProgress,
    //             autoSaveCount: updatedSubmission.metadata.autoSaveCount,
    //             notebookS3Url: updatedSubmission.notebook_s3_url,
    //             uploadedExtras
    //         };
    //     } catch (error) {
    //         logger.error('autoSaveSubmission error', error);
    //         if (error instanceof ApiError) throw error;
    //         throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Failed to autosave submission: ${error.message}`);
    //     }
    
    // }

//   /**
//    * Submit final assignment
//    */

  static async submitAssignment(req) {
        const { id } = req.params;

        const submission = await Submission.findByPk(id, {
            include: [{
                model: Project,
                as: 'project',
                include: [{ model: Course, as: 'course' }]
            }]
        });

        this.validateSubmission(submission, req.user.id);

        const timeSpent = this.calculateTimeSpent(submission);
        const attempts = (submission.attempts || 0) + 1;
        const finalMetadata = {
            ...(submission.metadata || {}),
            ...this.buildFinalSubmissionMetadata(submission)
        };

        const updatedSubmission = await submission.update({
            status: 'submitted',
            submitted_at: new Date(),
            time_spent: timeSpent,
            attempts,
            metadata:finalMetadata
        });

        const result = {
            id: updatedSubmission.id,
            status: updatedSubmission.status,
            submittedAt: updatedSubmission.submitted_at,
            timeSpent: updatedSubmission.time_spent,
            attempts: updatedSubmission.attempts,
            projectTitle: submission.project?.title,
            courseName: submission.project?.course?.name,
            notebooks: updatedSubmission.metadata?.notebooks || [],
            files: updatedSubmission.metadata?.files || []
        };

        return result;
    }



    static async downloadSubmissionNotebook(req) {
        try {
            const { id } = req.params;

            const submission = await Submission.findByPk(id, {
                include: [
                    { model: Project, 
                        as: 'project', 
                        include: [{ model: Course, as: 'course' }] 
                    },
                    { model: User, 
                        as: 'user', 
                        attributes: ['id', 'name', 'email'] 
                    }
                ]
            });

            if (!submission) {
                throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
            }

            const isAdmin = req.userRoles?.includes('admin');
            const isOwner = submission.user_id === req.user.id;
            const isInstructor = submission.project?.course?.instructor_id === req.user.id;
            const hasAccess = isAdmin || isOwner || isInstructor;

            if (!hasAccess) {
                throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to download this submission');
            }

            const notebooks = submission.metadata?.notebooks || [];
            if (!notebooks.length) {
                throw new ApiError(httpStatus.NOT_FOUND, 'No notebook file(s) found for this submission');
            }

            const selectedNotebook = notebooks[0];
            
            //Getting the download URL
            const presignedUrl = await S3Service.generatePresignedDownloadUrl(
                selectedNotebook.key,
                3600
            );

            //Sanitize filename for download
            const sanitizedName = submission.user.name.replace(/[^a-zA-Z0-9]/g, '_');
            const sanitizedTitle = submission.project.title.replace(/[^a-zA-Z0-9]/g, '_');
            const fileName = `${sanitizedName}_${sanitizedTitle}_${submission.id}.ipynb`;

            return {
                downloadUrl: presignedUrl,
                fileName,
                expiresIn: 3600,
                submission: {
                    id: submission.id,
                    status: submission.status,
                    projectTitle: submission.project.title,
                    courseName: submission.project.course.name,
                    studentName: submission.user.name,
                    submittedAt: submission.submitted_at
                }
                
            };
        }catch (error) {
            if (error instanceof ApiError) throw error;

            LoggerError(
                req,
                'Unexpected error in downloadSubmissionNotebook',
                'downloadSubmissionNotebook',
                httpStatus.INTERNAL_SERVER_ERROR,
                error
            );

            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to download submission notebook');
        }    
    }

//   /**
//    * Get submission statistics
//    */
  static async getSubmissionStatistics(req) {
        const { projectId } = req.params;

        const project = await Project.findByPk(projectId, {
            include: [{ model: Course, as: 'course' }]
        });

        if (!project) {
            throw new Error('Project not found');
        }

        const hasPermission = req.userRoles?.includes('admin') ||
            project.course.instructor_id === req.user.id;

        if (!hasPermission) {
            throw new Error('You do not have permission to view submission statistics');
        }

        return await this.calculateStatistics(projectId, project);
    }


//     // Private helper methods
    static getSubmissionIncludes() {
        return [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email', 'profile_picture']
            },
            {
                model: Project,
                as: 'project',
                attributes: ['id', 'title', 'due_date', 'difficulty_level'],
                include: [{
                    model: Course,
                    as: 'course',
                    attributes: ['id', 'name', 'code']
                }]
            },
            {
                model: Grade,
                as: 'grade',
                include: [{
                    model: User,
                    as: 'evaluator',
                    attributes: ['id', 'name', 'email']
                }]
            }
        ];
    }
//     // Inside SubmissionService class

    static transformSubmissions(submissions) {
        return submissions.map(submission => this.transformSubmission(submission));
    }

    static transformSubmission(submission) {
        if (!submission) return null;
        return {
            id: submission.id,
            status: submission.status,
            submittedAt: submission.submitted_at,
            executionTime: submission.execution_time,
            timeSpent: submission.time_spent,
            attempts: submission.attempts,
            submissionSummary: submission.submission_summary,
            notebooks: submission.metadata?.notebooks || [],
            files: submission.metadata?.files || [],
            metadata: submission.metadata,
            user: submission.user ? {
                id: submission.user.id,
                name: submission.user.name,
                email: submission.user.email,
                profilePicture: submission.user.profile_picture
            } : null,
            project: submission.project ? {
                id: submission.project.id,
                title: submission.project.title,
                dueDate: submission.project.due_date,
                difficultyLevel: submission.project.difficulty_level,
                course: submission.project.course ? {
                    id: submission.project.course.id,
                    name: submission.project.course.name,
                    code: submission.project.course.code
                } : null
            } : null,
            grade: submission.grade ? {
                id: submission.grade.id,
                totalScore: submission.grade.total_score,
                maxScore: submission.grade.max_score,
                percentage: submission.grade.percentage,
                letterGrade: submission.grade.letter_grade,
                feedback: submission.grade.feedback,
                // evaluator: submission.grade.evaluator ? {
                //     id: submission.grade.evaluator.id,
                //     name: submission.grade.evaluator.name,
                //     email: submission.grade.evaluator.email
                // } : null,
                gradedAt: submission.grade.graded_at
            } : null,
            createdAt: submission.created_at,
            updatedAt: submission.updated_at
        };
    }
    static async validateProjectAccess(projectId, userId) {
        const project = await Project.findByPk(projectId, {
            include: [{
                model: Course, 
                as: 'course'
            }]
        });

        if (!project) throw new Error('Project not found');
        if (project.status !== 'published') throw new Error('Project is not published');

        // Check enrollment separately
        const enrollment = await CourseEnrollment.findOne({
            where: { 
                user_id: userId, 
                course_id: project.course_id 
            }
        });

        if (!enrollment) throw new Error('User is not enrolled in this course');

        if (project.due_date && new Date() > new Date(project.due_date)) {
            throw new Error('Submission deadline has passed');
        }

        return project;
    }



    static inferS3TypeForFile(file, options = {}) {
    // options can carry explicit fileType (e.g., req.body.fileType)
    const explicit = options.fileType || options.fileTypePerFile;
    if (explicit) return explicit; // trust client-provided type when present

    const original = file.originalname || '';
    const ext = path.extname(original).toLowerCase();
    const mime = (file.mimetype || '').toLowerCase();
    const field = file.fieldname || '';

    // Heuristics
    if (ext === '.ipynb' || mime.includes('ipynb') || field.includes('notebook')) {
        return 'notebook-submission';
    }

    if (['.csv', '.json', '.zip', '.parquet'].includes(ext) || field.includes('dataset') || mime.includes('csv') || mime.includes('parquet')) {
        return 'project-dataset';
    }

    if (mime.startsWith('image/') || ['.png', '.jpg', '.jpeg', '.gif'].includes(ext)) {
        return 'profile-picture';
    }

    if (field.includes('template')) {
        return 'project-template';
    }

    if (field.includes('material')) {
        return 'course-material';
    }

    // fallback -> general submission path
    return 'submission';
    }


    static calculateTimeSpent(submission) {
        const lastSaved = submission.metadata?.lastAutoSave ? new Date(submission.metadata.lastAutoSave) : null;
        const currentTimeSpent = lastSaved ? (Date.now() - lastSaved.getTime()) / 1000 : 0;
        return Math.round(submission.time_spent + currentTimeSpent);
    }

    static buildAutoSaveMetadata(submission, newMetadata) {
        return {
            ...submission.metadata,
            ...newMetadata,
            lastAutoSave: new Date(),
            autoSaveCount: (submission.metadata?.autoSaveCount || 0) + 1,
            lastModified: new Date()
        };
    }

    static validateSubmission(submission, userId) {
        if (!submission) throw new Error('Submission not found');
        if (submission.user_id !== userId) throw new Error('Access denied');
        if (submission.status === 'submitted') throw new Error('Assignment has already been submitted');
        if (!submission.notebook_s3_url) throw new Error('Please upload your notebook before submitting');
        if (submission.project.due_date && new Date() > new Date(submission.project.due_date)) {
            throw new Error('Submission deadline has passed');
        }
    }

    static buildFinalSubmissionMetadata(submission) {
        return {
            ...(submission.metadata || {}),

            // ✅ preserve uploaded files and notebooks
            files: submission.metadata?.files || [],
            notebooks: submission.metadata?.notebooks || [],
            finalSubmission: true,
            submissionComplete: true,
            completedAt: new Date(),
            lastModified: new Date(),
            submissionHistory: [
                ...(submission.metadata?.submissionHistory || []),
                {
                    timestamp: new Date(),
                    status: 'submitted',
                    timeSpent: submission.time_spent,
                    attempts: submission.attempts
                }
            ]
        };
    }

    // static async calculateStatistics(projectId, project) {
    //     const totalEnrolled = await CourseEnrollment.count({
    //         where: {
    //             course_id: project.course_id,
    //             role_in_course: 'student',
    //             enrollment_status: 'active'
    //         }
    //     });

    //     const submissionCounts = await Submission.findAll({
    //         where: { project_id: projectId },
    //         attributes: [
    //             'status',
    //             [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    //         ],
    //         group: ['status']
    //     });

    //     const totalSubmissions = await Submission.count({
    //         where: { project_id: projectId }
    //     });

    //     const onTimeSubmissions = await Submission.count({
    //         where: {
    //             project_id: projectId,
    //             status: 'submitted',
    //             submitted_at: {
    //                 [Op.lte]: project.due_date
    //             }
    //         }
    //     });

    //     const averageMetrics = await Submission.findOne({
    //         where: { project_id: projectId },
    //         attributes: [
    //             [sequelize.fn('AVG', sequelize.col('execution_time')), 'avg_execution_time'],
    //             [sequelize.fn('AVG', sequelize.col('time_spent')), 'avg_time_spent'],
    //             [sequelize.fn('AVG', sequelize.col('attempts')), 'avg_attempts']
    //         ]
    //     });

    //     const gradeStats = await Grade.findAll({
    //         include: [{
    //             model: Submission,
    //             where: { project_id: projectId }
    //         }],
    //         attributes: [
    //             'letter_grade',
    //             [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
    //             [sequelize.fn('AVG', sequelize.col('percentage')), 'average']
    //         ],
    //         group: ['letter_grade']
    //     });

    //     return {
    //         overview: {
    //             totalEnrolled,
    //             totalSubmissions,
    //             submissionRate: totalEnrolled > 0 ?
    //                 (totalSubmissions / totalEnrolled * 100).toFixed(1) : 0,
    //             completionRate: totalEnrolled > 0 ?
    //                 (onTimeSubmissions / totalEnrolled * 100).toFixed(1) : 0
    //         },
    //         submissionStatus: submissionCounts.map(item => ({
    //             status: item.status,
    //             count: parseInt(item.getDataValue('count')),
    //             percentage: totalSubmissions > 0 ?
    //                 (parseInt(item.getDataValue('count')) / totalSubmissions * 100).toFixed(1) : 0
    //         })),
    //         performance: {
    //             averageExecutionTime: averageMetrics ?
    //                 parseFloat(averageMetrics.getDataValue('avg_execution_time')).toFixed(2) : null,
    //             averageTimeSpent: averageMetrics ?
    //                 parseFloat(averageMetrics.getDataValue('avg_time_spent')).toFixed(2) : null,
    //             averageAttempts: averageMetrics ?
    //                 parseFloat(averageMetrics.getDataValue('avg_attempts')).toFixed(1) : null
    //         },
    //         grades: {
    //             distribution: gradeStats.map(grade => ({
    //                 grade: grade.letter_grade,
    //                 count: parseInt(grade.getDataValue('count')),
    //                 average: parseFloat(grade.getDataValue('average')).toFixed(1)
    //             })),
    //             averageScore: gradeStats.length > 0 ?
    //                 (gradeStats.reduce((acc, curr) =>
    //                     acc + parseFloat(curr.getDataValue('average')), 0) / gradeStats.length
    //                 ).toFixed(1) : null
    //         },
    //         metadata: {
    //             projectTitle: project.title,
    //             courseName: project.course.name,
    //             dueDate: project.due_date,
    //             generatedAt: new Date()
    //         }
    //     };
    // }

    static async calculateStatistics(projectId, project) {
        const totalEnrolled = await CourseEnrollment.count({
            where: {
                course_id: project.course_id,
                role_in_course: 'student',
                enrollment_status: 'active'
            }
        });

        const totalSubmissions = await Submission.count({
            where: { project_id: projectId }
        });

        return {
            overview: {
                totalEnrolled,
                totalSubmissions,
                submissionRate: totalEnrolled > 0 ? 
                    (totalSubmissions / totalEnrolled * 100).toFixed(1) : 0
            },
            metadata: {
                projectTitle: project.title,
                courseName: project.course.name,
                dueDate: project.due_date,
                generatedAt: new Date()
            }
        };
    }

}


export default SubmissionService;