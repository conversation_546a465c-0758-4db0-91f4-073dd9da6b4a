import {
  Project,
  ProjectTemplate,
  ProjectAssignment,
  User,
  Course,
  Rubric,
  Submission,
  CourseEnrollment,
  Grade,
  Checkpoint
} from '../models/associations.js';
import { Op, Sequelize } from 'sequelize';
import logger from '../config/logger.config.js';
import courseRoleService from './courseRole.service.js';
import { checkAccessRole, getSortOrderList } from '../utils/helpers.utils.js';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';
import enhancedProjectUtils from '../utils/enhancedProject.utils.js';
import s3Service from './s3.service.js';
class EnhancedProjectService {
  /**
   * Create a new project with enhanced fields
   */
  async createProject(projectData, assignments = []) {
    try {
      const project = await Project.create(projectData);

      // Create project assignments if provided
      if (assignments.length > 0) {
        const assignmentPromises = assignments.map(assignment =>
          ProjectAssignment.create({
            ...assignment,
            project_id: project.id,
            assigned_by: projectData.created_by
          })
        );
        await Promise.all(assignmentPromises);
      }

      // Create template if requested
      if (projectData.is_template) {
        await this.createProjectTemplate(
          project.id,
          projectData,
          projectData.created_by
        );
      }

      logger.info(
        `Enhanced project created: ${project.title} by user ${projectData.created_by}`
      );

      return project;
    } catch (error) {
      logger.error('Error creating enhanced project:', error);
      throw error;
    }
  }

  /**
   * Create a new project with enhanced fields
   */
  async getEnhancedProject(req) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        courseId,
        status,
        difficultyLevel,
        sortBy,
        sortOrder
      } = req.query;

      const offset = (parseInt(page, 10) - 1) * parseInt(limit, 10);
      const isAdmin = req.userRoles?.includes('admin');
      const isStudent = req.userRoles?.includes('student') && !isAdmin;

      console.log('isAdmin:', isAdmin, 'isStudent:', isStudent);

      // ---------- base filters (search, course, difficulty) ----------
      const baseWhere = {};

      if (search) {
        baseWhere[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ];
      }

      if (courseId) baseWhere.course_id = courseId;
      if (difficultyLevel) baseWhere.difficulty_level = difficultyLevel;

      // ---------- include clause (course, instructor, creator) ----------
      const includeClause = [
        {
          model: Course,
          as: 'course',
          attributes: [
            'id',
            'name',
            'code',
            'term',
            'description',
            'instructor_id'
          ]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }
      ];

      // ---------- role-based visibility ----------
      let finalWhere = { ...baseWhere };

      if (isStudent) {
        // Students: only published & must be enrolled
        finalWhere.status = 'published';

        includeClause[0].include = [
          {
            model: CourseEnrollment,
            as: 'enrollments',
            where: { user_id: req.user.id },
            required: false
          }
        ];
      } else {
        // Non-students (admin, instructor, TA)

        if (status) {
          if (status === 'published') {
            finalWhere.status = 'published';
          } else if (status === 'draft') {
            // Only my drafts
            finalWhere = {
              ...baseWhere,
              status: 'draft',
              created_by: req.user.id
            };
          } else if (status === 'archived') {
            // archived (admins see all; non-admins limited by enrollment include below)
            finalWhere.status = 'archived';
          } else {
            // fallback if some unknown status
            finalWhere = { ...baseWhere, status };
          }
        } else {
          // default: published OR my drafts
          finalWhere[Op.or] = [
            { status: 'published' },
            { [Op.and]: [{ status: 'draft' }, { created_by: req.user.id }] }
          ];
        }

        // Non-admins still need enrollment check
        if (!isAdmin) {
          includeClause[0].include = [
            {
              model: CourseEnrollment,
              as: 'enrollments',
              where: { user_id: req.user.id },
              required: false
            }
          ];
        }
      }

      // ---------- query ----------
      const safeSortOrder = await getSortOrderList(sortBy, sortOrder, [
        ['created_at', 'DESC']
      ]);

      const { count, rows: projects } = await Project.findAndCountAll({
        where: finalWhere,
        include: includeClause,
        limit: parseInt(limit),
        offset,
        order: safeSortOrder,
        distinct: true
      });

      // ---------- bulk fetch instructor user objects for all projects ----------
      let instructorsById = {};
      try {
        const allInstructorIds = Array.from(
          new Set(
            projects
              .flatMap(p =>
                Array.isArray(p.instructor_id) ? p.instructor_id : []
              )
              .filter(Boolean)
          )
        );
        if (allInstructorIds.length) {
          const instructorUsers = await User.findAll({
            where: { id: { [Op.in]: allInstructorIds } },
            attributes: ['id', 'name', 'email']
          });
          instructorsById = instructorUsers.reduce((acc, u) => {
            acc[u.id] = u;
            return acc;
          }, {});
        }
      } catch (e) {
        logger.warn('Failed to prefetch instructor users', e);
      }

      let transformedProjects;

      // Transform project data for response
      if (projects.length > 0) {
        transformedProjects = await Promise.all(
          projects.map(async project => {
            // Get submission count for this project
            const submissionCount = await Submission.count({
              where: { project_id: project.id },
              // Ensure soft-deletes don't inject deleted_at filter on a table without that column
              paranoid: false
            });

            // Get user's submission if exists
            let userSubmission = null;
            if (req.user && !req.userRoles.includes('admin')) {
              userSubmission = await Submission.findOne({
                where: { project_id: project.id, user_id: req.user.id },
                attributes: ['id', 'status', 'submitted_at'],
                paranoid: false
              });
            }

            return {
              id: project.id,
              projectId: project.project_code,
              title: project.title,
              description: project.description,
              status: project.status,
              difficultyLevel: project.difficulty_level,
              startDate: project.start_date,
              dueDate: project.due_date,
              // estimatedHours: project.estimated_hours,
              dueDate: project.due_date,
              // instructions: project.instructions,
              submissionCount,
              userSubmission,
              instructors: Array.isArray(project.instructor_id)
                ? project.instructor_id
                    .map(iid => instructorsById[iid])
                    .filter(Boolean)
                    .map(u => ({ id: u.id, name: u.name, email: u.email }))
                : [],
              course: {
                id: project.course?.id,
                name: project.course?.name,
                code: project.course?.code,
                term: project.course?.term,
                description: project.course?.description,
                instructor: project.course?.instructor
              }
              /*  creator: {
             id: project.creator.id,
             name: project.creator.name,
             email: project.creator.email
           },
           createdAt: project.created_at,
           updatedAt: project.updated_at */
            };
          })
        );
      }

      return {
        projects: transformedProjects,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * update a project with enhanced fields
   */
  async updateProject(projectData, assignments = []) {
    try {
      const project = await Project.findByPk(projectData.id, {
        include: [
          {
            model: Course,
            as: 'course'
          }
        ]
      });

      if (!project)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');

      await project.update(projectData);

      logger.info(
        `Project updated: ${projectData.title} by user ${projectData.created_by}`
      );

      // Create project assignments if provided
      /*  if (assignments.length > 0) {
         const assignmentPromises = assignments.map(assignment => 
           ProjectAssignment.create({
             ...assignment,
             project_id: project.id,
             assigned_by: projectData.created_by
           })
         );
         await Promise.all(assignmentPromises);
       }
       
       // Create template if requested
       if (projectData.is_template) {
         await this.createProjectTemplate(projectData.id, projectData, projectData.created_by);
       } */

      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create project template
   */
  async createProjectTemplate(projectId, templateData, createdBy) {
    try {
      const template = await ProjectTemplate.create({
        project_id: projectId,
        template_name: templateData.title,
        template_description: templateData.description,
        category: templateData.template_category || 'general',
        subcategory: templateData.template_subcategory,
        difficulty_level: templateData.difficulty_level,
        estimated_hours: templateData.estimated_hours,
        total_points: templateData.total_points,
        learning_objectives: templateData.learning_objectives,
        prerequisites: templateData.prerequisites,
        skills_covered: templateData.skills_covered,
        technologies_used: templateData.technologies_used,
        tags: templateData.tags,
        created_by: createdBy
      });

      logger.info(`Project template created: ${template.template_name}`);
      return template;
    } catch (error) {
      logger.error('Error creating project template:', error);
      throw error;
    }
  }

  /**
   * Update project template
   */
  async updateProjectTemplate(templateId, updateData, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      // Check permissions
      if (template.created_by !== userId) {
        throw new Error('Permission denied: Only creator can update template');
      }

      await template.update(updateData);

      logger.info(
        `Project template updated: ${template.template_name} by user ${userId}`
      );
      return template;
    } catch (error) {
      logger.error('Error updating project template:', error);
      throw error;
    }
  }

  /**
   * Get project templates with filtering
   */
  async getProjectTemplates(options = {}) {
    try {
      const {
        category,
        subcategory,
        difficultyLevel,
        isFeatured,
        isPublic = true,
        page = 1,
        limit = 20,
        search
      } = options;

      const whereClause = {};

      if (category) {
        whereClause.category = category;
      }

      if (subcategory) {
        whereClause.subcategory = subcategory;
      }

      if (difficultyLevel) {
        whereClause.difficulty_level = difficultyLevel;
      }

      if (isFeatured !== undefined) {
        whereClause.is_featured = isFeatured;
      }

      if (isPublic !== undefined) {
        whereClause.is_public = isPublic;
      }

      const offset = (page - 1) * limit;

      const { count, rows: templates } = await ProjectTemplate.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status'],
            where: search
              ? {
                  [Op.or]: [
                    { title: { [Op.iLike]: `%${search}%` } },
                    { description: { [Op.iLike]: `%${search}%` } }
                  ]
                }
              : undefined
          },
          {
            model: User,
            as: 'createdBy',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [
          ['is_featured', 'DESC'],
          ['rating', 'DESC'],
          ['usage_count', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit,
        offset
      });

      return {
        templates,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting project templates:', error);
      throw error;
    }
  }

  /**
   * Assign users to project
   */
  async assignUsersToProject(projectId, assignments, assignedBy) {
    try {
      const assignmentPromises = assignments.map(assignment =>
        ProjectAssignment.create({
          project_id: projectId,
          user_id: assignment.userId,
          role: assignment.role,
          assignment_type: assignment.assignmentType || 'primary',
          permissions: assignment.permissions || {},
          assigned_by: assignedBy,
          start_date: assignment.startDate,
          end_date: assignment.endDate,
          notes: assignment.notes
        })
      );

      const createdAssignments = await Promise.all(assignmentPromises);

      logger.info(
        `Users assigned to project ${projectId}: ${assignments.length} assignments`
      );
      return createdAssignments;
    } catch (error) {
      logger.error('Error assigning users to project:', error);
      throw error;
    }
  }

  /**
   * Remove user assignment from project
   */
  async removeUserAssignment(projectId, userId) {
    try {
      const assignment = await ProjectAssignment.findOne({
        where: { project_id: projectId, user_id: userId }
      });

      if (!assignment) {
        throw new Error('Assignment not found');
      }

      await assignment.update({ is_active: false });

      logger.info(
        `User assignment removed: project ${projectId}, user ${userId}`
      );
      return { success: true };
    } catch (error) {
      logger.error('Error removing user assignment:', error);
      throw error;
    }
  }

  /**
   * Get project assignments
   */
  async getProjectAssignments(projectId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: { project_id: projectId, is_active: true },
        include: [
          {
            model: User,
            as: 'assignedUser',
            attributes: ['id', 'name', 'email', 'role']
          }
        ],
        order: [
          ['role', 'ASC'],
          ['assigned_at', 'ASC']
        ]
      });

      return assignments;
    } catch (error) {
      logger.error('Error getting project assignments:', error);
      throw error;
    }
  }

  /**
   * Publish project
   */
  async publishProject(projectId, publisherId, role) {
    try {
      const project = await Project.findByPk(projectId);

      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      if (role === 'student') {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Students cannot publish projects'
        );
      }

      if (project.status === 'draft' && project.created_by !== publisherId) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only the project creator can publish a draft project'
        );
      }

      if (project.status === 'published') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project already published');
      }

      if (project.status === 'archived') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project already archived');
      }

      /* if(!project.category_id){
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a category assigned before publishing'
        );
      } */

      if (!project.course_id) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must be assigned to a course'
        );
      }

      if (!project.instructor_id || project.instructor_id.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have at least one instructor assigned before publishing'
        );
      }

      if (!project.teaching_ass_id || project.teaching_ass_id.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have at least one teaching assistant assigned before publishing'
        );
      }

      const rubricData = await Rubric.findOne({
        where: { project_id: projectId }
      });
      if (!rubricData) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a rubric assigned before publishing'
        );
      }

      /* if (!project.start_date) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a start date assigned before publishing'
        );
      } */

      if (!project.due_date) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project must have a end date assigned before publishing'
        );
      }

      /* if (project.start_date > project.due_date) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project start date cannot be after end date'
        );
      } */

      await project.update({
        status: 'published',
        published_at: new Date(),
        published_by: publisherId
      });

      logger.info(`Project published: ${project.title} by user ${publisherId}`);
      return project;
    } catch (error) {
      logger.error('Error publishing project:', error);
      throw error;
    }
  }

  /**
   * Unpublish project
   */
  async unpublishProject(projectId, userId, role) {
    try {
      const project = await Project.findByPk(projectId);

      if (role === 'student') {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Students cannot unpublish projects'
        );
      }

      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      if (project.status !== 'published') {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Only published projects can be unpublished'
        );
      }

      // Check permissions
      if (project.created_by !== userId && project.published_by !== userId) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Permission denied: Only creator or publisher can unpublish'
        );
      }

      await project.update({
        status: 'draft',
        published_at: null,
        published_by: null
      });

      logger.info(`Project unpublished: ${project.title} by user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error unpublishing project:', error);
      throw error;
    }
  }

  /**
   * Save project as draft
   */
  async saveProjectAsDraft(projectId, userId) {
    try {
      const project = await Project.findByPk(projectId);

      if (!project) {
        throw new Error('Project not found');
      }

      // Check permissions
      if (project.created_by !== userId) {
        throw new Error('Permission denied: Only creator can save project');
      }

      await project.update({
        status: 'draft',
        published_at: null,
        published_by: null
      });

      logger.info(`Project saved as draft: ${project.title} by user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error saving project as draft:', error);
      throw error;
    }
  }

  /**
   * Get project by ID with all details
   * Get project with full details including assignments and template
   */
  async getProjectWithDetails(req) {
    try {
      const { id } = req.params;

      // Validate required fields
      if (!id)
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');

      // Validate user can update projects in this course
      const getProjectDetailsById =
        await enhancedProjectUtils.checkProjectExist(id);
      if (!getProjectDetailsById)
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      if (
        getProjectDetailsById.status === 'draft' &&
        getProjectDetailsById.created_by !== req.user.id
      )
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Draft project can be accessed only by creator'
        );

      const includeClause = [
        {
          model: Course,
          as: 'course',
          attributes: ['id', 'name', 'code', 'term', 'description']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: Rubric,
          as: 'rubrics',
          attributes: [
            'id',
            'title',
            'description',
            'criteria',
            'total_points',
            'grading_scale',
            'is_template',
            'template_name'
          ]
        },
        {
          model: Checkpoint,
          as: 'checkpoints',
          required: false,
          attributes: [
            'id',
            'title',
            'description',
            'checkpoint_number',
            'due_date',
            'status',
            'is_required',
            'metadata'
          ]
        },
        {
          model: ProjectTemplate,
          as: 'template',
          required: false
        },
        {
          model: ProjectAssignment,
          as: 'assignments',
          required: false,
          include: [
            {
              model: User,
              as: 'assignedUser',
              attributes: ['id', 'name', 'email']
            }
          ]
        }
      ];

      /*  if (await checkAccessRole(req.primaryRole, req.userRoles, 'studentOnly')) { }
       if (await checkAccessRole(req.primaryRole, req.userRoles, 'instructorOnly')) {
         includeClause.push({
           model: CourseEnrollment,
           as: 'courseEnrollments',
         });
       }
  */

      const project = await Project.findByPk(id, {
        include: includeClause
      });

      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }

      // Get assignment statistics
      let assignmentStats = {};
      try {
        assignmentStats = await this.getProjectAssignmentStats(id);
      } catch (error) {
        logger.warn('Error getting assignment stats:', error);
        assignmentStats = { totalAssignments: 0, byRole: {}, roles: [] };
      }

      // Add assignment statistics to project
      project.dataValues.assignmentStats = assignmentStats;

      // Get user's submission if exists
      let userSubmission = null;
      const isStudent = await checkAccessRole(
        req.primaryRole,
        req.userRoles,
        'studentOnly'
      );

      if (isStudent) {
        userSubmission = await Submission.unscoped().findAndCountAll({
          where: { project_id: project.id, user_id: req.user.id },
          attributes: [
            'id',
            'attempt_number',
            'notebook_s3_url',
            'status',
            'submitted_at',
            'late_submission',
            'days_late',
            'auto_save_data',
            'execution_results',
            'student_comments',
            'metadata'
          ],
          include: [
            {
              model: Grade,
              as: 'grade',
              paranoid: false,
              required: false,
              include: [
                {
                  model: User,
                  as: 'evaluator',
                  attributes: ['id', 'name', 'email']
                }
              ]
            }
          ]
        });
      } else {
        try {
          userSubmission = await Submission.findAndCountAll({
            where: { project_id: project.id },
            attributes: [
              'id',
              'attempt_number',
              'notebook_s3_url',
              'status',
              'submitted_at',
              'late_submission',
              'days_late',
              'auto_save_data',
              'execution_results',
              'student_comments',
              'metadata'
            ],
            include: [
              {
                model: Grade,
                as: 'grade',
                paranoid: false,
                required: false,
                include: [
                  {
                    model: User,
                    as: 'evaluator',
                    attributes: ['id', 'name', 'email']
                  }
                ]
              }
            ]
          });
        } catch (err) {
          logger.warn(`Error getting user submissions: ${err.message}`);
          // throw err;
        }
      }

      // Fetch instructor and teaching assistant user details
      let instructors = [];
      let teachingAssistants = [];

      if (
        Array.isArray(project.instructor_id) &&
        project.instructor_id.length > 0
      ) {
        instructors = await User.findAll({
          where: { id: project.instructor_id },
          attributes: ['id', 'name', 'email', 'status']
        });
      }

      if (
        Array.isArray(project.teaching_ass_id) &&
        project.teaching_ass_id.length > 0
      ) {
        teachingAssistants = await User.findAll({
          where: { id: project.teaching_ass_id },
          attributes: ['id', 'name', 'email', 'status']
        });
      }

      // ...after fetching the project and before returning projectData...
      const courseId = project.course_id;
      let students = [];
      if (courseId) {
        students = await User.findAll({
          include: [
            {
              model: CourseEnrollment,
              as: 'enrollments',
              where: { course_id: courseId, role_in_course: 'student' },
              attributes: []
            }
          ],
          attributes: ['id', 'name', 'email', 'status']
        });
      }

      // After fetching the project and before building projectData
      let datasetS3Details = [];
      if (
        Array.isArray(project.dataset_s3_url) &&
        project.dataset_s3_url.length > 0
      ) {
        try {
          datasetS3Details = await Promise.all(
            project.dataset_s3_url.map(async key => {
              try {
                // Fetch presigned URL and metadata for each S3 key
                return await s3Service.generatePresignedDownloadUrl(
                  key,
                  3600,
                  true
                );
              } catch (err) {
                // Optionally log or handle missing files
                logger.warn(`Could not fetch S3 details for key: ${key}`);
                return { key, error: 'Not found or inaccessible' };
              }
            })
          );
        } catch (error) {
          logger.warn('Error processing S3 dataset URLs:', error);
          datasetS3Details = [];
        }
      }

      // Add to response
      const projectData = {
        ...project.toJSON(),
        instructors,
        teachingAssistants,
        students,
        Submission: userSubmission,
        dataset_s3_url: datasetS3Details
      };

      return projectData;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get project assignment statistics
   */
  async getProjectAssignmentStats(projectId) {
    try {
      const stats = await ProjectAssignment.findAll({
        where: { project_id: projectId, is_active: true },
        attributes: [
          'role',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['role']
      });

      const totalAssignments = await ProjectAssignment.count({
        where: { project_id: projectId, is_active: true }
      });

      return {
        totalAssignments,
        byRole: stats.reduce((acc, stat) => {
          acc[stat.role] = parseInt(stat.dataValues.count);
          return acc;
        }, {}),
        roles: stats.map(stat => stat.role)
      };
    } catch (error) {
      logger.error('Error getting project assignment stats:', error);
      return {
        totalAssignments: 0,
        byRole: {},
        roles: []
      };
    }
  }

  /**
   * Get projects by user assignment
   */
  async getProjectsByUserAssignment(userId, role = null) {
    try {
      const whereClause = {
        user_id: userId,
        is_active: true
      };

      if (role) {
        whereClause.role = role;
      }

      const assignments = await ProjectAssignment.findAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: Course,
                as: 'course',
                attributes: ['id', 'name', 'code']
              },
              {
                model: User,
                as: 'creator',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ],
        order: [['assigned_at', 'DESC']]
      });

      return assignments.map(assignment => ({
        assignmentId: assignment.id,
        role: assignment.role,
        assignmentType: assignment.assignment_type,
        assignedAt: assignment.assigned_at,
        startDate: assignment.start_date,
        endDate: assignment.end_date,
        project: assignment.project
      }));
    } catch (error) {
      logger.error('Error getting projects by user assignment:', error);
      throw error;
    }
  }

  /**
   * Get user's project workload
   */
  async getUserProjectWorkload(userId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: {
          user_id: userId,
          is_active: true
        },
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'status', 'due_date', 'estimated_hours']
          }
        ]
      });

      const workload = {
        totalProjects: assignments.length,
        activeProjects: assignments.filter(
          a => a.project.status === 'published'
        ).length,
        draftProjects: assignments.filter(a => a.project.status === 'draft')
          .length,
        totalEstimatedHours: assignments.reduce(
          (sum, a) => sum + (a.project.estimated_hours || 0),
          0
        ),
        byRole: {},
        upcomingDeadlines: []
      };

      // Group by role
      assignments.forEach(assignment => {
        const role = assignment.role;
        if (!workload.byRole[role]) {
          workload.byRole[role] = {
            count: 0,
            projects: []
          };
        }
        workload.byRole[role].count++;
        workload.byRole[role].projects.push(assignment.project);
      });

      // Get upcoming deadlines (next 30 days)
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

      workload.upcomingDeadlines = assignments
        .filter(
          a => a.project.due_date && a.project.due_date <= thirtyDaysFromNow
        )
        .map(a => ({
          projectId: a.project.id,
          projectTitle: a.project.title,
          dueDate: a.project.due_date,
          daysUntilDue: Math.ceil(
            (a.project.due_date - new Date()) / (1000 * 60 * 60 * 24)
          )
        }))
        .sort((a, b) => a.daysUntilDue - b.daysUntilDue);

      return workload;
    } catch (error) {
      logger.error('Error getting user project workload:', error);
      throw error;
    }
  }

  /**
   * Duplicate project from template
   */
  async duplicateProjectFromTemplate(templateId, newProjectData, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId, {
        include: [
          {
            model: Project,
            as: 'project',
            include: ['rubrics']
          }
        ]
      });

      if (!template) {
        throw new Error('Template not found');
      }

      const originalProject = template.project;

      // Create new project with template data
      const newProject = await Project.create({
        ...newProjectData,
        title: newProjectData.title || `${originalProject.title} (Copy)`,
        description: newProjectData.description || originalProject.description,
        instructions:
          newProjectData.instructions || originalProject.instructions,
        difficulty_level:
          newProjectData.difficulty_level || originalProject.difficulty_level,
        estimated_hours:
          newProjectData.estimated_hours || originalProject.estimated_hours,
        learning_objectives:
          newProjectData.learning_objectives ||
          originalProject.learning_objectives,
        prerequisites:
          newProjectData.prerequisites || originalProject.prerequisites,
        tags: newProjectData.tags || originalProject.tags,
        created_by: userId,
        status: 'draft'
      });

      // Copy rubrics if they exist
      if (originalProject.rubrics && originalProject.rubrics.length > 0) {
        const rubricPromises = originalProject.rubrics.map(rubric =>
          Rubric.create({
            project_id: newProject.id,
            name: rubric.name,
            description: rubric.description,
            criteria: rubric.criteria,
            max_score: rubric.max_score,
            weight: rubric.weight
          })
        );
        await Promise.all(rubricPromises);
      }

      // Update template usage count
      await template.update({
        usage_count: template.usage_count + 1,
        last_used: new Date()
      });

      logger.info(
        `Project duplicated from template: ${template.template_name} by user ${userId}`
      );
      return newProject;
    } catch (error) {
      logger.error('Error duplicating project from template:', error);
      throw error;
    }
  }

  /**
   * Rate project template
   */
  async rateProjectTemplate(templateId, rating, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      // Calculate new average rating
      const newRatingCount = template.rating_count + 1;
      const newRating =
        (template.rating * template.rating_count + rating) / newRatingCount;

      await template.update({
        rating: newRating,
        rating_count: newRatingCount
      });

      logger.info(
        `Template rated: ${template.template_name} with rating ${rating} by user ${userId}`
      );
      return template;
    } catch (error) {
      logger.error('Error rating project template:', error);
      throw error;
    }
  }

  /**
   * Approve or reject a project template
   */
  async approveProjectTemplate(
    templateId,
    approved,
    reviewerId,
    reviewNotes = null
  ) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      const reviewStatus = approved ? 'approved' : 'rejected';

      await template.update({
        review_status: reviewStatus,
        reviewed_by: reviewerId,
        review_notes: reviewNotes,
        review_date: new Date()
      });

      return template;
    } catch (error) {
      logger.error('Error approving project template:', error);
      throw error;
    }
  }

  /**
   * Feature or unfeature a project template
   */
  async featureProjectTemplate(templateId, featured, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      await template.update({
        is_featured: featured
      });

      return template;
    } catch (error) {
      logger.error('Error featuring project template:', error);
      throw error;
    }
  }

  /**
   * Archive a project template
   */
  async archiveProjectTemplate(templateId, userId, archiveReason = null) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      await template.update({
        review_status: 'archived',
        review_notes: archiveReason || 'Template archived by user'
      });

      return template;
    } catch (error) {
      logger.error('Error archiving project template:', error);
      throw error;
    }
  }

  /**
   * Duplicate a project template
   */
  async duplicateProjectTemplate(templateId, newTemplateData, userId) {
    try {
      const originalTemplate = await ProjectTemplate.findByPk(templateId, {
        include: [
          {
            model: Project,
            as: 'project'
          }
        ]
      });

      if (!originalTemplate) {
        throw new Error('Template not found');
      }

      // Create new project from template
      const newProjectData = {
        ...originalTemplate.project.toJSON(),
        title:
          newTemplateData.title || `${originalTemplate.project.title} (Copy)`,
        description:
          newTemplateData.description || originalTemplate.project.description,
        course_id: newTemplateData.courseId,
        created_by: userId,
        is_template: false,
        status: 'draft'
      };

      // Remove template-specific fields
      delete newProjectData.id;
      delete newProjectData.created_at;
      delete newProjectData.updated_at;

      const newProject = await Project.create(newProjectData);

      // Create new template entry
      const newTemplate = await ProjectTemplate.create({
        project_id: newProject.id,
        template_name:
          newTemplateData.templateName ||
          `${originalTemplate.template_name} (Copy)`,
        template_description:
          newTemplateData.templateDescription ||
          originalTemplate.template_description,
        category: newTemplateData.category || originalTemplate.category,
        subcategory:
          newTemplateData.subcategory || originalTemplate.subcategory,
        difficulty_level:
          newTemplateData.difficultyLevel || originalTemplate.difficulty_level,
        estimated_hours:
          newTemplateData.estimatedHours || originalTemplate.estimated_hours,
        total_points:
          newTemplateData.totalPoints || originalTemplate.total_points,
        learning_objectives:
          newTemplateData.learningObjectives ||
          originalTemplate.learning_objectives,
        prerequisites:
          newTemplateData.prerequisites || originalTemplate.prerequisites,
        skills_covered:
          newTemplateData.skillsCovered || originalTemplate.skills_covered,
        technologies_used:
          newTemplateData.technologiesUsed ||
          originalTemplate.technologies_used,
        tags: newTemplateData.tags || originalTemplate.tags,
        is_featured: false,
        is_public:
          newTemplateData.isPublic !== undefined
            ? newTemplateData.isPublic
            : originalTemplate.is_public,
        rating: 0,
        rating_count: 0,
        usage_count: 0,
        download_count: 0,
        version: '1.0.0',
        changelog: [
          {
            version: '1.0.0',
            date: new Date().toISOString(),
            changes: ['Template duplicated from original'],
            author: userId
          }
        ],
        metadata: {
          duplicated_from: templateId,
          original_author: originalTemplate.created_by,
          duplication_date: new Date().toISOString()
        },
        created_by: userId,
        review_status: 'pending'
      });

      return {
        project: newProject,
        template: newTemplate
      };
    } catch (error) {
      logger.error('Error duplicating project template:', error);
      throw error;
    }
  }

  async creationOfProject(req) {
    const {
      title,
      description,
      courseId,
      projectType = 'individual',
      difficulty_level = 'beginner',
      estimatedHours,
      totalPoints = 100,
      dueDate,
      startDate,
      instructions,
      project_overview,
      learning_objectives,
      prerequisites,
      skillsCovered = [],
      technologiesUsed = [],
      tags = [],
      isTemplate = false,
      templateCategory = 'general',
      templateSubcategory,
      assignments = [],
      rubrics = [],
      isScreen,
      status = 'draft',
      id,
      categoryId = '3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66',
      instructorIds = [],
      teachingAssId = [],
      maxSubmissions,
      lateSubmissionsAllowed = false,
      sandbox_time_duration,
      late_submission_days_allowed
    } = req.body;

    // Validate required fields
    // if (!title || !description || !courseId)
    if (!title)
      throw new ApiError(httpStatus.BAD_REQUEST, 'Project Title is required');

    // Validate user can create projects in this course

    if (!(await checkAccessRole(req.primaryRole, req.userRoles, 'adminOnly'))) {
      try {
        if (courseId) {
          const permission = await courseRoleService.canCreateProjects(
            req.user.id,
            courseId
          );
          if (!permission.canCreate)
            throw new ApiError(httpStatus.FORBIDDEN, permission.reason);
        }
      } catch (error) {
        throw error;
      }
    }

    try {
      // Update project
      const projectData = {};
      if (title) projectData.title = title;
      if (categoryId) projectData.category_id = categoryId;
      if (courseId) projectData.course_id = courseId;
      if (description) projectData.description = description;
      if (instructorIds) projectData.instructor_id = instructorIds;
      if (teachingAssId) projectData.teaching_ass_id = teachingAssId;
      if (projectType) projectData.type = projectType;
      if (difficulty_level)
        projectData.difficulty_level = difficulty_level;
      if (totalPoints) projectData.total_points = totalPoints;
      if (estimatedHours)
        projectData.estimated_hours = estimatedHours;
      if (tags) projectData.tags = tags;
      if (project_overview)
        projectData.project_overview = project_overview;
      if (learning_objectives)
        projectData.learning_objectives = learning_objectives;
      if (prerequisites)
        projectData.prerequisites = prerequisites;
      if (instructions) projectData.instructions = instructions;
      // if (resources) projectData.resources = resources;
      if (maxSubmissions) projectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed)
        projectData.late_submission_allowed = lateSubmissionsAllowed;
      if (dueDate) projectData.due_date = dueDate;
      if (status) projectData.status = status;
      if (isScreen) projectData.isScreen = isScreen;
      if (startDate) projectData.start_date = startDate;
      if (isTemplate) projectData.is_template = isTemplate;
      if (templateCategory)
        projectData.template_category = templateCategory;
      if (templateSubcategory)
        projectData.template_subcategory = templateSubcategory;
      if (late_submission_days_allowed)
        projectData.late_submission_days_allowed = late_submission_days_allowed;
      if (sandbox_time_duration)
        projectData.sandbox_time_duration = sandbox_time_duration;

      // if (settings) projectData.settings = settings;

      if (req.user.id) {
        projectData.created_by = req.user.id;
      }

      let project;
      if (isScreen > 1 || id) {
        projectData.id = id;
        project = await this.updateProject(projectData, assignments);
      } else {
        project = await this.createProject(projectData, assignments);
      }

      return {
        id: project.id,
        projectId: project.project_code,
        title: project.title,
        status: project.status,
        courseId: project.course_id,
        projectType: project.project_type,
        totalPoints: project.total_points,
        isTemplate: project.is_template,
        createdAt: project.createdAt,
        isScreen: project.isScreen
      };
    } catch (error) {
      throw error;
    }
  }

  async UpdationOfProject(req) {
    const {
      title,
      description,
      courseId,
      projectType = 'individual',
      difficulty_level = 'beginner',
      estimatedHours,
      totalPoints = 100,
      dueDate,
      startDate,
      instructions,
      project_overview,
      learning_objectives,
      prerequisites,
      skillsCovered = [],
      technologiesUsed = [],
      tags = [],
      isTemplate = false,
      templateCategory = 'general',
      templateSubcategory,
      assignments = [],
      rubrics = [],
      categoryId = '3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66',
      instructorIds = [],
      teachingAssId = [],
      maxSubmissions,
      lateSubmissionsAllowed = false,
      sandbox_time_duration,
      late_submission_days_allowed
    } = req.body;

    const { id } = req.params;

    // Validate required fields
    if (!id)
      throw new ApiError(httpStatus.BAD_REQUEST, 'Project ID is required');
    if (!title || !description || !courseId)
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Title, description, and course ID are required'
      );

    // Validate user can update projects in this course
    const updateProjectDetails =
      await enhancedProjectUtils.checkProjectExist(id);
    if (!updateProjectDetails)
      throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
    if (updateProjectDetails.status === 'published')
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Published project cannot be updated'
      );
    if (updateProjectDetails.created_by !== req.user.id)
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'Only project creator can update the project'
      );
    try {
      // Update project
      const projectData = {};
      if (title) projectData.title = title;
      if (categoryId) projectData.category_id = categoryId;
      if (courseId) projectData.course_id = courseId;
      if (description) projectData.description = description;
      if (instructorIds) projectData.instructor_id = instructorIds;
      if (teachingAssId) projectData.teaching_ass_id = teachingAssId;
      if (projectType) projectData.type = projectType;
      if (difficulty_level)
        projectData.difficulty_level = difficulty_level;
      if (totalPoints) projectData.total_points = totalPoints;
      if (estimatedHours)
        projectData.estimated_hours = estimatedHours;
      if (tags) projectData.tags = tags;
      if (project_overview)
        projectData.project_overview = project_overview;
      if (learning_objectives)
        projectData.learning_objectives = learning_objectives;
      if (prerequisites)
        projectData.prerequisites = prerequisites;
      if (instructions) projectData.instructions = instructions;
      // if (resources) projectData.resources = resources;
      if (maxSubmissions) projectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed)
        projectData.late_submission_allowed = lateSubmissionsAllowed;
      if (dueDate) projectData.due_date = dueDate;
      if (startDate) projectData.start_date = startDate;
      if (isTemplate) projectData.is_template = isTemplate;
      if (templateCategory)
        projectData.template_category = templateCategory;
      if (templateSubcategory)
        projectData.template_subcategory = templateSubcategory;
      if (late_submission_days_allowed)
        projectData.late_submission_days_allowed = late_submission_days_allowed;
      if (sandbox_time_duration)
        projectData.sandbox_time_duration = sandbox_time_duration;

      // if (settings) projectData.settings = settings;

      projectData.id = id;
      const project = await this.updateProject(projectData, assignments);

      return {
        id: project.id,
        projectId: project.project_code,
        title: project.title,
        status: project.status,
        courseId: project.course_id,
        projectType: project.project_type,
        createdAt: project.createdAt
      };
    } catch (error) {
      throw error;
    }
  }
}

export default new EnhancedProjectService();
