import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import logger from '../config/logger.config.js';

/**
 * JWT Authentication Service
 *
 * Handles JWT token generation, validation, and management for both
 * access tokens and refresh tokens with proper security practices.
 */
class JWTService {
  constructor() {
    this.accessTokenSecret =
      process.env.JWT_SECRET || 'your-jwt-secret-change-in-production';
    this.refreshTokenSecret =
      process.env.JWT_REFRESH_SECRET ||
      'your-refresh-secret-change-in-production';
    this.accessTokenExpiry = process.env.JWT_EXPIRES_IN || '15m'; // Short-lived access tokens
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRES_IN || '7d'; // Long-lived refresh tokens
    this.issuer = process.env.JWT_ISSUER || 'bits-dataScience-platform';
    this.audience = process.env.JWT_AUDIENCE || 'bits-platform-users';
  }

  /**
   * Generate access token with user data
   * @param {Object} user - User object with id, email, name, roles
   * @param {Object} sessionData - Additional session data (LTI context, etc.)
   * @returns {string} JWT access token
   */
  generateAccessToken(user, sessionData = {}) {
    try {
      // Extract only role names for the token payload
      const roleNames = user.roles?.map(role => role.name) || [];

      const payload = {
        userId: user.id,
        email: user.email,
        name: user.name,
        roles: roleNames, // Only role names, not full role objects
        sessionType: sessionData.sessionType || 'manual', // 'lti' or 'manual'
        ltiContext: sessionData.ltiContext || null,
        ltiResourceLink: sessionData.ltiResourceLink || null,
        iat: Math.floor(Date.now() / 1000)
      };

      const options = {
        expiresIn: this.accessTokenExpiry,
        issuer: this.issuer,
        audience: this.audience,
        subject: user.id
      };

      return jwt.sign(payload, this.accessTokenSecret, options);
    } catch (error) {
      logger.error('Error generating access token:', error);
      throw new Error('Failed to generate access token');
    }
  }

  /**
   * Generate refresh token
   * @param {string} userId - User ID
   * @returns {string} JWT refresh token
   */
  generateRefreshToken(userId) {
    try {
      const payload = {
        userId,
        type: 'refresh',
        jti: crypto.randomUUID(), // Unique token ID for revocation
        iat: Math.floor(Date.now() / 1000)
      };

      const options = {
        expiresIn: this.refreshTokenExpiry,
        issuer: this.issuer,
        audience: this.audience,
        subject: userId
      };

      return jwt.sign(payload, this.refreshTokenSecret, options);
    } catch (error) {
      logger.error('Error generating refresh token:', error);
      throw new Error('Failed to generate refresh token');
    }
  }

  /**
   * Verify access token
   * @param {string} token - JWT access token
   * @returns {Object} Decoded token payload
   */
  verifyAccessToken(token) {
    try {
      const options = {
        issuer: this.issuer,
        audience: this.audience
      };

      return jwt.verify(token, this.accessTokenSecret, options);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Access token expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid access token');
      } else {
        logger.error('Error verifying access token:', error);
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Verify refresh token
   * @param {string} token - JWT refresh token
   * @returns {Object} Decoded token payload
   */
  verifyRefreshToken(token) {
    try {
      const options = {
        issuer: this.issuer,
        audience: this.audience
      };

      const decoded = jwt.verify(token, this.refreshTokenSecret, options);

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Refresh token expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid refresh token');
      } else {
        logger.error('Error verifying refresh token:', error);
        throw new Error('Refresh token verification failed');
      }
    }
  }

  /**
   * Extract token from Authorization header
   * @param {string} authHeader - Authorization header value
   * @returns {string|null} Extracted token or null
   */
  extractTokenFromHeader(authHeader) {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7).trim();
  }

  /**
   * Get token expiration time in seconds
   * @param {string} token - JWT token
   * @returns {number} Expiration timestamp
   */
  getTokenExpiration(token) {
    try {
      const decoded = jwt.decode(token);
      return decoded?.exp || 0;
    } catch (error) {
      logger.error('Error decoding token for expiration:', error);
      return 0;
    }
  }

  /**
   * Check if token is expired
   * @param {string} token - JWT token
   * @returns {boolean} True if expired
   */
  isTokenExpired(token) {
    const exp = this.getTokenExpiration(token);
    return exp < Math.floor(Date.now() / 1000);
  }

  /**
   * Generate token pair (access + refresh)
   * @param {Object} user - User object
   * @param {Object} sessionData - Session data
   * @returns {Object} Object with accessToken and refreshToken
   */
  generateTokenPair(user, sessionData = {}) {
    const accessToken = this.generateAccessToken(user, sessionData);
    const refreshToken = this.generateRefreshToken(user.id);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.accessTokenExpiry,
      tokenType: 'Bearer'
    };
  }
}

export default new JWTService();
