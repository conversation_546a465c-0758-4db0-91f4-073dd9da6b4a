import jwt from 'jsonwebtoken';
import { User, Role, Permission } from '../models/associations.js';
import logger from '../config/logger.config.js';
import jwtService from '../services/jwt.service.js';
import sessionCacheService from '../services/sessionCache.service.js';

// JWT Authentication Middleware
export const jwtMiddleware = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message:
          'Missing or invalid authorization header. Send Authorization: Bearer <token>'
      });
    }

    // Verify the access token
    let decoded;
    try {
      decoded = jwtService.verifyAccessToken(token);
    } catch (error) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      });
    }

    const userId = decoded.userId;

    // Try to get user session data from Redis cache first
    let sessionData = await sessionCacheService.getUserSession(userId);

    if (!sessionData) {
      // Cache miss - fetch from database and cache it
      logger.debug(
        `[JWT Auth] Cache miss for user: ${userId}, fetching from database`
      );

      const user = await User.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions'
              }
            ]
          }
        ],
        attributes: { exclude: ['password_hash'] }
      });

      if (!user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'User not found'
        });
      }

      // Create session data structure
      sessionData = {
        cookie: {
          originalMaxAge: ********,
          expires: new Date(Date.now() + ********).toISOString(),
          secure: process.env.NODE_ENV === 'production',
          httpOnly: true,
          path: '/'
        },
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          lms_user_id: user.lms_user_id,
          google_id: user.google_id,
          profile_picture: user.profile_picture,
          last_login: user.last_login,
          status: user.status,
          preferences: user.preferences || {},
          metadata: user.metadata || {},
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          deletedAt: user.deletedAt,
          roles: user.roles || []
        },
        sessionType: decoded.sessionType || 'manual',
        ltiContext: decoded.ltiContext || null,
        ltiResourceLink: decoded.ltiResourceLink || null,
        ltiLaunchData: decoded.ltiLaunchData, // LTI launch data is not stored in token for security
        createdAt: new Date().toISOString()
      };

      // Cache the session data
      await sessionCacheService.setUserSession(userId, sessionData);
    }

    console.log('JWT middleware -- sessionData', { sessionData });

    // Check if user account is active
    if (sessionData.user.status !== 'active') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User account is not active'
      });
    }

    // Extract user permissions and roles for RBAC
    const userPermissions = [];
    const userRoles = [];

    if (sessionData.user.roles) {
      sessionData.user.roles.forEach(role => {
        userRoles.push(role.name);
        if (role.permissions) {
          role.permissions.forEach(permission => {
            if (!userPermissions.includes(permission.key)) {
              userPermissions.push(permission.key);
            }
          });
        }
      });
    }

    // Attach user info to request object
    req.user = sessionData.user;
    req.userPermissions = userPermissions;
    req.userRoles = userRoles;
    req.primaryRole = userRoles[0] || 'student';

    // Populate req.session with complete session data for backward compatibility
    req.session = {
      cookie: sessionData.cookie || {
        originalMaxAge: ********,
        expires: new Date(Date.now() + ********).toISOString(),
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        path: '/'
      },
      user: sessionData.user,
      ltiContext: sessionData.ltiContext,
      ltiResourceLink: sessionData.ltiResourceLink,
      ltiLaunchData: sessionData.ltiLaunchData
    };

    // Additional properties for compatibility
    req.sessionType = sessionData.sessionType;
    req.ltiContext = sessionData.ltiContext;
    req.ltiResourceLink = sessionData.ltiResourceLink;
    req.ltiLaunchData = sessionData.ltiLaunchData;

    // Add Jupiter username for compatibility
    req.user.jupiterUserName = `user_${sessionData.user.id}`;

    logger.debug(`[JWT Auth] Authentication successful for user: ${userId}`);
    next();
  } catch (error) {
    logger.error('[JWT Auth] Authentication error:', error);
    return res.status(500).json({
      error: 'Authentication Error',
      message: 'Internal server error during authentication'
    });
  }
};

// Optional middleware for endpoints that don't require authentication
export const optionalJwtMiddleware = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = jwtService.extractTokenFromHeader(authHeader);

  if (!token) {
    // No token provided - set default values
    req.user = null;
    req.userPermissions = [];
    req.userRoles = [];
    req.sessionData = null;
    req.sessionType = null;
    return next();
  }

  // If token is provided, validate it using the main middleware
  return jwtMiddleware(req, res, next);
};

// Generate JWT token
export const generateToken = user => {
  const payload = {
    userId: user.id,
    email: user.email,
    name: user.name
  };

  const options = {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
    audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
  };

  return jwt.sign(payload, process.env.JWT_SECRET, options);
};

// Verify token utility - now uses the same JWT implementation as jwtMiddleware
export const verifyToken = async (req, res, next) => {
  // Use the same JWT middleware implementation for consistency
  return jwtMiddleware(req, res, next);
};
