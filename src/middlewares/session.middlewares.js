import logger from '../config/logger.config.js';
import { 
  updateSessionActivity, 
  isSessionExpired, 
  getSessionExpirationInfo,
  validateSessionData 
} from '../config/session.config.js';

/**
 * Enhanced Session Middleware for LTI
 * 
 * Provides session validation, activity tracking, and proper error handling
 * for LTI launch sessions.
 */

/**
 * Middleware to enforce session on protected routes
 * Returns JSON error with relaunch hint on session expiration
 */
export const requireLtiSession = (req, res, next) => {
  try {
    // Check if session exists
    if (!req.session) {
      logger.warn('[Session Middleware] No session found', {
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        error: 'session_expired',
        reason: 'No active session found',
        relaunchHint: true,
        message: 'Please relaunch from your learning management system'
      });
    }

    // Validate session data structure
    try {
      validateSessionData(req.session);
    } catch (validationError) {
      logger.warn('[Session Middleware] Invalid session data', {
        error: validationError.message,
        path: req.path,
        sessionId: req.sessionID
      });
      
      return res.status(401).json({
        error: 'session_expired',
        reason: 'Invalid session data',
        relaunchHint: true,
        message: 'Please relaunch from your learning management system'
      });
    }

    // Check if session is expired
    if (isSessionExpired(req.session)) {
      logger.info('[Session Middleware] Session expired due to inactivity', {
        path: req.path,
        sessionId: req.sessionID,
        lastActivity: req.session.last_activity
      });
      
      return res.status(401).json({
        error: 'session_expired',
        reason: 'Session expired due to inactivity',
        relaunchHint: true,
        message: 'Please relaunch from your learning management system'
      });
    }

    // Update session activity
    updateSessionActivity(req);

    // Add session info to request for use in controllers
    req.sessionInfo = {
      sessionId: req.sessionID,
      userId: req.session.sub,
      platformId: req.session.iss,
      contextId: req.session.context_id,
      resourceLinkId: req.session.resource_link_id,
      roles: req.session.roles || [],
      ltiRoles: req.session.lti_roles || [],
      expirationInfo: getSessionExpirationInfo(req.session)
    };

    next();
  } catch (error) {
    logger.error('[Session Middleware] Unexpected error:', {
      error: error.message,
      stack: error.stack,
      path: req.path
    });
    
    return res.status(500).json({
      error: 'session_error',
      reason: 'Internal session error',
      relaunchHint: true,
      message: 'Please try relaunching from your learning management system'
    });
  }
};

/**
 * Middleware to track session activity (for all routes)
 */
export const trackSessionActivity = (req, res, next) => {
  if (req.session && req.session.last_activity) {
    updateSessionActivity(req);
  }
  next();
};

/**
 * Middleware to validate LTI session specifically
 */
export const requireLtiLaunchSession = (req, res, next) => {
  if (!req.session || req.session.session_type !== 'lti_launch') {
    logger.warn('[Session Middleware] Invalid LTI session type', {
      path: req.path,
      sessionType: req.session?.session_type || 'none'
    });
    
    return res.status(401).json({
      error: 'invalid_session_type',
      reason: 'Session is not from LTI launch',
      relaunchHint: true,
      message: 'Please access this resource through your learning management system'
    });
  }
  
  next();
};

/**
 * Middleware to check specific LTI roles
 */
export const requireLtiRole = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.session || !req.session.lti_roles) {
      return res.status(403).json({
        error: 'insufficient_roles',
        reason: 'No LTI roles found in session',
        relaunchHint: true,
        message: 'Please relaunch with appropriate permissions'
      });
    }

    const userRoles = req.session.lti_roles;
    const hasRequiredRole = requiredRoles.some(role => 
      userRoles.some(userRole => userRole.includes(role))
    );

    if (!hasRequiredRole) {
      logger.warn('[Session Middleware] Insufficient LTI roles', {
        required: requiredRoles,
        userRoles: userRoles,
        path: req.path
      });
      
      return res.status(403).json({
        error: 'insufficient_roles',
        reason: `Required roles: ${requiredRoles.join(', ')}`,
        relaunchHint: true,
        message: 'You do not have the required permissions for this resource'
      });
    }

    next();
  };
};

/**
 * Middleware to check context access
 */
export const requireContextAccess = (req, res, next) => {
  const requestedContextId = req.params.contextId || req.query.contextId;
  
  if (!requestedContextId) {
    return next(); // No context restriction
  }

  if (!req.session || !req.session.context_id) {
    return res.status(403).json({
      error: 'no_context_access',
      reason: 'No context information in session',
      relaunchHint: true,
      message: 'Please relaunch from the correct course context'
    });
  }

  if (req.session.context_id !== requestedContextId) {
    logger.warn('[Session Middleware] Context access denied', {
      requested: requestedContextId,
      sessionContext: req.session.context_id,
      path: req.path
    });
    
    return res.status(403).json({
      error: 'context_access_denied',
      reason: 'Access denied for this context',
      relaunchHint: true,
      message: 'You do not have access to this course context'
    });
  }

  next();
};

/**
 * Middleware to add session security headers
 */
export const addSessionSecurityHeaders = (req, res, next) => {
  // Add security headers for session protection
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Add session info header (for debugging in development)
  if (process.env.NODE_ENV === 'development' && req.session) {
    res.setHeader('X-Session-Info', JSON.stringify({
      sessionId: req.sessionID,
      userId: req.session.sub,
      contextId: req.session.context_id,
      expiresAt: req.sessionInfo?.expirationInfo?.expiresAt
    }));
  }
  
  next();
};

/**
 * Middleware to handle session cleanup on logout
 */
export const handleSessionLogout = (req, res, next) => {
  const returnUrl = req.query.return_url || req.body.return_url;
  
  req.session.destroy((err) => {
    if (err) {
      logger.error('[Session Middleware] Failed to destroy session:', {
        error: err.message,
        sessionId: req.sessionID
      });
    } else {
      logger.info('[Session Middleware] Session destroyed successfully', {
        sessionId: req.sessionID
      });
    }
    
    // Clear session cookie
    res.clearCookie('bits.session.id', {
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax'
    });
    
    // Return logout response with LMS return option
    res.json({
      success: true,
      message: 'Logged out successfully',
      returnToLms: returnUrl ? {
        url: returnUrl,
        message: 'Return to Learning Management System'
      } : null
    });
  });
};

export default {
  requireLtiSession,
  trackSessionActivity,
  requireLtiLaunchSession,
  requireLtiRole,
  requireContextAccess,
  addSessionSecurityHeaders,
  handleSessionLogout
};

