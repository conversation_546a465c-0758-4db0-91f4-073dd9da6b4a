import { createProxyMiddleware } from 'http-proxy-middleware';
import { URLSearchParams } from 'url';
import config from '../config/database.config.js';
import logger from '../config/logger.config.js';
import jupyterService from '../services/jupyterhub.service.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';

/**
 * Middleware to ensure a per-user Jupyter token is present on the request object.
 * Runs for normal HTTP requests (Express middleware chain). WebSocket upgrade
 * requests are handled separately in server.js where the token is also attached.
 */
export const attachJupyterToken = async (req, res, next) => {
  const username = req.user?.jupiterUserName;
  console.log('In attachJupyterToken Middleware', JSON.stringify(username));
  try {
    // If query already contains a token (e.g., proxied or pre-signed), skip fetching
    const hasTokenInQuery =
      typeof req.url === 'string' && req.url.includes('token=');
    if (
      req.user &&
      username &&
      !req.user.jupyterUserToken &&
      !hasTokenInQuery
    ) {
      logger.info(`Attaching Jupyter token for user: ${username}`);
      req.user.jupyterUserToken = await jupyterService.getUserToken(username);
    }
    return next();
  } catch (err) {
    // Normalize common failures to clearer HTTP errors
    const msg = err?.message || 'Failed to obtain Jupyter token';
    logger.error(`attachJupyterToken error for '${username}': ${msg}`);
    // Attach status to error if not present
    if (!err.status) {
      if (msg.includes('permission') || msg.includes('permission')) {
        err.status = 403;
      } else if (
        msg.includes('not found') ||
        msg.includes('Missing Jupyter user context')
      ) {
        err.status = 404;
      } else {
        err.status = 500;
      }
    }
    return next(err);
  }
};

const component = 'jupyterProxy';
const auditComponent = 'JupyterHub Proxy';

const jupyterProxyCore = createProxyMiddleware({
  target: config.jupyterhub.url,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  followRedirects: true,
  selfHandleResponse: true,
  /**
   * Synchronous path rewrite leveraging the user + token already resolved.
   * For WebSockets, the token is attached during the upgrade authentication.
   */
  pathRewrite: (path, req) => {
    console.log(req.user);
    if (!req.user || !req.user.jupiterUserName || !req.user.jupyterUserToken) {
      logger.error('Missing Jupyter user context or token on request.');
      throw new Error('Missing Jupyter user context for proxying.');
    }

    const username = req.user.jupiterUserName;
    const token = req.user.jupyterUserToken;

    // Separate path and query if present
    const hasQuery = path.includes('?');
    const [rawPath, rawQuery = ''] = hasQuery ? path.split('?') : [path, ''];

    // Build /user/{username}{originalPath}
    const userScopedPath = `/user/${username}${rawPath}`;

    // Merge existing query params with token param
    const params = new URLSearchParams(rawQuery);
    params.set('token', token);
    const queryString = params.toString();

    const rewritten = `${userScopedPath}?${queryString}`;
    logger.info(`Jupyter proxy rewrite: '${path}' -> '${rewritten}'`);
    return rewritten;
  },
  on: {
    proxyReq: (proxyReq, req, _res) => {
      logger.info('Jupyter proxy request:', req);
    },
    // Handle proxy responses manually so we can use buildSuccessResponse for JSON
    proxyRes: (proxyRes, req, res) => {
      const contentType = proxyRes.headers['content-type'] || '';
      logger.info(`Intercepting response with Content-Type: ${contentType}`);

      const isJson = contentType.includes('application/json');

      if (!isJson) {
        // Stream non-JSON responses as-is
        res.writeHead(proxyRes.statusCode || 200, proxyRes.headers);
        proxyRes.pipe(res);
        return;
      }

      // Buffer JSON responses to transform and wrap consistently
      let body = Buffer.from([]);
      proxyRes.on('data', chunk => {
        body = Buffer.concat([body, chunk]);
      });

      proxyRes.on('end', async () => {
        try {
          const dataString = body.toString('utf8');
          const jsonData = JSON.parse(dataString);

          res.message = jsonData.message ? jsonData.message : res.message;
          // Use the shared success responder for consistency
          await buildSuccessResponse(
            req,
            res,
            jsonData,
            'Jupyter response processed successfully',
            component,
            auditComponent
          );
        } catch (error) {
          logger.error('Error modifying Jupyter proxy response:', error);
          // Fallback: return original JSON untouched
          try {
            res.writeHead(proxyRes.statusCode || 200, {
              ...proxyRes.headers,
              'content-type': 'application/json; charset=utf-8'
            });
            res.end(body);
          } catch (writeErr) {
            logger.error(
              'Error writing fallback Jupyter proxy response:',
              writeErr
            );
            if (!res.headersSent) {
              res.status(500).json({
                error: 'Internal Server Error',
                message: 'Failed to process Jupyter response'
              });
            }
          }
        }
      });
    }
  },

  onError: (err, req, res) => {
    logger.error('Jupyter PROXY ERROR:', err);
    if (!res.headersSent) {
      res.status(504).send('Jupyter proxy gateway timeout');
    }
  }
});

// Export the proxy wrapped with asyncHandler to align with controller patterns
export const jupyterProxy = asyncHandler(
  async (req, res, next) => jupyterProxyCore(req, res, next),
  { component, auditComponent }
);
