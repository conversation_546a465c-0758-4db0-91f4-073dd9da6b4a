import jwtService from '../services/jwt.service.js';
import sessionCacheService from '../services/sessionCache.service.js';
import { User, Role, Permission } from '../models/associations.js';
import logger from '../config/logger.config.js';

/**
 * JWT Authentication Middleware
 *
 * Validates JWT access tokens and retrieves user data from Redis cache
 * for optimal performance. Falls back to database if cache miss occurs.
 */

/**
 * Main JWT authentication middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const jwtAuthMiddleware = async (req, res, next) => {
  try {
    logger.info(`[JWT Auth] Authenticating user for path: ${req.path}`);
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message:
          'Missing or invalid authorization header. Send Authorization: Bearer <token>'
      });
    }

    // Verify the access token
    let decoded;
    try {
      decoded = jwtService.verifyAccessToken(token);
    } catch (error) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      });
    }

    const userId = decoded.userId;

    // Try to get user session data from Redis cache first
    let sessionData = await sessionCacheService.getUserSession(userId);

    logger.info(
      `[JWT Auth] Cache lookup for user: ${userId}, Cache hit: ${JSON.stringify(sessionData, null, 2)}`
    );

    if (!sessionData) {
      // Cache miss - fetch from database and cache it
      logger.debug(
        `[JWT Auth] Cache miss for user: ${userId}, fetching from database`
      );

      const user = await User.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions'
              }
            ]
          }
        ],
        attributes: { exclude: ['password_hash'] }
      });

      if (!user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'User not found'
        });
      }

      // Create session data structure
      sessionData = {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          lms_user_id: user.lms_user_id,
          google_id: user.google_id,
          profile_picture: user.profile_picture,
          last_login: user.last_login,
          status: user.status,
          preferences: user.preferences || {},
          metadata: user.metadata || {},
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          deletedAt: user.deletedAt,
          roles: user.roles || []
        },
        sessionType: decoded.sessionType || 'manual',
        ltiContext: decoded.ltiContext || null,
        ltiResourceLink: decoded.ltiResourceLink || null,
        ltiLaunchData: null, // Not stored in token for security
        createdAt: new Date().toISOString()
      };

      // Cache the session data
      await sessionCacheService.setUserSession(userId, sessionData);
    }

    // Check if user account is active
    if (sessionData.user.status !== 'active') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User account is not active'
      });
    }

    // Extract user permissions and roles for RBAC
    const userPermissions = [];
    const userRoles = [];

    if (sessionData.user.roles) {
      sessionData.user.roles.forEach(role => {
        userRoles.push(role.name);
        if (role.permissions) {
          role.permissions.forEach(permission => {
            if (!userPermissions.includes(permission.key)) {
              userPermissions.push(permission.key);
            }
          });
        }
      });
    }

    // Attach user info to request object
    req.user = sessionData.user;
    req.userPermissions = userPermissions;
    req.userRoles = userRoles;
    req.primaryRole = userRoles[0] || 'student';
    req.sessionData = sessionData;
    req.sessionType = sessionData.sessionType;
    req.ltiContext = sessionData.ltiContext;
    req.ltiResourceLink = sessionData.ltiResourceLink;
    req.ltiLaunchData = sessionData.ltiLaunchData;

    // Add Jupiter username for compatibility
    req.user.jupiterUserName = `user_${sessionData.user.id}`;

    logger.debug(`[JWT Auth] Authentication successful for user: ${userId}`);
    next();
  } catch (error) {
    logger.error('[JWT Auth] Authentication error:', error);
    return res.status(500).json({
      error: 'Authentication Error',
      message: 'Internal server error during authentication'
    });
  }
};

/**
 * Optional JWT middleware for endpoints that don't require authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const optionalJwtAuthMiddleware = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = jwtService.extractTokenFromHeader(authHeader);

  if (!token) {
    // No token provided - set default values
    req.user = null;
    req.userPermissions = [];
    req.userRoles = [];
    req.sessionData = null;
    req.sessionType = null;
    return next();
  }

  // If token is provided, validate it using the main middleware
  return jwtAuthMiddleware(req, res, next);
};

/**
 * Middleware to check if user has specific roles
 * @param {Array} requiredRoles - Array of required role names
 * @returns {Function} Express middleware function
 */
export const requireRoles = requiredRoles => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    const userRoles = req.userRoles || [];
    const hasRequiredRole = requiredRoles.some(role =>
      userRoles.includes(role)
    );

    if (!hasRequiredRole) {
      return res.status(403).json({
        error: 'Forbidden',
        message: `Access denied. Required roles: ${requiredRoles.join(', ')}`
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has specific permissions
 * @param {Array} requiredPermissions - Array of required permission keys
 * @returns {Function} Express middleware function
 */
export const requirePermissions = requiredPermissions => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    const userPermissions = req.userPermissions || [];
    const hasRequiredPermission = requiredPermissions.some(permission =>
      userPermissions.includes(permission)
    );

    if (!hasRequiredPermission) {
      return res.status(403).json({
        error: 'Forbidden',
        message: `Access denied. Required permissions: ${requiredPermissions.join(', ')}`
      });
    }

    next();
  };
};

/**
 * Middleware to check if session is LTI-based
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const requireLtiSession = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required'
    });
  }

  if (req.sessionType !== 'lti') {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'LTI session required'
    });
  }

  next();
};

// Export the main middleware as default for backward compatibility
export default jwtAuthMiddleware;
