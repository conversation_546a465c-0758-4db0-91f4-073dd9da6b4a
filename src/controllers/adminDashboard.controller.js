import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import dashboardService from '../services/dashboard.service.js';

let component, auditComponent;
/**
 * @desc    Get admin dashboard data
 * @route   GET /api/admin/dashboard
 * @access  Private (Admin)
 */
export const getAdminDashboard = asyncHandler(
  async (req, res) => {
    component = 'getAdminDashboard';
    auditComponent = 'get Admin Dashboard details';
    const result = await dashboardService.getAdminDasboardDetails(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Successfully fetch the Admin Dashboard details',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);
