import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import enhancedProjectService from '../services/enhancedProject.service.js';
import courseRoleService from '../services/courseRole.service.js';
import logger from '../config/logger.config.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

let component, auditComponent;
/**
 * @desc    Create enhanced project with assignments and template support
 * @route   POST /api/projects/enhanced
 * @access  Private (Instructor/Admin)
 */
export const createEnhancedProject = asyncHandler(
  async (req, res) => {
    component = 'createEnhancedProject';
    auditComponent = 'Create Enhanced Project';
    const result = await enhancedProjectService.creationOfProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Enhanced project created successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get enhanced projects
 * @route   GET /api/projects/enhanced
 * @access  Private (Instructor/Admin/TA/Student)
 */
export const getEnhancedProject = asyncHandler(
  async (req, res) => {
    component = 'getEnhancedProject';
    auditComponent = 'Fetch Enhanced Project';
    const result = await enhancedProjectService.getEnhancedProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Fetch Enhanced project successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Create enhanced project with assignments and template support
 * @route   POST /api/projects/enhanced
 * @access  Private (Instructor/Admin)
 */
export const updateEnhancedProject = asyncHandler(
  async (req, res) => {
    component = 'updateEnhancedProject';
    auditComponent = 'Update Enhanced Project';
    const result = await enhancedProjectService.UpdationOfProject(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Enhanced project updated successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Create project template
 * @route   POST /api/projects/templates
 * @access  Private (Instructor/Admin)
 */
export const createProjectTemplate = asyncHandler(async (req, res) => {
  const {
    projectId,
    templateName,
    templateDescription,
    category = 'general',
    subcategory,
    difficultyLevel,
    estimatedHours,
    totalPoints = 100,
    learningObjectives = [],
    prerequisites = [],
    skillsCovered = [],
    technologiesUsed = [],
    tags = []
  } = req.body;

  // Validate required fields
  if (!projectId || !templateName) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Project ID and template name are required'
    });
  }

  try {
    const templateData = {
      template_name: templateName,
      template_description: templateDescription,
      category,
      subcategory,
      difficulty_level: difficultyLevel,
      estimated_hours: estimatedHours,
      total_points: totalPoints,
      learning_objectives: learningObjectives,
      prerequisites,
      skills_covered: skillsCovered,
      technologies_used: technologiesUsed,
      tags
    };

    const template = await enhancedProjectService.createProjectTemplate(
      projectId,
      templateData,
      req.user.id
    );

    res.status(201).json({
      success: true,
      message: 'Project template created successfully',
      template: {
        id: template.id,
        templateName: template.template_name,
        category: template.category,
        difficultyLevel: template.difficulty_level,
        totalPoints: template.total_points,
        createdAt: template.created_at
      }
    });
  } catch (error) {
    logger.error('Error creating project template:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create project template'
    });
  }
});

/**
 * @desc    Get project templates
 * @route   GET /api/projects/templates
 * @access  Private
 */
export const getProjectTemplates = asyncHandler(async (req, res) => {
  const {
    category,
    subcategory,
    difficultyLevel,
    isFeatured,
    isPublic = 'true',
    page = 1,
    limit = 20,
    search
  } = req.query;

  try {
    const result = await enhancedProjectService.getProjectTemplates({
      category,
      subcategory,
      difficultyLevel,
      isFeatured: isFeatured === 'true',
      isPublic: isPublic === 'true',
      page: parseInt(page),
      limit: parseInt(limit),
      search
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting project templates:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project templates'
    });
  }
});

/**
 * @desc    Assign users to project
 * @route   POST /api/projects/:id/assignments
 * @access  Private (Instructor/Admin)
 */
export const assignUsersToProject = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;
  const { assignments } = req.body;

  if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Assignments array is required'
    });
  }

  try {
    const createdAssignments =
      await enhancedProjectService.assignUsersToProject(
        projectId,
        assignments,
        req.user.id
      );

    res.status(201).json({
      success: true,
      message: `${assignments.length} users assigned to project successfully`,
      assignments: createdAssignments
    });
  } catch (error) {
    logger.error('Error assigning users to project:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to assign users to project'
    });
  }
});

/**
 * @desc    Get project assignments
 * @route   GET /api/projects/:id/assignments
 * @access  Private
 */
export const getProjectAssignments = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;

  try {
    const assignments =
      await enhancedProjectService.getProjectAssignments(projectId);

    res.json({
      success: true,
      assignments
    });
  } catch (error) {
    logger.error('Error getting project assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignments'
    });
  }
});

/**
 * @desc    Remove user assignment from project
 * @route   DELETE /api/projects/:id/assignments/:userId
 * @access  Private (Instructor/Admin)
 */
export const removeUserAssignment = asyncHandler(async (req, res) => {
  const { id: projectId, userId } = req.params;

  try {
    const result = await enhancedProjectService.removeUserAssignment(
      projectId,
      userId
    );

    res.json({
      success: true,
      message: 'User assignment removed successfully',
      data: result
    });
  } catch (error) {
    if (error.message === 'Assignment not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    logger.error('Error removing user assignment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove user assignment'
    });
  }
});

/**
 * @desc    Publish project
 * @route   POST /api/projects/:id/publish
 * @access  Private (Instructor/Admin)
 */
export const publishProject = asyncHandler(
  async (req, res) => {
    component = 'publishProject';
    auditComponent = 'Publish Project';
    const result = await enhancedProjectService.publishProject(
      req.params.id,
      req.user.id,
      req.userRoles
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project published successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Unpublish project
 * @route   POST /api/projects/:id/unpublish
 * @access  Private (Instructor/Admin)
 */
export const unpublishProject = asyncHandler(
  async (req, res) => {
    component = 'unpublishProject';
    auditComponent = 'Unpublish Project';
    const result = await enhancedProjectService.unpublishProject(
      req.params.id,
      req.user.id,
      req.userRoles
    );
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project unpublished successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Save project as draft
 * @route   POST /api/projects/:id/save-draft
 * @access  Private (Instructor/Admin)
 */
export const saveProjectAsDraft = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;

  try {
    const project = await enhancedProjectService.saveProjectAsDraft(
      projectId,
      req.user.id
    );

    res.json({
      success: true,
      message: 'Project saved as draft successfully',
      project: {
        id: project.id,
        title: project.title,
        status: project.status
      }
    });
  } catch (error) {
    if (error.message === 'Project not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project not found'
      });
    }

    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }

    logger.error('Error saving project as draft:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to save project as draft'
    });
  }
});

/**
 * @desc    Get project with full details
 * @route   GET /api/projects/:id/details
 * @access  Private
 */
export const getProjectWithDetails = asyncHandler(
  async (req, res) => {
    component = 'getProjectWithDetails';
    auditComponent = 'Fetch Enhanced Project with Details';
    const result = await enhancedProjectService.getProjectWithDetails(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Fetch Enhanced project with details successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Duplicate project from template
 * @route   POST /api/projects/templates/:id/duplicate
 * @access  Private (Instructor/Admin)
 */
export const duplicateProjectFromTemplate = asyncHandler(async (req, res) => {
  const { id: templateId } = req.params;
  const newProjectData = req.body;

  if (!newProjectData.courseId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Course ID is required for new project'
    });
  }

  try {
    const newProject =
      await enhancedProjectService.duplicateProjectFromTemplate(
        templateId,
        { ...newProjectData, created_by: req.user.id },
        req.user.id
      );

    res.status(201).json({
      success: true,
      message: 'Project duplicated from template successfully',
      project: {
        id: newProject.id,
        title: newProject.title,
        status: newProject.status,
        courseId: newProject.course_id,
        createdAt: newProject.created_at
      }
    });
  } catch (error) {
    if (error.message === 'Template not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Template not found'
      });
    }

    logger.error('Error duplicating project from template:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to duplicate project from template'
    });
  }
});

/**
 * @desc    Rate project template
 * @route   POST /api/projects/templates/:id/rate
 * @access  Private
 */
export const rateProjectTemplate = asyncHandler(async (req, res) => {
  const { id: templateId } = req.params;
  const { rating } = req.body;

  if (!rating || rating < 0 || rating > 5) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Rating must be between 0 and 5'
    });
  }

  try {
    const template = await enhancedProjectService.rateProjectTemplate(
      templateId,
      rating,
      req.user.id
    );

    res.json({
      success: true,
      message: 'Template rated successfully',
      template: {
        id: template.id,
        templateName: template.template_name,
        rating: template.rating,
        ratingCount: template.rating_count
      }
    });
  } catch (error) {
    if (error.message === 'Template not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Template not found'
      });
    }

    logger.error('Error rating project template:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to rate project template'
    });
  }
});

/**
 * @desc    Get projects by user assignment
 * @route   GET /api/projects/assignments/user/:role?
 * @access  Private
 */
export const getProjectsByUserAssignment = asyncHandler(async (req, res) => {
  try {
    const { role } = req.params;
    const userId = req.user.id;

    const assignments =
      await enhancedProjectService.getProjectsByUserAssignment(userId, role);

    res.json({
      success: true,
      message: 'User project assignments retrieved successfully',
      data: {
        assignments,
        total: assignments.length,
        role: role || 'all'
      }
    });
  } catch (error) {
    logger.error('Error getting user project assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user project assignments'
    });
  }
});

/**
 * @desc    Get user's project workload
 * @route   GET /api/projects/workload
 * @access  Private
 */
export const getUserProjectWorkload = asyncHandler(async (req, res) => {
  try {
    const userId = req.user.id;

    const workload =
      await enhancedProjectService.getUserProjectWorkload(userId);

    res.json({
      success: true,
      message: 'User project workload retrieved successfully',
      data: workload
    });
  } catch (error) {
    logger.error('Error getting user project workload:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user project workload'
    });
  }
});

/**
 * @desc    Get project assignment statistics
 * @route   GET /api/projects/:id/assignment-stats
 * @access  Private
 */
export const getProjectAssignmentStats = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const stats = await enhancedProjectService.getProjectAssignmentStats(id);

    res.json({
      success: true,
      message: 'Project assignment statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    logger.error('Error getting project assignment stats:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignment statistics'
    });
  }
});
