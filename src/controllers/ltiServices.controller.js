import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import { requireLtiSession, requireLtiRole, requireContextAccess } from '../middlewares/session.middlewares.js';
import nrpsService from '../services/nrpsService.js';
import agsService from '../services/agsService.js';
import ltiService from '../services/lti.service.js';
import logger from '../config/logger.config.js';

/**
 * LTI Services Controller
 * 
 * Handles NRPS (roster) and AGS (grades) operations with proper
 * role-based access control and IMS media types.
 */

// ============================================================================
// NRPS (Names and Role Provisioning Services) Endpoints
// ============================================================================

/**
 * @desc    Get course roster (all members)
 * @route   GET /api/lti/roster
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const getCourseRoster = asyncHandler(async (req, res) => {
  try {
    const { contextId, resourceLinkId, role, limit } = req.query;
    const session = req.session;

    logger.info('[LTI Services] getCourseRoster: Starting', {
      contextId: contextId || session.context_id,
      resourceLinkId: resourceLinkId || session.resource_link_id,
      role: role || 'all',
      limit: limit || 'none'
    });

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse NRPS claim from session (would be stored during launch)
    const nrpsConfig = nrpsService.parseNrpsClaim(session);
    if (!nrpsConfig) {
      return res.status(400).json({
        error: 'NRPS Not Available',
        message: 'NRPS service not available for this context'
      });
    }

    // Use context ID from query or session
    const targetContextId = contextId || session.context_id;
    if (!targetContextId) {
      return res.status(400).json({
        error: 'Context Required',
        message: 'Context ID is required'
      });
    }

    // Get roster with options
    const result = await nrpsService.listMembers(
      targetContextId,
      nrpsConfig.contextMembershipsUrl,
      platform,
      {
        resourceLinkId: resourceLinkId || session.resource_link_id,
        role,
        limit: limit ? parseInt(limit) : undefined,
        deploymentId: session.deployment_id
      }
    );

    res.json(result);

  } catch (error) {
    logger.error('[LTI Services] getCourseRoster: Failed', {
      error: error.message,
      contextId: req.query.contextId,
      sessionId: req.sessionID
    });

    res.status(500).json({
      error: 'Roster Retrieval Failed',
      message: error.message
    });
  }
});

/**
 * @desc    Get course instructors
 * @route   GET /api/lti/roster/instructors
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const getInstructors = asyncHandler(async (req, res) => {
  try {
    const { contextId, limit } = req.query;
    const session = req.session;

    logger.info('[LTI Services] getInstructors: Starting', {
      contextId: contextId || session.context_id,
      limit: limit || 'none'
    });

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse NRPS claim
    const nrpsConfig = nrpsService.parseNrpsClaim(session);
    if (!nrpsConfig) {
      return res.status(400).json({
        error: 'NRPS Not Available',
        message: 'NRPS service not available for this context'
      });
    }

    const targetContextId = contextId || session.context_id;
    if (!targetContextId) {
      return res.status(400).json({
        error: 'Context Required',
        message: 'Context ID is required'
      });
    }

    // Get instructors
    const result = await nrpsService.getInstructors(
      targetContextId,
      nrpsConfig.contextMembershipsUrl,
      platform,
      {
        limit: limit ? parseInt(limit) : undefined,
        deploymentId: session.deployment_id
      }
    );

    res.json(result);

  } catch (error) {
    logger.error('[LTI Services] getInstructors: Failed', {
      error: error.message,
      contextId: req.query.contextId
    });

    res.status(500).json({
      error: 'Instructors Retrieval Failed',
      message: error.message
    });
  }
});

/**
 * @desc    Get course students
 * @route   GET /api/lti/roster/students
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const getStudents = asyncHandler(async (req, res) => {
  try {
    const { contextId, limit } = req.query;
    const session = req.session;

    logger.info('[LTI Services] getStudents: Starting', {
      contextId: contextId || session.context_id,
      limit: limit || 'none'
    });

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse NRPS claim
    const nrpsConfig = nrpsService.parseNrpsClaim(session);
    if (!nrpsConfig) {
      return res.status(400).json({
        error: 'NRPS Not Available',
        message: 'NRPS service not available for this context'
      });
    }

    const targetContextId = contextId || session.context_id;
    if (!targetContextId) {
      return res.status(400).json({
        error: 'Context Required',
        message: 'Context ID is required'
      });
    }

    // Get students
    const result = await nrpsService.getStudents(
      targetContextId,
      nrpsConfig.contextMembershipsUrl,
      platform,
      {
        limit: limit ? parseInt(limit) : undefined,
        deploymentId: session.deployment_id
      }
    );

    res.json(result);

  } catch (error) {
    logger.error('[LTI Services] getStudents: Failed', {
      error: error.message,
      contextId: req.query.contextId
    });

    res.status(500).json({
      error: 'Students Retrieval Failed',
      message: error.message
    });
  }
});

// ============================================================================
// AGS (Assignment and Grade Services) Endpoints
// ============================================================================

/**
 * @desc    Create or get line item
 * @route   POST /api/lti/lineitems
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const createLineItem = asyncHandler(async (req, res) => {
  try {
    const { label, scoreMaximum, resourceLinkId, tag } = req.body;
    const session = req.session;

    logger.info('[LTI Services] createLineItem: Starting', {
      label,
      scoreMaximum,
      resourceLinkId: resourceLinkId || session.resource_link_id,
      tag: tag || 'none'
    });

    // Validate required fields
    if (!label || !scoreMaximum) {
      return res.status(400).json({
        error: 'Missing Required Fields',
        message: 'Label and scoreMaximum are required'
      });
    }

    if (scoreMaximum <= 0) {
      return res.status(400).json({
        error: 'Invalid Score Maximum',
        message: 'Score maximum must be greater than 0'
      });
    }

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse AGS claim
    const agsConfig = agsService.parseAgsClaim(session);
    if (!agsConfig) {
      return res.status(400).json({
        error: 'AGS Not Available',
        message: 'AGS service not available for this context'
      });
    }

    // Ensure line item exists
    const result = await agsService.ensureLineItem(
      label,
      scoreMaximum,
      agsConfig.lineItems,
      platform,
      {
        resourceLinkId: resourceLinkId || session.resource_link_id,
        tag,
        deploymentId: session.deployment_id
      }
    );

    res.status(201).json({
      success: true,
      lineItem: result,
      message: 'Line item created successfully'
    });

  } catch (error) {
    logger.error('[LTI Services] createLineItem: Failed', {
      error: error.message,
      label: req.body.label,
      scoreMaximum: req.body.scoreMaximum
    });

    res.status(500).json({
      error: 'Line Item Creation Failed',
      message: error.message
    });
  }
});

/**
 * @desc    List line items
 * @route   GET /api/lti/lineitems
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const listLineItems = asyncHandler(async (req, res) => {
  try {
    const { resourceLinkId, tag } = req.query;
    const session = req.session;

    logger.info('[LTI Services] listLineItems: Starting', {
      resourceLinkId: resourceLinkId || session.resource_link_id,
      tag: tag || 'none'
    });

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse AGS claim
    const agsConfig = agsService.parseAgsClaim(session);
    if (!agsConfig) {
      return res.status(400).json({
        error: 'AGS Not Available',
        message: 'AGS service not available for this context'
      });
    }

    // List line items
    const result = await agsService.listLineItems(
      agsConfig.lineItems,
      platform,
      {
        resourceLinkId: resourceLinkId || session.resource_link_id,
        tag,
        deploymentId: session.deployment_id
      }
    );

    res.json(result);

  } catch (error) {
    logger.error('[LTI Services] listLineItems: Failed', {
      error: error.message,
      resourceLinkId: req.query.resourceLinkId
    });

    res.status(500).json({
      error: 'Line Items Retrieval Failed',
      message: error.message
    });
  }
});

/**
 * @desc    Post score to line item
 * @route   POST /api/lti/lineitems/:lineItemId/scores
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const postScore = asyncHandler(async (req, res) => {
  try {
    const { lineItemId } = req.params;
    const { userSub, scoreGiven, gradingProgress, activityProgress, scoreMaximum, comment } = req.body;
    const session = req.session;

    logger.info('[LTI Services] postScore: Starting', {
      lineItemId,
      userSub,
      scoreGiven,
      gradingProgress,
      activityProgress,
      scoreMaximum: scoreMaximum || 'none',
      comment: comment || 'none'
    });

    // Validate required fields
    if (!userSub || scoreGiven === undefined || !gradingProgress || !activityProgress) {
      return res.status(400).json({
        error: 'Missing Required Fields',
        message: 'userSub, scoreGiven, gradingProgress, and activityProgress are required'
      });
    }

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse AGS claim
    const agsConfig = agsService.parseAgsClaim(session);
    if (!agsConfig) {
      return res.status(400).json({
        error: 'AGS Not Available',
        message: 'AGS service not available for this context'
      });
    }

    // Post score
    const result = await agsService.postScore(
      userSub,
      scoreGiven,
      gradingProgress,
      activityProgress,
      lineItemId,
      platform,
      {
        scoreMaximum,
        comment,
        deploymentId: session.deployment_id
      }
    );

    res.status(201).json(result);

  } catch (error) {
    logger.error('[LTI Services] postScore: Failed', {
      error: error.message,
      lineItemId: req.params.lineItemId,
      userSub: req.body.userSub,
      scoreGiven: req.body.scoreGiven
    });

    res.status(500).json({
      error: 'Score Submission Failed',
      message: error.message
    });
  }
});

/**
 * @desc    List results for line item
 * @route   GET /api/lti/lineitems/:lineItemId/results
 * @access  Private (requires LTI session)
 */
export const listResults = asyncHandler(async (req, res) => {
  try {
    const { lineItemId } = req.params;
    const { userId } = req.query;
    const session = req.session;

    logger.info('[LTI Services] listResults: Starting', {
      lineItemId,
      userId: userId || 'all'
    });

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse AGS claim
    const agsConfig = agsService.parseAgsClaim(session);
    if (!agsConfig) {
      return res.status(400).json({
        error: 'AGS Not Available',
        message: 'AGS service not available for this context'
      });
    }

    // List results
    const result = await agsService.listResults(
      lineItemId,
      platform,
      {
        userId,
        deploymentId: session.deployment_id
      }
    );

    res.json(result);

  } catch (error) {
    logger.error('[LTI Services] listResults: Failed', {
      error: error.message,
      lineItemId: req.params.lineItemId,
      userId: req.query.userId
    });

    res.status(500).json({
      error: 'Results Retrieval Failed',
      message: error.message
    });
  }
});

/**
 * @desc    Get user's own results for line item (learner access)
 * @route   GET /api/lti/lineitems/:lineItemId/my-results
 * @access  Private (requires LTI session, any role)
 */
export const getMyResults = asyncHandler(async (req, res) => {
  try {
    const { lineItemId } = req.params;
    const session = req.session;

    logger.info('[LTI Services] getMyResults: Starting', {
      lineItemId,
      userSub: session.sub
    });

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse AGS claim
    const agsConfig = agsService.parseAgsClaim(session);
    if (!agsConfig) {
      return res.status(400).json({
        error: 'AGS Not Available',
        message: 'AGS service not available for this context'
      });
    }

    // Get results for current user only
    const result = await agsService.listResults(
      lineItemId,
      platform,
      {
        userId: session.sub, // Only get results for current user
        deploymentId: session.deployment_id
      }
    );

    res.json(result);

  } catch (error) {
    logger.error('[LTI Services] getMyResults: Failed', {
      error: error.message,
      lineItemId: req.params.lineItemId,
      userSub: req.session.sub
    });

    res.status(500).json({
      error: 'My Results Retrieval Failed',
      message: error.message
    });
  }
});

// ============================================================================
// Service Status Endpoints
// ============================================================================

/**
 * @desc    Get NRPS service status
 * @route   GET /api/lti/services/nrps/status
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const getNrpsStatus = asyncHandler(async (req, res) => {
  try {
    const session = req.session;

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse NRPS claim
    const nrpsConfig = nrpsService.parseNrpsClaim(session);
    if (!nrpsConfig) {
      return res.status(400).json({
        error: 'NRPS Not Available',
        message: 'NRPS service not available for this context'
      });
    }

    // Get service status
    const status = await nrpsService.getServiceStatus(
      nrpsConfig.contextMembershipsUrl,
      platform
    );

    res.json({
      success: true,
      service: 'NRPS',
      status
    });

  } catch (error) {
    logger.error('[LTI Services] getNrpsStatus: Failed', {
      error: error.message
    });

    res.status(500).json({
      error: 'NRPS Status Check Failed',
      message: error.message
    });
  }
});

/**
 * @desc    Get AGS service status
 * @route   GET /api/lti/services/ags/status
 * @access  Private (requires LTI session, Instructor/TA roles)
 */
export const getAgsStatus = asyncHandler(async (req, res) => {
  try {
    const session = req.session;

    // Get platform configuration
    const platform = await ltiService.getPlatformByIssuer(session.iss);
    if (!platform) {
      return res.status(400).json({
        error: 'Platform Not Found',
        message: 'LTI platform configuration not found'
      });
    }

    // Parse AGS claim
    const agsConfig = agsService.parseAgsClaim(session);
    if (!agsConfig) {
      return res.status(400).json({
        error: 'AGS Not Available',
        message: 'AGS service not available for this context'
      });
    }

    // Get service status
    const status = await agsService.getServiceStatus(
      agsConfig.lineItems,
      platform
    );

    res.json({
      success: true,
      service: 'AGS',
      status
    });

  } catch (error) {
    logger.error('[LTI Services] getAgsStatus: Failed', {
      error: error.message
    });

    res.status(500).json({
      error: 'AGS Status Check Failed',
      message: error.message
    });
  }
});

export default {
  // NRPS endpoints
  getCourseRoster,
  getInstructors,
  getStudents,
  
  // AGS endpoints
  createLineItem,
  listLineItems,
  postScore,
  listResults,
  getMyResults,
  
  // Service status
  getNrpsStatus,
  getAgsStatus
};

