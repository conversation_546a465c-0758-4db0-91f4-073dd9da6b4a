import httpStatus from 'http-status';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import S3Service from '../services/s3.service.js';

let component, auditComponent;
/**
 * @desc    Upload project dataset file
 * @route   POST /api/s3/upload-project-dataset
 * @access  Private (Instructor/Admin)
 * @form    multipart/form-data with field name: dataset
 */
export const uploadProjectDataset = asyncHandler(
  async (req, res) => {
    component = 'uploadProjectDataset';
    auditComponent = 'Upload Project Dataset';
    const result = await S3Service.uploadProjectDataset(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Successfully Upload Project Dataset File',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);
