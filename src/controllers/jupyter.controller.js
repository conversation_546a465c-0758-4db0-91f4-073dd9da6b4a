import logger from '../config/logger.config.js';
import jupyterService from '../services/jupyterhub.service.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';

let component, auditComponent;

const startJupyterServer = asyncHandler(
  async (req, res) => {
    component = 'startJupyterServer';
    auditComponent = 'Start Jupyter Server';
    const user = req.user;
    logger.info(`Starting Jupyter server for user: ${user?.id}`);
    const response = await jupyterService.ensureServerIsRunning(user);
    await buildSuccessResponse(
      req,
      res,
      response,
      'Jupyter server started successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

const stopJupyterServer = asyncHandler(
  async (req, res) => {
    component = 'stopJupyterServer';
    auditComponent = 'Stop Jupyter Server';
    const user = req.user;
    logger.info(`Stopping Jupyter server for user: ${user?.id}`);
    const response = await jupyterService.stopServer(user);
    await buildSuccessResponse(
      req,
      res,
      response,
      'Jupyter server stopped successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

const getJupyterToken = asyncHandler(
  async (req, res) => {
    component = 'getJupyterToken';
    auditComponent = 'Get Jupyter Token';
    const user = req.user;
    logger.info(`Getting Jupyter token for user: ${user?.id}`);
    const userToken = await jupyterService.getUserToken(user.jupiterUserName);
    const data = {
      userName: user.jupiterUserName,
      userToken
    };
    await buildSuccessResponse(
      req,
      res,
      data,
      'Jupyter token retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

const jupyterController = {
  startJupyterServer,
  stopJupyterServer,
  getJupyterToken
};

export const executeCode = asyncHandler(
  async (req, res) => {
    component = 'executeCode';
    auditComponent = 'Execute Code Cell';
    const { kernelId } = req.params;
    const { code, timeout = 60000 } = req.body;

    const options = {
      username: req.user?.jupiterUserName || 'anonymous',
      userToken: req.user?.jupyterUserToken || null,
      session: req.user?.id ? `user_${req.user.id}` : `session_${Date.now()}`,
      timeout,
      silent: req.body.silent || false,
      store_history: req.body.store_history !== false,
      user_expressions: req.body.user_expressions || {},
      allow_stdin: req.body.allow_stdin || false,
      stop_on_error: req.body.stop_on_error !== false
    };

    const data = await jupyterService.executeCode(kernelId, code, options);

    await buildSuccessResponse(
      req,
      res,
      data,
      'Code executed successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * Execute all cells in a notebook sequentially.
 */
export const executeNotebook = asyncHandler(
  async (req, res) => {
    component = 'executeNotebook';
    auditComponent = 'Execute Notebook';
    const { kernelId } = req.params;
    const { notebook, timeout = 60000, stopOnError = true } = req.body;

    const options = {
      username: req.user?.jupiterUserName || 'anonymous',
      userToken: req.user?.jupyterUserToken || null,
      session: req.user?.id ? `user_${req.user.id}` : `session_${Date.now()}`,
      timeout,
      silent: false,
      store_history: true,
      user_expressions: {},
      allow_stdin: false,
      stop_on_error: stopOnError
    };

    const data = await jupyterService.executeNotebook(
      kernelId,
      notebook,
      options
    );

    await buildSuccessResponse(
      req,
      res,
      data,
      'Notebook executed successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * Create a per-project workspace: ensure server, create /:projectId folder,
 * create Untitled.ipynb, and start a session with kernel name python3.
 * Returns server, session, kernel and paths.
 */
export const createWorkspace = asyncHandler(
  async (req, res) => {
    component = 'createWorkspace';
    auditComponent = 'Create Jupyter Workspace';
    const { projectId } = req.params;
    const user = req.user;
    const token = req.user?.jupyterUserToken;

    const response = await jupyterService.createWorkspace(projectId, user);

    await buildSuccessResponse(
      req,
      res,
      response,
      'Jupyter workspace created successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);
export { jupyterController };

export default jupyterController;
