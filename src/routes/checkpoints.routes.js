import express from 'express';
import {
  createCheckpoint,
  getProjectCheckpoints,
  getCheckpointDetails,
  updateCheckpointProgress,
  submitCheckpoint,
  gradeCheckpoint,
  getProjectProgress,
  getInstructorDashboard,
  getStudentProgressOverview,
  getCheckpointAnalytics,
  updateCheckpoint,
  deleteCheckpoint
} from '../controllers/checkpoint.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { validate } from '../middlewares/validation.middlewares.js';
import { body, query, param } from 'express-validator';

const STATUS = ['draft', 'published', 'archived'];
const GOAL_TYPES = ['file_upload', 'text', 'link', 'quiz', 'code', 'custom'];

const router = express.Router();

// Apply JWT authentication to all routes
router.use(jwtMiddleware);

// ---------- CREATE CHECKPOINT (POST) ----------
router.post(
  '/',
  [
    requirePermissions(['create_projects']),
    body()
      .isArray({ min: 1 })
      .withMessage(
        'Request body must be a non-empty array of checkpoint objects'
      ),

    // Checkpoint fields
    body('*.project_id')
      .isUUID()
      .withMessage('project_id must be a valid UUID'),
    body('*.isScreen')
      .exists({ checkFalsy: true })
      .withMessage('isScreen is required')
      .isInt({ min: 1, max: 4 })
      .withMessage('isScreen must be a number between 1 and 4'),
    body('*.title')
      .trim()
      .notEmpty()
      .withMessage('title is required')
      .isLength({ min: 2, max: 255 })
      .withMessage('title must be 2–255 chars'),
    body('*.description')
      .optional({ nullable: true })
      .isString()
      .withMessage('description must be a string'),
    body('*.checkpoint_number')
      .isInt({ min: 1 })
      .withMessage('checkpoint_number must be an integer ≥ 1')
      .toInt(),
    body('*.start_date')
      .isISO8601()
      .withMessage('start_date must be ISO8601 date'),
    body('*.due_date').isISO8601().withMessage('due_date must be ISO8601 date'),
    body('*.weight_percentage')
      .isFloat({ min: 0, max: 100 })
      .withMessage('weight_percentage must be between 0 and 100')
      .toFloat(),
    body('*.is_required')
      .isBoolean()
      .withMessage('is_required must be boolean')
      .toBoolean(),
    body('*.status')
      .optional()
      .customSanitizer(v => String(v || '').toLowerCase())
      .isIn(STATUS)
      .withMessage(`status must be one of: ${STATUS.join(' | ')}`),
    body('*.metadata')
      .optional({ nullable: true })
      .isObject()
      .withMessage('metadata must be an object'),

    // ---------- goals (checkpoint_goals) ----------
  /*   body('*.goals')
      .isArray({ min: 1 })
      .withMessage('goals must be a non-empty array'),
    body('*.goals.*.title')
      .trim()
      .notEmpty()
      .withMessage('Each goal must have a title')
      .isLength({ min: 2, max: 255 })
      .withMessage('goal.title must be 2–255 chars'),
    body('*.goals.*.goal_type')
      .customSanitizer(v => String(v || '').toLowerCase())
      .isIn(GOAL_TYPES)
      .withMessage(`goal_type must be one of: ${GOAL_TYPES.join(' | ')}`),
    body('*.goals.*.description')
      .optional({ nullable: true })
      .isString()
      .withMessage('goal.description must be a string'),
    body('*.goals.*.required_files')
      .optional({ nullable: true })
      .isArray()
      .withMessage('required_files must be an array'),
    body('*.goals.*.required_files.*')
      .optional()
      .isString()
      .trim()
      .notEmpty()
      .withMessage('required_files items must be non-empty strings'),
    body('*.goals.*.completion_criteria')
      .optional({ nullable: true })
      .isArray()
      .withMessage('completion_criteria must be an array'),
    body('*.goals.*.completion_criteria.*.description')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Each completion_criteria item must have a description'),
    body('*.goals.*.points')
      .isFloat({ min: 0 })
      .withMessage('points must be a number ≥ 0')
      .toFloat(),
    body('*.goals.*.order_index')
      .isInt({ min: 0 })
      .withMessage('order_index must be an integer ≥ 0')
      .toInt(),
    body('*.goals.*.is_required')
      .isBoolean()
      .withMessage('goal.is_required must be boolean')
      .toBoolean(),
    body('*.goals.*.estimated_time_minutes')
      .optional({ nullable: true })
      .isInt({ min: 0 })
      .withMessage('estimated_time_minutes must be an integer ≥ 0')
      .toInt(),
    body('*.goals.*.metadata')
      .optional({ nullable: true })
      .isObject()
      .withMessage('goal.metadata must be an object') */
  ],
  validate,
  createCheckpoint
);

router.get('/:id', requirePermissions(['view_projects']), getCheckpointDetails);

router.put(
  '/:id',
  [
    requirePermissions(['edit_projects']),
    param('id').isUUID().withMessage('Checkpoint ID must be a valid UUID'),

    // Checkpoint fields
    body('project_id').isUUID().withMessage('project_id must be a valid UUID'),
    body('isScreen')
      .exists({ checkFalsy: true })
      .withMessage('isScreen is required')
      .isInt({ min: 1, max: 4 })
      .withMessage('isScreen must be a number between 1 and 4'),
    body('title')
      .trim()
      .notEmpty()
      .withMessage('title is required')
      .isLength({ min: 2, max: 255 })
      .withMessage('title must be 2–255 chars'),
    body('description')
      .optional({ nullable: true })
      .isString()
      .withMessage('description must be a string'),
    body('checkpoint_number')
      .isInt({ min: 1 })
      .withMessage('checkpoint_number must be an integer ≥ 1')
      .toInt(),
    body('start_date')
      .isISO8601()
      .withMessage('start_date must be ISO8601 date'),
    body('due_date').isISO8601().withMessage('due_date must be ISO8601 date'),
    body('weight_percentage')
      .isFloat({ min: 0, max: 100 })
      .withMessage('weight_percentage must be between 0 and 100')
      .toFloat(),
    body('is_required')
      .isBoolean()
      .withMessage('is_required must be boolean')
      .toBoolean(),
    body('status')
      .optional()
      .customSanitizer(v => String(v || '').toLowerCase())
      .isIn(STATUS)
      .withMessage(`status must be one of: ${STATUS.join(' | ')}`),
    body('metadata')
      .optional({ nullable: true })
      .isObject()
      .withMessage('metadata must be an object'),

/*     // ---------- goals (checkpoint_goals) ----------
    body('goals')
      .isArray({ min: 1 })
      .withMessage('goals must be a non-empty array'),
    body('goals.*.title')
      .trim()
      .notEmpty()
      .withMessage('Each goal must have a title')
      .isLength({ min: 2, max: 255 })
      .withMessage('goal.title must be 2–255 chars'),
    body('goals.*.goal_type')
      .customSanitizer(v => String(v || '').toLowerCase())
      .isIn(GOAL_TYPES)
      .withMessage(`goal_type must be one of: ${GOAL_TYPES.join(' | ')}`),
    body('goals.*.description')
      .optional({ nullable: true })
      .isString()
      .withMessage('goal.description must be a string'),
    body('goals.*.required_files')
      .optional({ nullable: true })
      .isArray()
      .withMessage('required_files must be an array'),
    body('goals.*.required_files.*')
      .optional()
      .isString()
      .trim()
      .notEmpty()
      .withMessage('required_files items must be non-empty strings'),
    body('goals.*.completion_criteria')
      .optional({ nullable: true })
      .isArray()
      .withMessage('completion_criteria must be an array'),
    body('goals.*.completion_criteria.*.description')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Each completion_criteria item must have a description'),
    body('goals.*.points')
      .isFloat({ min: 0 })
      .withMessage('points must be a number ≥ 0')
      .toFloat(),
    body('goals.*.order_index')
      .isInt({ min: 0 })
      .withMessage('order_index must be an integer ≥ 0')
      .toInt(),
    body('goals.*.is_required')
      .isBoolean()
      .withMessage('goal.is_required must be boolean')
      .toBoolean(),
    body('goals.*.estimated_time_minutes')
      .optional({ nullable: true })
      .isInt({ min: 0 })
      .withMessage('estimated_time_minutes must be an integer ≥ 0')
      .toInt(),
    body('goals.*.metadata')
      .optional({ nullable: true })
      .isObject()
      .withMessage('goal.metadata must be an object') */
  ],
  validate,
  updateCheckpoint
);

router.delete(
  '/:id',
  requirePermissions(['delete_projects']),
  deleteCheckpoint
);

// Checkpoint progress routes
router.put(
  '/:id/progress',
  requirePermissions(['submit_assignments']),
  updateCheckpointProgress
);

router.post(
  '/:id/submit',
  requirePermissions(['submit_assignments']),
  submitCheckpoint
);

router.post(
  '/:id/grade',
  requirePermissions(['grade_submissions']),
  gradeCheckpoint
);

// Analytics routes
router.get(
  '/:id/analytics',
  requirePermissions(['view_projects']),
  getCheckpointAnalytics
);

// Project checkpoints route (moved to projects.js for better organization)
// GET /api/projects/:id/checkpoints

// Project progress route (moved to projects.js for better organization)
// GET /api/projects/:id/progress

// Instructor dashboard routes
router.get(
  '/instructor/courses/:id/dashboard',
  requirePermissions(['view_projects']),
  getInstructorDashboard
);

router.get(
  '/instructor/courses/:id/progress-overview',
  requirePermissions(['view_projects']),
  getStudentProgressOverview
);

export default router;
