import express from 'express';
import {
  // NRPS endpoints
  getCourseRoster,
  getInstructors,
  getStudents,
  
  // AGS endpoints
  createLineItem,
  listLineItems,
  postScore,
  listResults,
  getMyResults,
  
  // Service status
  getNrpsStatus,
  getAgsStatus
} from '../controllers/ltiServices.controller.js';
import { 
  requireLtiSession, 
  requireLtiRole,
  trackSessionActivity,
  addSessionSecurityHeaders 
} from '../middlewares/session.middlewares.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: LTI Services
 *   description: NRPS (roster) and AGS (grades) operations
 */

// ============================================================================
// NRPS (Names and Role Provisioning Services) Routes
// ============================================================================

/**
 * @swagger
 * /api/lti/roster:
 *   get:
 *     summary: Get course roster (all members)
 *     tags: [LTI Services]
 *     description: Retrieve all members from the course roster via NRPS
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: contextId
 *         schema:
 *           type: string
 *         description: LTI context ID (defaults to session context)
 *       - in: query
 *         name: resourceLinkId
 *         schema:
 *           type: string
 *         description: Resource link ID filter
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *         description: Role filter (e.g., Learner, Instructor)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of members to return
 *     responses:
 *       200:
 *         description: Course roster retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 contextId:
 *                   type: string
 *                 members:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       user_id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       email:
 *                         type: string
 *                       roles:
 *                         type: array
 *                         items:
 *                           type: string
 *                       status:
 *                         type: string
 *                 totalMembers:
 *                   type: integer
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Bad request (missing NRPS claim or context)
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/roster',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  getCourseRoster
);

/**
 * @swagger
 * /api/lti/roster/instructors:
 *   get:
 *     summary: Get course instructors
 *     tags: [LTI Services]
 *     description: Retrieve only instructors from the course roster
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: contextId
 *         schema:
 *           type: string
 *         description: LTI context ID (defaults to session context)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of instructors to return
 *     responses:
 *       200:
 *         description: Instructors retrieved successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/roster/instructors',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  getInstructors
);

/**
 * @swagger
 * /api/lti/roster/students:
 *   get:
 *     summary: Get course students
 *     tags: [LTI Services]
 *     description: Retrieve only students from the course roster
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: contextId
 *         schema:
 *           type: string
 *         description: LTI context ID (defaults to session context)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of students to return
 *     responses:
 *       200:
 *         description: Students retrieved successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/roster/students',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  getStudents
);

// ============================================================================
// AGS (Assignment and Grade Services) Routes
// ============================================================================

/**
 * @swagger
 * /api/lti/lineitems:
 *   post:
 *     summary: Create or get line item
 *     tags: [LTI Services]
 *     description: Create a new line item or get existing one with same label and resource link
 *     security:
 *       - sessionAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - label
 *               - scoreMaximum
 *             properties:
 *               label:
 *                 type: string
 *                 description: Line item label/title
 *                 example: "Project 1 - Data Analysis"
 *               scoreMaximum:
 *                 type: number
 *                 description: Maximum possible score
 *                 example: 100
 *               resourceLinkId:
 *                 type: string
 *                 description: Resource link ID (defaults to session resource link)
 *               tag:
 *                 type: string
 *                 description: Optional tag for categorization
 *     responses:
 *       201:
 *         description: Line item created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 lineItem:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     label:
 *                       type: string
 *                     scoreMaximum:
 *                       type: number
 *                     resourceLinkId:
 *                       type: string
 *                     tag:
 *                       type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request (missing required fields or invalid data)
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.post('/lineitems',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  createLineItem
);

/**
 * @swagger
 * /api/lti/lineitems:
 *   get:
 *     summary: List line items
 *     tags: [LTI Services]
 *     description: Retrieve all line items for the context
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: resourceLinkId
 *         schema:
 *           type: string
 *         description: Resource link ID filter
 *       - in: query
 *         name: tag
 *         schema:
 *           type: string
 *         description: Tag filter
 *     responses:
 *       200:
 *         description: Line items retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 lineItems:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       label:
 *                         type: string
 *                       scoreMaximum:
 *                         type: number
 *                       resourceLinkId:
 *                         type: string
 *                       tag:
 *                         type: string
 *                 totalLineItems:
 *                   type: integer
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/lineitems',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  listLineItems
);

/**
 * @swagger
 * /api/lti/lineitems/{lineItemId}/scores:
 *   post:
 *     summary: Post score to line item
 *     tags: [LTI Services]
 *     description: Submit a score for a specific user to a line item
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: path
 *         name: lineItemId
 *         required: true
 *         schema:
 *           type: string
 *         description: Line item ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userSub
 *               - scoreGiven
 *               - gradingProgress
 *               - activityProgress
 *             properties:
 *               userSub:
 *                 type: string
 *                 description: User subject (LTI user ID)
 *                 example: "user123"
 *               scoreGiven:
 *                 type: number
 *                 description: Score given to user
 *                 example: 85
 *               gradingProgress:
 *                 type: string
 *                 enum: [NotStarted, InProgress, Completed, InReview, Failed]
 *                 description: Grading progress status
 *                 example: "Completed"
 *               activityProgress:
 *                 type: string
 *                 enum: [Initialized, Started, InProgress, Submitted, Completed]
 *                 description: Activity progress status
 *                 example: "Completed"
 *               scoreMaximum:
 *                 type: number
 *                 description: Maximum score (optional, defaults to line item maximum)
 *                 example: 100
 *               comment:
 *                 type: string
 *                 description: Optional comment
 *                 example: "Great work on the analysis!"
 *     responses:
 *       201:
 *         description: Score submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 scoreId:
 *                   type: string
 *                 userId:
 *                   type: string
 *                 scoreGiven:
 *                   type: number
 *                 gradingProgress:
 *                   type: string
 *                 activityProgress:
 *                   type: string
 *                 submittedAt:
 *                   type: string
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Bad request (missing required fields or invalid progress values)
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.post('/lineitems/:lineItemId/scores',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  postScore
);

/**
 * @swagger
 * /api/lti/lineitems/{lineItemId}/results:
 *   get:
 *     summary: List results for line item
 *     tags: [LTI Services]
 *     description: Retrieve all results (scores) for a specific line item
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: path
 *         name: lineItemId
 *         required: true
 *         schema:
 *           type: string
 *         description: Line item ID
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: User ID filter (optional)
 *     responses:
 *       200:
 *         description: Results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 lineItemId:
 *                   type: string
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       scoreGiven:
 *                         type: number
 *                       scoreMaximum:
 *                         type: number
 *                       gradingProgress:
 *                         type: string
 *                       activityProgress:
 *                         type: string
 *                       timestamp:
 *                         type: string
 *                 totalResults:
 *                   type: integer
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/lineitems/:lineItemId/results',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  listResults
);

/**
 * @swagger
 * /api/lti/lineitems/{lineItemId}/my-results:
 *   get:
 *     summary: Get user's own results for line item
 *     tags: [LTI Services]
 *     description: Retrieve results (scores) for the current user only (learner access)
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: path
 *         name: lineItemId
 *         required: true
 *         schema:
 *           type: string
 *         description: Line item ID
 *     responses:
 *       200:
 *         description: User's results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 lineItemId:
 *                   type: string
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       scoreGiven:
 *                         type: number
 *                       scoreMaximum:
 *                         type: number
 *                       gradingProgress:
 *                         type: string
 *                       activityProgress:
 *                         type: string
 *                       timestamp:
 *                         type: string
 *                 totalResults:
 *                   type: integer
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Session expired or invalid
 *       500:
 *         description: Server error
 */
router.get('/lineitems/:lineItemId/my-results',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  getMyResults
);

// ============================================================================
// Service Status Routes
// ============================================================================

/**
 * @swagger
 * /api/lti/services/nrps/status:
 *   get:
 *     summary: Get NRPS service status
 *     tags: [LTI Services]
 *     description: Check the availability and status of the NRPS service
 *     security:
 *       - sessionAuth: []
 *     responses:
 *       200:
 *         description: NRPS service status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 service:
 *                   type: string
 *                   example: "NRPS"
 *                 status:
 *                   type: object
 *                   properties:
 *                     available:
 *                       type: boolean
 *                     status:
 *                       type: integer
 *                     serviceVersion:
 *                       type: string
 *                     checkedAt:
 *                       type: string
 *       400:
 *         description: Bad request (NRPS not available)
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/services/nrps/status',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  getNrpsStatus
);

/**
 * @swagger
 * /api/lti/services/ags/status:
 *   get:
 *     summary: Get AGS service status
 *     tags: [LTI Services]
 *     description: Check the availability and status of the AGS service
 *     security:
 *       - sessionAuth: []
 *     responses:
 *       200:
 *         description: AGS service status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 service:
 *                   type: string
 *                   example: "AGS"
 *                 status:
 *                   type: object
 *                   properties:
 *                     available:
 *                       type: boolean
 *                     status:
 *                       type: integer
 *                     serviceVersion:
 *                       type: string
 *                     checkedAt:
 *                       type: string
 *       400:
 *         description: Bad request (AGS not available)
 *       401:
 *         description: Session expired or invalid
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/services/ags/status',
  trackSessionActivity,
  addSessionSecurityHeaders,
  requireLtiSession,
  requireLtiRole(['Instructor', 'TeachingAssistant']),
  getAgsStatus
);

export default router;

