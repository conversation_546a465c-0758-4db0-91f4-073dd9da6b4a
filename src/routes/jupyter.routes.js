import express from 'express';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import {
  jupyterProxy,
  attachJupyterToken
} from '../middlewares/jupyterhub.middlewares.js';
import {
  executeCode,
  executeNotebook,
  createWorkspace
} from '../controllers/jupyter.controller.js';
const router = express.Router();

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/execute:
 *   post:
 *     summary: Execute code in a kernel
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 example: "print('Hello, World!')"
 *     responses:
 *       200:
 *         description: Code execution result
 */
router.post(
  '/kernels/:kernelId/execute',
  jwtMiddleware,
  attachJupyterToken,
  executeCode
);

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/execute-notebook:
 *   post:
 *     summary: Execute all cells in a notebook
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notebook:
 *                 type: object
 *                 description: Full Jupyter notebook JSON
 *               timeout:
 *                 type: integer
 *                 default: 30000
 *               stopOnError:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Notebook execution results
 */
router.post(
  '/kernels/:kernelId/execute-notebook',
  jwtMiddleware,
  attachJupyterToken,
  executeNotebook
);

// Create project workspace: starts server, creates /:projectId and Untitled.ipynb, starts session
router.post(
  '/:projectId/createWorkspace',
  jwtMiddleware,
  attachJupyterToken,
  createWorkspace
);

// Catch-all proxy (must be last)
router.use('/', jwtMiddleware, attachJupyterToken, jupyterProxy);

export default router;
