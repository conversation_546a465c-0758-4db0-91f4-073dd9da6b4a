import express from 'express';
import {
  initiateLogin,
  handleO<PERSON><PERSON>allback,
  handleLaunch,
  getJWKS,
  handleDeepLinking,
  registerPlatform,
  getPlatforms,
  updatePlatform,
  deletePlatform,
  sendGradeToLMS,
  getCourseContexts,
  getLTIConfiguration,
  getRoster,
  getTokenCacheStats,
  invalidatePlatformTokens,
  cleanupExpiredTokens,
  getLineItems,
  getLineItemByResourceLinkId,
  getStudents,
  getInstructors,
  createLineItem,
  postScore,
  listResults,
  getMyResult,
  getLTIServiceStatus,
  linkProjectToLmsLineItem
} from '../controllers/lti.controller.js';
import { requireRoles } from '../middlewares/rbac.middlewares.js';
import { body, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: LTI
 *   description: Learning Tools Interoperability (LTI 1.3) integration
 */

/**
 * @swagger
 * /api/lti/config:
 *   get:
 *     summary: Get LTI tool configuration for platform registration
 *     tags: [LTI]
 *     responses:
 *       200:
 *         description: LTI configuration retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 title:
 *                   type: string
 *                 description:
 *                   type: string
 *                 target_link_uri:
 *                   type: string
 *                 oidc_initiation_url:
 *                   type: string
 *                 public_jwk_url:
 *                   type: string
 *                 scopes:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get('/config', getLTIConfiguration);

/**
 * @swagger
 * /api/lti/login:
 *   post:
 *     summary: LTI login initiation endpoint
 *     tags: [LTI]
 *     description: Initiates LTI 1.3 authentication flow
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - iss
 *               - login_hint
 *               - target_link_uri
 *               - client_id
 *             properties:
 *               iss:
 *                 type: string
 *                 description: Platform issuer identifier
 *               login_hint:
 *                 type: string
 *                 description: User identifier hint from platform
 *               target_link_uri:
 *                 type: string
 *                 description: Target URI for the launch
 *               client_id:
 *                 type: string
 *                 description: OAuth client ID
 *               lti_message_hint:
 *                 type: string
 *                 description: Optional message hint
 *     responses:
 *       302:
 *         description: Redirect to platform authentication
 *       400:
 *         description: Invalid platform or parameters
 */
router.post(
  '/oidc/init',
  [
    body('iss').isURL(),
    body('login_hint').isString(),
    body('target_link_uri').isURL(),
    body('client_id').isString(),
    body('lti_message_hint').optional().isString()
  ],
  validate,
  initiateLogin
);

/**
 * @swagger
 * /api/lti/oidc/callback:
 *   post:
 *     summary: LTI OIDC callback endpoint
 *     tags: [LTI]
 *     description: Handles OIDC callback from platform with ID token
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token from platform
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       302:
 *         description: Redirect to application page
 *       400:
 *         description: Invalid callback parameters or token
 */
router.post(
  '/oidc/callback',
  [body('id_token').isJWT(), body('state').isString()],
  validate,
  handleOIDCCallback
);

/**
 * @swagger
 * /api/lti/launch:
 *   post:
 *     summary: LTI launch endpoint
 *     tags: [LTI]
 *     description: Handles LTI 1.3 launch requests with ID token
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token from platform
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       302:
 *         description: Redirect to application page
 *       400:
 *         description: Invalid launch parameters or token
 */
router.post(
  '/launch',
  [body('id_token').isJWT(), body('state').isString()],
  validate,
  handleLaunch
);

/**
 * @swagger
 * /api/lti/jwks:
 *   get:
 *     summary: Get tool's public key set (JWKS)
 *     tags: [LTI]
 *     description: Provides public keys for JWT verification
 *     responses:
 *       200:
 *         description: JWKS retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 keys:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       kty:
 *                         type: string
 *                       use:
 *                         type: string
 *                       kid:
 *                         type: string
 *                       alg:
 *                         type: string
 *                       n:
 *                         type: string
 *                       e:
 *                         type: string
 */
router.get('/jwks', getJWKS);

/**
 * @swagger
 * /api/lti/deep-linking:
 *   post:
 *     summary: Handle deep linking requests
 *     tags: [LTI]
 *     description: Processes LTI deep linking for content selection
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token with deep linking claim
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       200:
 *         description: Deep linking response form
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *       400:
 *         description: Invalid deep linking request
 */
router.post(
  '/deep-linking',
  [body('id_token').isJWT(), body('state').isString()],
  validate,
  handleDeepLinking
);

/**
 * @swagger
 * /api/lti/platforms:
 *   get:
 *     summary: Get all registered LTI platforms
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Platforms retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/platforms', [requireRoles(['admin'])], getPlatforms);

/**
 * @swagger
 * /api/lti/platforms:
 *   post:
 *     summary: Register new LTI platform
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platformId
 *               - platformName
 *               - clientId
 *               - authLoginUrl
 *               - authTokenUrl
 *               - keySetUrl
 *             properties:
 *               platformId:
 *                 type: string
 *                 description: Platform issuer identifier
 *               platformName:
 *                 type: string
 *                 description: Human-readable platform name
 *               clientId:
 *                 type: string
 *                 description: OAuth client ID
 *               authLoginUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform authentication URL
 *               authTokenUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform token endpoint
 *               keySetUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform JWKS URL
 *               settings:
 *                 type: object
 *                 description: Additional platform settings
 *     responses:
 *       201:
 *         description: Platform registered successfully
 *       409:
 *         description: Platform already exists
 *       403:
 *         description: Insufficient permissions
 */
router.post(
  '/platforms',
  [
    requireRoles(['admin']),
    body('platformId').isURL(),
    body('platformName').isLength({ min: 2, max: 100 }),
    body('clientId').isString(),
    body('authLoginUrl').isURL(),
    body('authTokenUrl').isURL(),
    body('keySetUrl').isURL(),
    body('settings').optional().isObject()
  ],
  validate,
  registerPlatform
);

/**
 * @swagger
 * /api/lti/platforms/{id}:
 *   put:
 *     summary: Update LTI platform configuration
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               platformName:
 *                 type: string
 *               authLoginUrl:
 *                 type: string
 *                 format: uri
 *               authTokenUrl:
 *                 type: string
 *                 format: uri
 *               keySetUrl:
 *                 type: string
 *                 format: uri
 *               isActive:
 *                 type: boolean
 *               settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Platform updated successfully
 *       404:
 *         description: Platform not found
 *       403:
 *         description: Insufficient permissions
 */
router.put(
  '/platforms/:id',
  [
    requireRoles(['admin']),
    param('id').isUUID(),
    body('platformName').optional().isLength({ min: 2, max: 100 }),
    body('authLoginUrl').optional().isURL(),
    body('authTokenUrl').optional().isURL(),
    body('keySetUrl').optional().isURL(),
    body('isActive').optional().isBoolean(),
    body('settings').optional().isObject()
  ],
  validate,
  updatePlatform
);

/**
 * @swagger
 * /api/lti/platforms/{id}:
 *   delete:
 *     summary: Delete LTI platform
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Platform deleted successfully
 *       404:
 *         description: Platform not found
 *       409:
 *         description: Platform has active contexts
 *       403:
 *         description: Insufficient permissions
 */
router.delete(
  '/platforms/:id',
  [requireRoles(['admin']), param('id').isUUID()],
  validate,
  deletePlatform
);

/**
 * @swagger
 * /api/lti/grades:
 *   post:
 *     summary: Send grade to LMS via AGS
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     description: Sends a grade back to the LMS using Assignment and Grade Services
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - submissionId
 *               - gradeId
 *             properties:
 *               submissionId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the submission
 *               gradeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the grade to send
 *     responses:
 *       200:
 *         description: Grade sent to LMS successfully
 *       404:
 *         description: Submission or grade not found
 *       400:
 *         description: Project not linked to LTI resource
 *       500:
 *         description: Failed to send grade to LMS
 */
router.post(
  '/grades',
  [body('submissionId').isUUID(), body('gradeId').isUUID()],
  validate,
  sendGradeToLMS
);

/**
 * @swagger
 * /api/lti/contexts/course/{courseId}:
 *   get:
 *     summary: Get LTI contexts for a course
 *     tags: [LTI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Contexts retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/contexts/course/:courseId',
  [param('courseId').isUUID()],
  validate,
  getCourseContexts
);

/**
 * @swagger
 * /api/lti/nrps/memberships:
 *   get:
 *     summary: Fetch roster via NRPS
 *     tags: [LTI]
 *     description: Uses NRPS service URL from the last LTI launch to retrieve context memberships
 *     parameters:
 *       - in: query
 *         name: rlid
 *         schema:
 *           type: string
 *         description: Optional resource link ID filter
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *         description: Optional role filter (e.g., Instructor)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Page size
 *     responses:
 *       200:
 *         description: Membership container returned
 *       400:
 *         description: Missing launch context or NRPS not available
 */
router.get('/nrps/memberships', getRoster);

/**
 * @swagger
 * /api/lti/cache/stats:
 *   get:
 *     summary: Get LTI token cache statistics
 *     tags: [LTI]
 *     description: Retrieve statistics about cached LTI service tokens
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 stats:
 *                   type: object
 *                   properties:
 *                     totalTokens:
 *                       type: integer
 *                     expiredTokens:
 *                       type: integer
 *                     validTokens:
 *                       type: integer
 *                     cacheKeys:
 *                       type: integer
 *       500:
 *         description: Failed to retrieve cache statistics
 */
router.get('/cache/stats', requireRoles(['admin']), getTokenCacheStats);

/**
 * @swagger
 * /api/lti/cache/platform/{platformId}:
 *   delete:
 *     summary: Invalidate cached tokens for a platform
 *     tags: [LTI]
 *     description: Remove cached tokens for a specific platform
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: platformId
 *         required: true
 *         schema:
 *           type: string
 *         description: Platform identifier
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               scopes:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Optional specific scopes to invalidate
 *     responses:
 *       200:
 *         description: Tokens invalidated successfully
 *       500:
 *         description: Failed to invalidate tokens
 */
router.delete(
  '/cache/platform/:platformId',
  requireRoles(['admin']),
  invalidatePlatformTokens
);

/**
 * @swagger
 * /api/lti/cache/cleanup:
 *   post:
 *     summary: Clean up expired tokens
 *     tags: [LTI]
 *     description: Remove all expired tokens from cache
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 cleanedCount:
 *                   type: integer
 *       500:
 *         description: Failed to cleanup expired tokens
 */
router.post('/cache/cleanup', requireRoles(['admin']), cleanupExpiredTokens);

/**
 * @swagger
 * /api/lti/ags/lineitems:
 *   get:
 *     summary: Fetch gradebook columns via AGS
 *     tags: [LTI]
 *     description: Uses AGS service URL from the last LTI launch to retrieve all line items (gradebook columns)
 *     parameters:
 *       - in: query
 *         name: resource_link_id
 *         schema:
 *           type: string
 *         description: Optional resource link ID filter
 *       - in: query
 *         name: tag
 *         schema:
 *           type: string
 *         description: Optional tag filter
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of line items to return
 *     responses:
 *       200:
 *         description: Line items (gradebook columns) returned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 lineItems:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       label:
 *                         type: string
 *                       scoreMaximum:
 *                         type: number
 *                       resourceLinkId:
 *                         type: string
 *                       tag:
 *                         type: string
 *                 totalItems:
 *                   type: integer
 *                 context:
 *                   type: object
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Missing launch context or AGS not available
 *       401:
 *         description: Unauthorized access token
 *       403:
 *         description: Insufficient permissions
 */
router.get('/ags/lineitems', getLineItems);

/**
 * @swagger
 * /api/lti/ags/lineitems/:resourceLinkId:
 *   get:
 *     summary: Fetch a single gradebook column by resourceLinkId
 *     tags: [LTI]
 *     description: Returns the first line item that matches the provided resourceLinkId
 *     parameters:
 *       - in: path
 *         name: resourceLinkId
 *         required: true
 *         schema:
 *           type: string
 *         description: The Resource Link ID to filter line items
 *     responses:
 *       200:
 *         description: Matching line item (if any)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 found:
 *                   type: boolean
 *                 lineItem:
 *                   type: object
 *                 metadata:
 *                   type: object
 *       400:
 *         description: Missing required query parameter or launch context
 *       404:
 *         description: AGS endpoint not found or no matching item
 */
router.get('/ags/lineitems/:resourceLinkId', getLineItemByResourceLinkId);

/**
 * @swagger
 * /api/lti/nrps/students:
 *   get:
 *     summary: Get student memberships only
 *     tags: [LTI]
 *     responses:
 *       200: { description: Students fetched }
 */
router.get('/nrps/students', getStudents);

/**
 * @swagger
 * /api/lti/nrps/instructors:
 *   get:
 *     summary: Get instructor memberships only
 *     tags: [LTI]
 *     responses:
 *       200: { description: Instructors fetched }
 */
router.get('/nrps/instructors', getInstructors);

/**
 * @swagger
 * /api/lti/ags/lineitems:
 *   post:
 *     summary: Create (and store) a new line item
 *     tags: [LTI]
 */
router.post('/ags/lineitems', createLineItem);

/**
 * @swagger
 * /api/lti/ags/scores:
 *   post:
 *     summary: Post a score to a line item (Grade Passback)
 *     tags: [LTI]
 */
router.post('/ags/scores', postScore);

/**
 * @swagger
 * /api/lti/ags/results:
 *   get:
 *     summary: List all results for a line item
 *     tags: [LTI]
 */
router.get('/ags/results', listResults);

/**
 * @swagger
 * /api/lti/ags/results/me:
 *   get:
 *     summary: Get current user result for a line item
 *     tags: [LTI]
 */
router.get('/ags/results/me', getMyResult);

/**
 * @swagger
 * /api/lti/status:
 *   get:
 *     summary: Get current LTI service/session status
 *     tags: [LTI]
 */
router.get('/status', getLTIServiceStatus);

/**
 * @swagger
 * /api/lti/ags/link-project:
 *   post:
 *     summary: Link local project to LMS line item and update LMS resourceId
 *     tags: [LTI]
 *     description: Given resourceLinkId and projectId, updates local lti_resource_links and lti_line_items with projectId, updates project.course_id/total_points from line item, and performs a PUT to LMS lineitem to set resourceId.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [resourceLinkId, projectId]
 *             properties:
 *               resourceLinkId:
 *                 type: string
 *               projectId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Mapping updated and LMS line item resourceId set
 *       400:
 *         description: Validation or missing context
 *       404:
 *         description: Not found
 */
router.post(
  '/ags/link-project',
  [body('resourceLinkId').isString(), body('projectId').isUUID()],
  validate,
  linkProjectToLmsLineItem
);

export default router;
