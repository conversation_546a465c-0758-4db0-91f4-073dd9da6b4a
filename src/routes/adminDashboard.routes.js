import express from 'express';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { query, param, body } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';
import { getAdminDashboard } from '../controllers/adminDashboard.controller.js';

const router = express.Router();

router.use(jwtMiddleware);

/**
 * @desc    Get admin dashboard data
 * @route   GET /api/admin/dashboard
 * @access  Private (Admin)
 */

// Dashboard overview
router.get('/dashboard', [], getAdminDashboard);

export default router;

