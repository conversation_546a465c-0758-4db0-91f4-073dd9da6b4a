import express from 'express';
import {
  getProjectCreationCourses,
  getCourseDropdownByRole,
  getUserCourseRole,
  canCreateProjectsInCourse,
  getCourseTeachingStaff,
  getCourseSummary
} from '../controllers/courseDropdown.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

/**
 * @swagger
 * tags:
 *   name: Course Dropdown
 *   description: Course dropdown and role validation endpoints for project creation
 */

/**
 * @swagger
 * /api/courses/project-creation:
 *   get:
 *     summary: Get courses for project creation dropdown (instructor/TA only)
 *     tags: [Course Dropdown]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Project creation courses retrieved successfully
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 */
router.get(
  '/project-creation',
  [requirePermissions(['create_projects'])],
  getProjectCreationCourses
);

/**
 * @swagger
 * /api/courses/dropdown/{role}:
 *   get:
 *     summary: Get course dropdown for specific user role
 *     tags: [Course Dropdown]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: role
 *         required: true
 *         schema:
 *           type: string
 *           enum: [instructor, ta, student]
 *         description: User role to filter courses
 *     responses:
 *       200:
 *         description: Courses for role retrieved successfully
 *       400:
 *         description: Invalid role parameter
 *       500:
 *         description: Internal server error
 */
router.get(
  '/dropdown/:role',
  [
    requirePermissions(['view_courses']),
    param('role')
      .isIn(['instructor', 'ta', 'student'])
      .withMessage('Invalid role. Must be instructor, ta, or student')
  ],
  validate,
  getCourseDropdownByRole
);

/**
 * @swagger
 * /api/courses/{courseId}/user-role:
 *   get:
 *     summary: Validate user's role in a specific course
 *     tags: [Course Dropdown]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Course ID
 *     responses:
 *       200:
 *         description: User course role retrieved successfully
 *       404:
 *         description: User not enrolled in course
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:courseId/user-role',
  [
    requirePermissions(['view_courses']),
    param('courseId').isUUID().withMessage('Valid course ID is required')
  ],
  validate,
  getUserCourseRole
);

/**
 * @swagger
 * /api/courses/{courseId}/can-create-projects:
 *   get:
 *     summary: Check if user can create projects in a specific course
 *     tags: [Course Dropdown]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Course ID
 *     responses:
 *       200:
 *         description: Project creation permission checked successfully
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:courseId/can-create-projects',
  [
    requirePermissions(['create_projects']),
    param('courseId').isUUID().withMessage('Valid course ID is required')
  ],
  validate,
  canCreateProjectsInCourse
);

/**
 * @swagger
 * /api/courses/{courseId}/teaching-staff:
 *   get:
 *     summary: Get teaching staff for a specific course
 *     tags: [Course Dropdown]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Course ID
 *     responses:
 *       200:
 *         description: Course teaching staff retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:courseId/teaching-staff',
  [
    requirePermissions(['create_projects']),
    param('courseId').isUUID().withMessage('Valid course ID is required')
  ],
  validate,
  getCourseTeachingStaff
);

/**
 * @swagger
 * /api/courses/{courseId}/summary:
 *   get:
 *     summary: Get course summary for dropdown display
 *     tags: [Course Dropdown]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Course ID
 *     responses:
 *       200:
 *         description: Course summary retrieved successfully
 *       404:
 *         description: Course not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:courseId/summary',
  [
    requirePermissions(['view_courses']),
    param('courseId').isUUID().withMessage('Valid course ID is required')
  ],
  validate,
  getCourseSummary
);

export default router;
