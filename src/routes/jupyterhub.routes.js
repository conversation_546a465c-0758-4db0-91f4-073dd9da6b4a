import express from 'express';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import jupyterController from '../controllers/jupyter.controller.js';
const router = express.Router();

router.post('/start', jwtMiddleware, jupyterController.startJupyterServer);

router.post('/stop', jwtMiddleware, jupyterController.stopJupyterServer);

router.get('/token', jwtMiddleware, jupyterController.getJupyterToken);

export default router;
