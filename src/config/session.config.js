import session from 'express-session';
import SequelizeStoreInit from 'connect-session-sequelize';
import { sequelize } from './database.config.js';
import logger from './logger.config.js';

/**
 * Enhanced Session Configuration for LTI
 *
 * Implements secure session management with proper TTL, cookie security,
 * and separation between user sessions and service tokens.
 */

// Session configuration constants
export const SESSION_CONFIG = {
  // Cookie security settings
  COOKIE_NAME: 'bits.session.id',
  COOKIE_SECURE: process.env.NODE_ENV === 'production',
  COOKIE_HTTP_ONLY: true,
  COOKIE_SAME_SITE: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',

  // Session TTL settings
  IDLE_TIMEOUT: 2 * 60 * 60 * 1000, // 2 hours idle
  ABSOLUTE_TIMEOUT: 12 * 60 * 60 * 1000, // 12 hours absolute
  CHECK_EXPIRATION_INTERVAL: 15 * 60 * 1000, // 15 minutes

  // State/nonce store settings (separate from session)
  STATE_STORE_TTL: 10 * 60 * 1000, // 10 minutes

  // Session secret
  SECRET:
    process.env.SESSION_SECRET || 'your-session-secret-change-in-production'
};

/**
 * Create enhanced session store with proper configuration
 */
export const createSessionStore = () => {
  const SequelizeStore = SequelizeStoreInit(session.Store);

  const sessionStore = new SequelizeStore({
    db: sequelize,
    tableName: 'sessions',
    checkExpirationInterval: SESSION_CONFIG.CHECK_EXPIRATION_INTERVAL,
    expiration: SESSION_CONFIG.ABSOLUTE_TIMEOUT,

    // Custom session data handling
    extendDefaultFields: (defaults, session) => {
      return {
        data: defaults.data,
        expires: defaults.expires,
        createdAt: defaults.createdAt,
        updatedAt: defaults.updatedAt
      };
    }
  });

  // Handle store events
  sessionStore.on('connect', () => {
    logger.info('[Session Store] Connected to database');
  });

  sessionStore.on('disconnect', () => {
    logger.warn('[Session Store] Disconnected from database');
  });

  return sessionStore;
};

/**
 * Create session middleware with enhanced security
 */
export const createSessionMiddleware = () => {
  // const sessionStore = createSessionStore();
  const SequelizeStore = SequelizeStoreInit(session.Store);
  const sessionStore = new SequelizeStore({
    db: sequelize,
    tableName: 'sessions',
    checkExpirationInterval: 15 * 60 * 1000,
    expiration: 24 * 60 * 60 * 1000
  });

  return session({
    secret: process.env.SESSION_SECRET || 'your-session-secret',
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'none',
      maxAge: 24 * 60 * 60 * 1000
    },
    name: 'bits.session.id',

    // Custom session ID generation
    genid: req => {
      return `lti_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    // Custom session serialization
    serialize: session => {
      return JSON.stringify(session);
    },

    deserialize: sessionData => {
      try {
        return JSON.parse(sessionData);
      } catch (error) {
        logger.error('[Session] Failed to deserialize session data:', error);
        return {};
      }
    }
  });
};

/**
 * Session data structure for LTI launches
 */
export const createLtiSessionData = (
  launchData,
  user,
  context,
  resourceLink
) => {
  return {
    // User identification
    sub: launchData.sub, // LTI subject (user ID)
    iss: launchData.iss, // LTI issuer (platform URL)

    // LTI context
    deployment_id:
      launchData['https://purl.imsglobal.org/spec/lti/claim/deployment_id'],
    context_id: context?.contextId,
    resource_link_id: resourceLink?.resourceLinkId,

    // User profile
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      lms_user_id: user.lms_user_id
    },

    // Mapped roles (enhanced RBAC)
    roles: user.roles || [],
    lti_roles:
      launchData['https://purl.imsglobal.org/spec/lti/claim/roles'] || [],
    internal_roles: user.internal_roles || [],
    permissions: user.permissions || [],

    // Session metadata
    session_type: 'lti_launch',
    created_at: new Date().toISOString(),
    last_activity: new Date().toISOString(),

    // Platform information
    platform: {
      id: context?.platformId,
      name: context?.platform?.platformName
    }
  };
};

/**
 * Session data structure for standard user logins (non-LTI)
 */
export const createUserSessionData = user => {
  const nowIso = new Date().toISOString();
  return {
    // Standard identifiers
    sub: String(user.id),
    iss:
      process.env.APP_URL ||
      process.env.BACKEND_URL ||
      'bits-datascience-backend',

    // User profile
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      profile_picture: user.profile_picture
    },

    // RBAC snapshot
    roles: user.roles?.map(r => ({ id: r.id, name: r.name })) || [],
    permissions:
      (user.roles || []).flatMap(r => (r.permissions || []).map(p => p.key)) ||
      [],

    // Session metadata
    session_type: 'user_login',
    created_at: nowIso,
    last_activity: nowIso
  };
};

/**
 * Initialize (regenerate + save) an express-session with provided data
 * Ensures a fresh session ID using genid() and persists to the store (DB)
 */
export const initializeUserSession = async (req, sessionData) => {
  if (!req.session) return; // session middleware not mounted

  // Regenerate to prevent fixation and get a fresh ID matching genid format
  await new Promise(resolve => {
    req.session.regenerate(() => resolve());
  });

  Object.assign(req.session, sessionData);

  // Persist so Set-Cookie header is included and data stored in DB
  await new Promise((resolve, reject) => {
    req.session.save(err => (err ? reject(err) : resolve()));
  });
};

/**
 * Validate session data structure
 */
export const validateSessionData = sessionData => {
  const required = ['sub', 'iss', 'user', 'session_type'];
  const missing = required.filter(field => !sessionData[field]);

  if (missing.length > 0) {
    throw new Error(
      `Invalid session data: missing fields ${missing.join(', ')}`
    );
  }

  if (sessionData.session_type !== 'lti_launch') {
    throw new Error('Invalid session type');
  }

  return true;
};

/**
 * Update session activity timestamp
 */
export const updateSessionActivity = req => {
  if (req.session && req.session.last_activity) {
    req.session.last_activity = new Date().toISOString();
  }
};

/**
 * Check if session is expired based on idle timeout
 */
export const isSessionExpired = sessionData => {
  if (!sessionData.last_activity) {
    return true;
  }

  const lastActivity = new Date(sessionData.last_activity);
  const now = new Date();
  const idleTime = now.getTime() - lastActivity.getTime();

  return idleTime > SESSION_CONFIG.IDLE_TIMEOUT;
};

/**
 * Get session expiration info
 */
export const getSessionExpirationInfo = sessionData => {
  if (!sessionData.last_activity) {
    return { expired: true, expiresAt: null, idleTimeRemaining: 0 };
  }

  const lastActivity = new Date(sessionData.last_activity);
  const expiresAt = new Date(
    lastActivity.getTime() + SESSION_CONFIG.IDLE_TIMEOUT
  );
  const now = new Date();
  const idleTimeRemaining = Math.max(0, expiresAt.getTime() - now.getTime());

  return {
    expired: idleTimeRemaining === 0,
    expiresAt: expiresAt.toISOString(),
    idleTimeRemaining
  };
};

export default {
  SESSION_CONFIG,
  createSessionStore,
  createSessionMiddleware,
  createLtiSessionData,
  createUserSessionData,
  initializeUserSession,
  validateSessionData,
  updateSessionActivity,
  isSessionExpired,
  getSessionExpirationInfo
};
