<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="547" failures="149" errors="0" time="15.534">
  <testsuite name="Submission Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:52" time="0.179" tests="39">
    <testcase classname="Submission Model (Mocked) Model Definition should define Submission model with correct attributes" name="Submission Model (Mocked) Model Definition should define Submission model with correct attributes" time="0.005">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Definition should define table name correctly" name="Submission Model (Mocked) Model Definition should define table name correctly" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Definition should define indexes correctly" name="Submission Model (Mocked) Model Definition should define indexes correctly" time="0.003">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate required fields" name="Submission Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate status enum values" name="Submission Model (Mocked) Model Validation should validate status enum values" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate attempt number" name="Submission Model (Mocked) Model Validation should validate attempt number" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate days late" name="Submission Model (Mocked) Model Validation should validate days late" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate UUID format" name="Submission Model (Mocked) Model Validation should validate UUID format" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate S3 URL format" name="Submission Model (Mocked) Model Validation should validate S3 URL format" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate JSONB fields" name="Submission Model (Mocked) Model Validation should validate JSONB fields" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate additional files array" name="Submission Model (Mocked) Model Validation should validate additional files array" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should create submission successfully" name="Submission Model (Mocked) Model Methods should create submission successfully" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submission by user and project" name="Submission Model (Mocked) Model Methods should find submission by user and project" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submission by ID" name="Submission Model (Mocked) Model Methods should find submission by ID" time="0.007">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submissions by user" name="Submission Model (Mocked) Model Methods should find submissions by user" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submissions by project" name="Submission Model (Mocked) Model Methods should find submissions by project" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should update submission" name="Submission Model (Mocked) Model Methods should update submission" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should delete submission" name="Submission Model (Mocked) Model Methods should delete submission" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submissions by status" name="Submission Model (Mocked) Model Methods should find submissions by status" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find late submissions" name="Submission Model (Mocked) Model Methods should find late submissions" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should count submissions" name="Submission Model (Mocked) Model Methods should count submissions" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should set default values before create" name="Submission Model (Mocked) Model Hooks should set default values before create" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should calculate days late" name="Submission Model (Mocked) Model Hooks should calculate days late" time="0.002">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should validate unique user-project-attempt combination" name="Submission Model (Mocked) Model Hooks should validate unique user-project-attempt combination" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should sanitize student comments" name="Submission Model (Mocked) Model Hooks should sanitize student comments" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Associations should have user association" name="Submission Model (Mocked) Model Associations should have user association" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Associations should have project association" name="Submission Model (Mocked) Model Associations should have project association" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Associations should have grade association" name="Submission Model (Mocked) Model Associations should have grade association" time="0.002">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have by status scope" name="Submission Model (Mocked) Model Scopes should have by status scope" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have by user scope" name="Submission Model (Mocked) Model Scopes should have by user scope" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have by project scope" name="Submission Model (Mocked) Model Scopes should have by project scope" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have late submissions scope" name="Submission Model (Mocked) Model Scopes should have late submissions scope" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Error Handling should handle duplicate submission error" name="Submission Model (Mocked) Error Handling should handle duplicate submission error" time="0.026">
    </testcase>
    <testcase classname="Submission Model (Mocked) Error Handling should handle validation error" name="Submission Model (Mocked) Error Handling should handle validation error" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Error Handling should handle submission not found" name="Submission Model (Mocked) Error Handling should handle submission not found" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should check if submission is late" name="Submission Model (Mocked) Business Logic should check if submission is late" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should check if submission is complete" name="Submission Model (Mocked) Business Logic should check if submission is complete" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should calculate submission progress" name="Submission Model (Mocked) Business Logic should calculate submission progress" time="0.002">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should get next attempt number" name="Submission Model (Mocked) Business Logic should get next attempt number" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Grade Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:52" time="0.13" tests="34">
    <testcase classname="Grade Model (Mocked) Model Definition should define Grade model with correct attributes" name="Grade Model (Mocked) Model Definition should define Grade model with correct attributes" time="0.002">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Definition should define table name correctly" name="Grade Model (Mocked) Model Definition should define table name correctly" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Definition should define indexes correctly" name="Grade Model (Mocked) Model Definition should define indexes correctly" time="0.002">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate required fields" name="Grade Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate score values" name="Grade Model (Mocked) Model Validation should validate score values" time="0.006">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate percentage range" name="Grade Model (Mocked) Model Validation should validate percentage range" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate letter grade format" name="Grade Model (Mocked) Model Validation should validate letter grade format" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate UUID format" name="Grade Model (Mocked) Model Validation should validate UUID format" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate JSONB fields" name="Grade Model (Mocked) Model Validation should validate JSONB fields" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate grading time" name="Grade Model (Mocked) Model Validation should validate grading time" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should create grade successfully" name="Grade Model (Mocked) Model Methods should create grade successfully" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find grade by submission ID" name="Grade Model (Mocked) Model Methods should find grade by submission ID" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find grade by ID" name="Grade Model (Mocked) Model Methods should find grade by ID" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find grades by evaluator" name="Grade Model (Mocked) Model Methods should find grades by evaluator" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should update grade" name="Grade Model (Mocked) Model Methods should update grade" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should delete grade" name="Grade Model (Mocked) Model Methods should delete grade" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find final grades" name="Grade Model (Mocked) Model Methods should find final grades" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should count grades" name="Grade Model (Mocked) Model Methods should count grades" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should calculate percentage before create" name="Grade Model (Mocked) Model Hooks should calculate percentage before create" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should calculate letter grade" name="Grade Model (Mocked) Model Hooks should calculate letter grade" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should set graded_at timestamp" name="Grade Model (Mocked) Model Hooks should set graded_at timestamp" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should validate rubric scores" name="Grade Model (Mocked) Model Hooks should validate rubric scores" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Associations should have submission association" name="Grade Model (Mocked) Model Associations should have submission association" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Associations should have evaluator association" name="Grade Model (Mocked) Model Associations should have evaluator association" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Scopes should have final grades scope" name="Grade Model (Mocked) Model Scopes should have final grades scope" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Scopes should have by evaluator scope" name="Grade Model (Mocked) Model Scopes should have by evaluator scope" time="0.003">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Scopes should have by percentage range scope" name="Grade Model (Mocked) Model Scopes should have by percentage range scope" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Error Handling should handle duplicate submission ID error" name="Grade Model (Mocked) Error Handling should handle duplicate submission ID error" time="0.013">
    </testcase>
    <testcase classname="Grade Model (Mocked) Error Handling should handle validation error" name="Grade Model (Mocked) Error Handling should handle validation error" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Error Handling should handle grade not found" name="Grade Model (Mocked) Error Handling should handle grade not found" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should calculate grade point average" name="Grade Model (Mocked) Business Logic should calculate grade point average" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should check if grade is passing" name="Grade Model (Mocked) Business Logic should check if grade is passing" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should calculate average grade" name="Grade Model (Mocked) Business Logic should calculate average grade" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should check if grade is late" name="Grade Model (Mocked) Business Logic should check if grade is late" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Course Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:54" time="0.108" tests="34">
    <testcase classname="Course Model (Mocked) Model Definition should define Course model with correct attributes" name="Course Model (Mocked) Model Definition should define Course model with correct attributes" time="0.002">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Definition should define table name correctly" name="Course Model (Mocked) Model Definition should define table name correctly" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Definition should define indexes correctly" name="Course Model (Mocked) Model Definition should define indexes correctly" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate required fields" name="Course Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate name length" name="Course Model (Mocked) Model Validation should validate name length" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate code length" name="Course Model (Mocked) Model Validation should validate code length" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate status enum values" name="Course Model (Mocked) Model Validation should validate status enum values" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate UUID format for instructor_id" name="Course Model (Mocked) Model Validation should validate UUID format for instructor_id" time="0.004">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate date formats" name="Course Model (Mocked) Model Validation should validate date formats" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate JSONB fields" name="Course Model (Mocked) Model Validation should validate JSONB fields" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should create course successfully" name="Course Model (Mocked) Model Methods should create course successfully" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find course by LMS ID" name="Course Model (Mocked) Model Methods should find course by LMS ID" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find course by ID" name="Course Model (Mocked) Model Methods should find course by ID" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find all active courses" name="Course Model (Mocked) Model Methods should find all active courses" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find courses by instructor" name="Course Model (Mocked) Model Methods should find courses by instructor" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should update course" name="Course Model (Mocked) Model Methods should update course" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should delete course" name="Course Model (Mocked) Model Methods should delete course" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find courses by term" name="Course Model (Mocked) Model Methods should find courses by term" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should count courses" name="Course Model (Mocked) Model Methods should count courses" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Hooks should set default values before create" name="Course Model (Mocked) Model Hooks should set default values before create" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Hooks should validate unique LMS course ID" name="Course Model (Mocked) Model Hooks should validate unique LMS course ID" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Hooks should sanitize course name" name="Course Model (Mocked) Model Hooks should sanitize course name" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Associations should have instructor association" name="Course Model (Mocked) Model Associations should have instructor association" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Associations should have enrollments association" name="Course Model (Mocked) Model Associations should have enrollments association" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Associations should have projects association" name="Course Model (Mocked) Model Associations should have projects association" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Scopes should have active scope" name="Course Model (Mocked) Model Scopes should have active scope" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Scopes should have by instructor scope" name="Course Model (Mocked) Model Scopes should have by instructor scope" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Scopes should have by term scope" name="Course Model (Mocked) Model Scopes should have by term scope" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Error Handling should handle duplicate LMS course ID error" name="Course Model (Mocked) Error Handling should handle duplicate LMS course ID error" time="0.012">
    </testcase>
    <testcase classname="Course Model (Mocked) Error Handling should handle validation error" name="Course Model (Mocked) Error Handling should handle validation error" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Error Handling should handle course not found" name="Course Model (Mocked) Error Handling should handle course not found" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Business Logic should check if course is active" name="Course Model (Mocked) Business Logic should check if course is active" time="0.003">
    </testcase>
    <testcase classname="Course Model (Mocked) Business Logic should check if course is within date range" name="Course Model (Mocked) Business Logic should check if course is within date range" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Business Logic should get course duration in days" name="Course Model (Mocked) Business Logic should get course duration in days" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Project Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:54" time="0.103" tests="32">
    <testcase classname="Project Model (Mocked) Model Definition should define Project model with correct attributes" name="Project Model (Mocked) Model Definition should define Project model with correct attributes" time="0.005">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Definition should define table name correctly" name="Project Model (Mocked) Model Definition should define table name correctly" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Definition should define timestamps" name="Project Model (Mocked) Model Definition should define timestamps" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate required fields" name="Project Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate estimated hours" name="Project Model (Mocked) Model Validation should validate estimated hours" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate UUID format for createdBy" name="Project Model (Mocked) Model Validation should validate UUID format for createdBy" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate tags array" name="Project Model (Mocked) Model Validation should validate tags array" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate settings object" name="Project Model (Mocked) Model Validation should validate settings object" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should create project successfully" name="Project Model (Mocked) Model Methods should create project successfully" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find project by title" name="Project Model (Mocked) Model Methods should find project by title" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find project by ID" name="Project Model (Mocked) Model Methods should find project by ID" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find all active projects" name="Project Model (Mocked) Model Methods should find all active projects" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find projects by creator" name="Project Model (Mocked) Model Methods should find projects by creator" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should update project" name="Project Model (Mocked) Model Methods should update project" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should delete project" name="Project Model (Mocked) Model Methods should delete project" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find projects by tags" name="Project Model (Mocked) Model Methods should find projects by tags" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should count projects" name="Project Model (Mocked) Model Methods should count projects" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Hooks should set default values before create" name="Project Model (Mocked) Model Hooks should set default values before create" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Hooks should validate title length" name="Project Model (Mocked) Model Hooks should validate title length" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Hooks should sanitize description" name="Project Model (Mocked) Model Hooks should sanitize description" time="0.004">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Associations should have creator association" name="Project Model (Mocked) Model Associations should have creator association" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Associations should have submissions association" name="Project Model (Mocked) Model Associations should have submissions association" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Associations should have sandbox settings association" name="Project Model (Mocked) Model Associations should have sandbox settings association" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Scopes should have active scope" name="Project Model (Mocked) Model Scopes should have active scope" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Scopes should have inactive scope" name="Project Model (Mocked) Model Scopes should have inactive scope" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Scopes should have by creator scope" name="Project Model (Mocked) Model Scopes should have by creator scope" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Error Handling should handle validation error" name="Project Model (Mocked) Error Handling should handle validation error" time="0.011">
    </testcase>
    <testcase classname="Project Model (Mocked) Error Handling should handle foreign key constraint error" name="Project Model (Mocked) Error Handling should handle foreign key constraint error" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Error Handling should handle project not found" name="Project Model (Mocked) Error Handling should handle project not found" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Business Logic should calculate completion percentage" name="Project Model (Mocked) Business Logic should calculate completion percentage" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Business Logic should check if project is overdue" name="Project Model (Mocked) Business Logic should check if project is overdue" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Business Logic should get project difficulty level" name="Project Model (Mocked) Business Logic should get project difficulty level" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="JupyterHub Admin Service" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:55" time="0.299" tests="28">
    <testcase classname="JupyterHub Admin Service createUser should create user successfully" name="JupyterHub Admin Service createUser should create user successfully" time="0.003">
    </testcase>
    <testcase classname="JupyterHub Admin Service createUser should handle user creation errors" name="JupyterHub Admin Service createUser should handle user creation errors" time="0.016">
    </testcase>
    <testcase classname="JupyterHub Admin Service createUser should handle duplicate user creation" name="JupyterHub Admin Service createUser should handle duplicate user creation" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteUser should delete user successfully" name="JupyterHub Admin Service deleteUser should delete user successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteUser should handle user deletion errors" name="JupyterHub Admin Service deleteUser should handle user deletion errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteUser should handle non-existent user deletion" name="JupyterHub Admin Service deleteUser should handle non-existent user deletion" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUser should get user successfully" name="JupyterHub Admin Service getUser should get user successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUser should handle user retrieval errors" name="JupyterHub Admin Service getUser should handle user retrieval errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service listUsers should list users successfully" name="JupyterHub Admin Service listUsers should list users successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service listUsers should handle user listing errors" name="JupyterHub Admin Service listUsers should handle user listing errors" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service startServer should start server successfully" name="JupyterHub Admin Service startServer should start server successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service startServer should handle server start errors" name="JupyterHub Admin Service startServer should handle server start errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service stopServer should stop server successfully" name="JupyterHub Admin Service stopServer should stop server successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service stopServer should handle server stop errors" name="JupyterHub Admin Service stopServer should handle server stop errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getServerStatus should get server status successfully" name="JupyterHub Admin Service getServerStatus should get server status successfully" time="0.005">
    </testcase>
    <testcase classname="JupyterHub Admin Service getServerStatus should handle server status retrieval errors" name="JupyterHub Admin Service getServerStatus should handle server status retrieval errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUserServers should get user servers successfully" name="JupyterHub Admin Service getUserServers should get user servers successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUserServers should handle user servers retrieval errors" name="JupyterHub Admin Service getUserServers should handle user servers retrieval errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service addUserToGroup should add user to group successfully" name="JupyterHub Admin Service addUserToGroup should add user to group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service addUserToGroup should handle add user to group errors" name="JupyterHub Admin Service addUserToGroup should handle add user to group errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service removeUserFromGroup should remove user from group successfully" name="JupyterHub Admin Service removeUserFromGroup should remove user from group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service removeUserFromGroup should handle remove user from group errors" name="JupyterHub Admin Service removeUserFromGroup should handle remove user from group errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service createGroup should create group successfully" name="JupyterHub Admin Service createGroup should create group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service createGroup should handle group creation errors" name="JupyterHub Admin Service createGroup should handle group creation errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteGroup should delete group successfully" name="JupyterHub Admin Service deleteGroup should delete group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteGroup should handle group deletion errors" name="JupyterHub Admin Service deleteGroup should handle group deletion errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service listGroups should list groups successfully" name="JupyterHub Admin Service listGroups should list groups successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service listGroups should handle group listing errors" name="JupyterHub Admin Service listGroups should handle group listing errors" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Build Service" errors="0" failures="22" skipped="0" timestamp="2025-09-17T15:45:55" time="0.457" tests="22">
    <testcase classname="Build Service constructor should initialize with default values" name="Build Service constructor should initialize with default values" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service constructor should initialize with environment variables" name="Build Service constructor should initialize with environment variables" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should start image build successfully" name="Build Service startImageBuild should start image build successfully" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should start image build with default options" name="Build Service startImageBuild should start image build with default options" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should handle build start failure" name="Build Service startImageBuild should handle build start failure" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should use custom ECR repo from environment" name="Build Service startImageBuild should use custom ECR repo from environment" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should get build status successfully" name="Build Service getBuildStatus should get build status successfully" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should handle build not found" name="Build Service getBuildStatus should handle build not found" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should handle build status failure" name="Build Service getBuildStatus should handle build status failure" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should handle build with missing optional fields" name="Build Service getBuildStatus should handle build with missing optional fields" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service generateBuildSpec should generate valid buildspec" name="Build Service generateBuildSpec should generate valid buildspec" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service generateBuildSpec should include all required sections" name="Build Service generateBuildSpec should include all required sections" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service createBuildWebhook should create webhook successfully" name="Build Service createBuildWebhook should create webhook successfully" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service createBuildWebhook should handle webhook creation failure" name="Build Service createBuildWebhook should handle webhook creation failure" time="0.004">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service updateProjectImageUrl should update project image URL successfully" name="Build Service updateProjectImageUrl should update project image URL successfully" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service updateProjectImageUrl should handle project update failure" name="Build Service updateProjectImageUrl should handle project update failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service validateBuildRequirements should validate requirements successfully" name="Build Service validateBuildRequirements should validate requirements successfully" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service validateBuildRequirements should handle validation failure" name="Build Service validateBuildRequirements should handle validation failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildHistory should get build history successfully" name="Build Service getBuildHistory should get build history successfully" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildHistory should handle build history failure" name="Build Service getBuildHistory should handle build history failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service StartBuildCommand validation should create StartBuildCommand with correct parameters" name="Build Service StartBuildCommand validation should create StartBuildCommand with correct parameters" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service BatchGetBuildsCommand validation should create BatchGetBuildsCommand with correct parameters" name="Build Service BatchGetBuildsCommand validation should create BatchGetBuildsCommand with correct parameters" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Error Handler Middleware" errors="0" failures="18" skipped="0" timestamp="2025-09-17T15:45:56" time="0.437" tests="31">
    <testcase classname="Error Handler Middleware errorHandler should handle generic errors" name="Error Handler Middleware errorHandler should handle generic errors" time="0.005">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;API Error:&quot;, {&quot;ip&quot;: &quot;127.0.0.1&quot;, &quot;message&quot;: &quot;Something went wrong&quot;, &quot;method&quot;: &quot;GET&quot;, &quot;stack&quot;: &quot;Error stack trace&quot;, &quot;url&quot;: &quot;/api/test&quot;, &quot;user&quot;: &quot;<EMAIL>&quot;, &quot;userAgent&quot;: &quot;Mozilla/5.0&quot;}
Received: {&quot;clientIp&quot;: &quot;127.0.0.1&quot;, &quot;component&quot;: &quot;N/A&quot;, &quot;email&quot;: &quot;<EMAIL>&quot;, &quot;fileName&quot;: &quot;&quot;, &quot;lineNumber&quot;: &quot;&quot;, &quot;message&quot;: &quot;Something went wrong&quot;, &quot;methodNameStr&quot;: &quot;&quot;, &quot;module_name&quot;: &quot;Bits DS Projects Portal&quot;, &quot;requestData&quot;: &quot;:\&quot;\&quot;&quot;, &quot;stack&quot;: &quot;Error stack trace&quot;, &quot;statusCode&quot;: 500, &quot;url&quot;: &quot;&quot;, &quot;userAgent&quot;: &quot;N/A&quot;, &quot;userId&quot;: &quot;N/A&quot;}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:51:28)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle ValidationError" name="Error Handler Middleware errorHandler should handle ValidationError" time="0.005">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

@@ -1,6 +1,10 @@
  Object {
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Validation error&quot;,
+   },
    &quot;details&quot;: Array [
      Object {
        &quot;field&quot;: &quot;email&quot;,
        &quot;message&quot;: &quot;Email is required&quot;,
        &quot;value&quot;: null,
@@ -9,11 +13,10 @@
        &quot;field&quot;: &quot;name&quot;,
        &quot;message&quot;: &quot;Name is required&quot;,
        &quot;value&quot;: &quot;&quot;,
      },
    ],
-   &quot;error&quot;: &quot;Validation error&quot;,
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 400,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.606Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:82:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle SequelizeUniqueConstraintError" name="Error Handler Middleware errorHandler should handle SequelizeUniqueConstraintError" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Resource already exists&quot;,
+   },
    &quot;details&quot;: Array [
      Object {
        &quot;field&quot;: &quot;email&quot;,
        &quot;message&quot;: &quot;email must be unique&quot;,
        &quot;value&quot;: &quot;<EMAIL>&quot;,
      },
    ],
-   &quot;error&quot;: &quot;Resource already exists&quot;,
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 409,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.612Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:105:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle SequelizeForeignKeyConstraintError" name="Error Handler Middleware errorHandler should handle SequelizeForeignKeyConstraintError" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;Invalid reference to related resource&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Invalid reference to related resource&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 400,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.618Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:124:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle SequelizeConnectionError" name="Error Handler Middleware errorHandler should handle SequelizeConnectionError" time="0.002">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Service temporarily unavailable&quot;
Received: undefined
    at Object.toBe (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:141:34)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle JsonWebTokenError" name="Error Handler Middleware errorHandler should handle JsonWebTokenError" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;Invalid authentication token&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Invalid authentication token&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 401,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.622Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:156:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle TokenExpiredError" name="Error Handler Middleware errorHandler should handle TokenExpiredError" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;Authentication token expired&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Authentication token expired&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 401,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.624Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:172:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_SIZE" name="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_SIZE" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;File size too large&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;File size too large&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 400,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.626Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:189:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_COUNT" name="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_COUNT" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;Too many files&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Too many files&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 400,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.629Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:206:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle MulterError with unknown code" name="Error Handler Middleware errorHandler should handle MulterError with unknown code" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;File upload error&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;File upload error&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 400,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.631Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:223:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle custom errors with statusCode" name="Error Handler Middleware errorHandler should handle custom errors with statusCode" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 422

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:238:35)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle custom errors with status" name="Error Handler Middleware errorHandler should handle custom errors with status" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 418

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:254:35)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle errors without message" name="Error Handler Middleware errorHandler should handle errors without message" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;error&quot;: &quot;Internal server error&quot;,
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Internal server error&quot;,
+   },
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 500,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.636Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:270:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should include stack trace in development mode" name="Error Handler Middleware errorHandler should include stack trace in development mode" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: {&quot;error&quot;: &quot;Development error&quot;, &quot;method&quot;: &quot;GET&quot;, &quot;path&quot;: &quot;/api/test&quot;, &quot;stack&quot;: &quot;Development stack trace&quot;, &quot;status&quot;: 500, &quot;timestamp&quot;: Any&lt;String&gt;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:288:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should not include stack trace in production mode" name="Error Handler Middleware errorHandler should not include stack trace in production mode" time="0.003">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: {&quot;error&quot;: &quot;Production error&quot;, &quot;method&quot;: &quot;GET&quot;, &quot;path&quot;: &quot;/api/test&quot;, &quot;status&quot;: 500, &quot;timestamp&quot;: Any&lt;String&gt;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:309:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle request without user" name="Error Handler Middleware errorHandler should handle request without user" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;API Error:&quot;, {&quot;ip&quot;: &quot;127.0.0.1&quot;, &quot;message&quot;: &quot;Test error&quot;, &quot;method&quot;: &quot;GET&quot;, &quot;stack&quot;: Any&lt;String&gt;, &quot;url&quot;: &quot;/api/test&quot;, &quot;user&quot;: &quot;anonymous&quot;, &quot;userAgent&quot;: &quot;Mozilla/5.0&quot;}
Received: {&quot;clientIp&quot;: &quot;127.0.0.1&quot;, &quot;component&quot;: &quot;N/A&quot;, &quot;email&quot;: undefined, &quot;fileName&quot;: &quot;/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js&quot;, &quot;lineNumber&quot;: &quot;324&quot;, &quot;message&quot;: &quot;Test error&quot;, &quot;methodNameStr&quot;: &quot;Object.&lt;anonymous&gt;&quot;, &quot;module_name&quot;: &quot;Bits DS Projects Portal&quot;, &quot;requestData&quot;: &quot;:\&quot;\&quot;&quot;, &quot;stack&quot;: &quot;Error: Test error
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:324:21)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)&quot;, &quot;statusCode&quot;: 500, &quot;url&quot;: &quot;&quot;, &quot;userAgent&quot;: &quot;N/A&quot;, &quot;userId&quot;: &quot;N/A&quot;}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:328:28)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should include details for non-500 errors in production" name="Error Handler Middleware errorHandler should include details for non-500 errors in production" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
+   &quot;data&quot;: Object {
+     &quot;isSuccess&quot;: false,
+     &quot;message&quot;: &quot;Validation error&quot;,
+   },
    &quot;details&quot;: Array [
      Object {
        &quot;field&quot;: &quot;email&quot;,
        &quot;message&quot;: &quot;Email is required&quot;,
        &quot;value&quot;: null,
      },
    ],
-   &quot;error&quot;: &quot;Validation error&quot;,
    &quot;method&quot;: &quot;GET&quot;,
    &quot;path&quot;: &quot;/api/test&quot;,
    &quot;status&quot;: 400,
-   &quot;timestamp&quot;: Any&lt;String&gt;,
+   &quot;timestamp&quot;: &quot;2025-09-17T15:45:56.645Z&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:351:33)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ValidationError should have correct properties" name="Error Handler Middleware Custom Error Classes ValidationError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes NotFoundError should have correct properties" name="Error Handler Middleware Custom Error Classes NotFoundError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes NotFoundError should use default message" name="Error Handler Middleware Custom Error Classes NotFoundError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes UnauthorizedError should have correct properties" name="Error Handler Middleware Custom Error Classes UnauthorizedError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes UnauthorizedError should use default message" name="Error Handler Middleware Custom Error Classes UnauthorizedError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ForbiddenError should have correct properties" name="Error Handler Middleware Custom Error Classes ForbiddenError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ForbiddenError should use default message" name="Error Handler Middleware Custom Error Classes ForbiddenError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ConflictError should have correct properties" name="Error Handler Middleware Custom Error Classes ConflictError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ConflictError should use default message" name="Error Handler Middleware Custom Error Classes ConflictError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes BadRequestError should have correct properties" name="Error Handler Middleware Custom Error Classes BadRequestError should have correct properties" time="0.002">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes BadRequestError should use default message" name="Error Handler Middleware Custom Error Classes BadRequestError should use default message" time="0.002">
    </testcase>
    <testcase classname="Error Handler Middleware asyncHandler should handle successful async operations" name="Error Handler Middleware asyncHandler should handle successful async operations" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware asyncHandler should handle async operation errors" name="Error Handler Middleware asyncHandler should handle async operation errors" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware asyncHandler should handle synchronous errors" name="Error Handler Middleware asyncHandler should handle synchronous errors" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: [Error: Sync operation failed]

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:488:24)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Role Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:57" time="0.094" tests="31">
    <testcase classname="Role Model (Mocked) Model Definition should define Role model with correct attributes" name="Role Model (Mocked) Model Definition should define Role model with correct attributes" time="0.002">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Definition should define table name correctly" name="Role Model (Mocked) Model Definition should define table name correctly" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Definition should define indexes correctly" name="Role Model (Mocked) Model Definition should define indexes correctly" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate required fields" name="Role Model (Mocked) Model Validation should validate required fields" time="0.002">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate name length" name="Role Model (Mocked) Model Validation should validate name length" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate priority range" name="Role Model (Mocked) Model Validation should validate priority range" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate UUID format" name="Role Model (Mocked) Model Validation should validate UUID format" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate JSONB fields" name="Role Model (Mocked) Model Validation should validate JSONB fields" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should create role successfully" name="Role Model (Mocked) Model Methods should create role successfully" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find role by name" name="Role Model (Mocked) Model Methods should find role by name" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find role by ID" name="Role Model (Mocked) Model Methods should find role by ID" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find all system roles" name="Role Model (Mocked) Model Methods should find all system roles" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should update role" name="Role Model (Mocked) Model Methods should update role" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should delete role" name="Role Model (Mocked) Model Methods should delete role" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find roles by priority" name="Role Model (Mocked) Model Methods should find roles by priority" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should count roles" name="Role Model (Mocked) Model Methods should count roles" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Hooks should set default values before create" name="Role Model (Mocked) Model Hooks should set default values before create" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Hooks should validate unique role name" name="Role Model (Mocked) Model Hooks should validate unique role name" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Hooks should sanitize role name" name="Role Model (Mocked) Model Hooks should sanitize role name" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Associations should have users association" name="Role Model (Mocked) Model Associations should have users association" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Associations should have permissions association" name="Role Model (Mocked) Model Associations should have permissions association" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Scopes should have system roles scope" name="Role Model (Mocked) Model Scopes should have system roles scope" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Scopes should have by priority scope" name="Role Model (Mocked) Model Scopes should have by priority scope" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Scopes should have by name scope" name="Role Model (Mocked) Model Scopes should have by name scope" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Error Handling should handle duplicate role name error" name="Role Model (Mocked) Error Handling should handle duplicate role name error" time="0.011">
    </testcase>
    <testcase classname="Role Model (Mocked) Error Handling should handle validation error" name="Role Model (Mocked) Error Handling should handle validation error" time="0.003">
    </testcase>
    <testcase classname="Role Model (Mocked) Error Handling should handle role not found" name="Role Model (Mocked) Error Handling should handle role not found" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should check if role is system role" name="Role Model (Mocked) Business Logic should check if role is system role" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should check role hierarchy" name="Role Model (Mocked) Business Logic should check role hierarchy" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should get role permissions" name="Role Model (Mocked) Business Logic should get role permissions" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should check if role can be deleted" name="Role Model (Mocked) Business Logic should check if role can be deleted" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Workspace Controller" errors="0" failures="6" skipped="0" timestamp="2025-09-17T15:45:57" time="0.145" tests="14">
    <testcase classname="Workspace Controller forkExercise should fork exercise successfully" name="Workspace Controller forkExercise should fork exercise successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: {&quot;forkVersion&quot;: Any&lt;String&gt;, &quot;isReady&quot;: false, &quot;projectId&quot;: &quot;project-123&quot;, &quot;s3Prefix&quot;: Any&lt;String&gt;, &quot;status&quot;: &quot;creating&quot;, &quot;studentId&quot;: &quot;user-123&quot;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:129:36)</failure>
    </testcase>
    <testcase classname="Workspace Controller forkExercise should return 404 when project not found" name="Workspace Controller forkExercise should return 404 when project not found" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller forkExercise should return 400 when workspace already exists" name="Workspace Controller forkExercise should return 400 when workspace already exists" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller getWorkspaceStatus should get workspace status successfully" name="Workspace Controller getWorkspaceStatus should get workspace status successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;success&quot;: true,
-   &quot;workspace&quot;: ObjectContaining {
-     &quot;forkVersion&quot;: undefined,
-     &quot;id&quot;: &quot;workspace-123&quot;,
-     &quot;isReady&quot;: undefined,
-     &quot;projectId&quot;: &quot;project-123&quot;,
-     &quot;s3Prefix&quot;: undefined,
-     &quot;status&quot;: &quot;active&quot;,
-   },
+   &quot;error&quot;: &quot;Failed to get workspace status&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.workspaceExists is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:223:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller getWorkspaceStatus should return 404 when workspace not found" name="Workspace Controller getWorkspaceStatus should return 404 when workspace not found" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller listWorkspaceFiles should list workspace files successfully" name="Workspace Controller listWorkspaceFiles should list workspace files successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;files&quot;: Any&lt;Array&gt;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Failed to list workspace files&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.listWorkspaceFiles is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:279:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller listWorkspaceFiles should return 404 when workspace not found" name="Workspace Controller listWorkspaceFiles should return 404 when workspace not found" time="0.003">
    </testcase>
    <testcase classname="Workspace Controller uploadWorkspaceFile should upload file to workspace successfully" name="Workspace Controller uploadWorkspaceFile should upload file to workspace successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;file&quot;: Object {
-     &quot;path&quot;: &quot;test.py&quot;,
-     &quot;size&quot;: 18,
-     &quot;uploadedAt&quot;: Any&lt;Date&gt;,
-   },
-   &quot;message&quot;: &quot;File uploaded successfully&quot;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Failed to upload file&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.uploadFile is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:330:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller uploadWorkspaceFile should return 404 when workspace not found" name="Workspace Controller uploadWorkspaceFile should return 404 when workspace not found" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 404
Received: 400

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:347:35)</failure>
    </testcase>
    <testcase classname="Workspace Controller uploadWorkspaceFile should return 400 when no file provided" name="Workspace Controller uploadWorkspaceFile should return 400 when no file provided" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller deleteWorkspaceFile should delete file from workspace successfully" name="Workspace Controller deleteWorkspaceFile should delete file from workspace successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;message&quot;: &quot;File deleted successfully&quot;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Failed to delete file&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.deleteWorkspaceFile is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:395:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller deleteWorkspaceFile should return 404 when workspace not found" name="Workspace Controller deleteWorkspaceFile should return 404 when workspace not found" time="0">
    </testcase>
    <testcase classname="Workspace Controller getUserWorkspaces should get user workspaces successfully" name="Workspace Controller getUserWorkspaces should get user workspaces successfully" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller getUserWorkspaces should handle database errors" name="Workspace Controller getUserWorkspaces should handle database errors" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Sandbox Orchestrator Service" errors="0" failures="6" skipped="0" timestamp="2025-09-17T15:45:57" time="0.199" tests="23">
    <testcase classname="Sandbox Orchestrator Service createSandbox should create sandbox successfully" name="Sandbox Orchestrator Service createSandbox should create sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service createSandbox should handle sandbox creation errors" name="Sandbox Orchestrator Service createSandbox should handle sandbox creation errors" time="0.016">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service createSandbox should validate required configuration" name="Sandbox Orchestrator Service createSandbox should validate required configuration" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service deleteSandbox should delete sandbox successfully" name="Sandbox Orchestrator Service deleteSandbox should delete sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service deleteSandbox should handle sandbox deletion errors" name="Sandbox Orchestrator Service deleteSandbox should handle sandbox deletion errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service deleteSandbox should handle non-existent sandbox" name="Sandbox Orchestrator Service deleteSandbox should handle non-existent sandbox" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxStatus should get sandbox status successfully" name="Sandbox Orchestrator Service getSandboxStatus should get sandbox status successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/sandboxes/sandbox-789/status&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/json&quot;}}
Received: &quot;https://sandbox-api.example.com/sandboxes/sandbox-789/status&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:158:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxStatus should handle status retrieval errors" name="Sandbox Orchestrator Service getSandboxStatus should handle status retrieval errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service updateSandboxResources should update sandbox resources successfully" name="Sandbox Orchestrator Service updateSandboxResources should update sandbox resources successfully" time="0.001">
      <failure>Error: Resource update failed
    at updateSandboxResources (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/src/services/sandboxOrchestrator.js:88:11)
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:197:50)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service updateSandboxResources should handle resource update errors" name="Sandbox Orchestrator Service updateSandboxResources should handle resource update errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service updateSandboxResources should validate resource limits" name="Sandbox Orchestrator Service updateSandboxResources should validate resource limits" time="0.002">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service listSandboxes should list sandboxes successfully" name="Sandbox Orchestrator Service listSandboxes should list sandboxes successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/sandboxes&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/json&quot;}, &quot;params&quot;: {&quot;limit&quot;: 10, &quot;page&quot;: 1, &quot;status&quot;: &quot;running&quot;}}
Received: &quot;https://sandbox-api.example.com/sandboxes?page=1&amp;limit=10&amp;status=running&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:267:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service listSandboxes should handle listing errors" name="Sandbox Orchestrator Service listSandboxes should handle listing errors" time="0.005">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service listSandboxes should handle pagination parameters" name="Sandbox Orchestrator Service listSandboxes should handle pagination parameters" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: Any&lt;String&gt;, ObjectContaining {&quot;params&quot;: {&quot;limit&quot;: 5, &quot;page&quot;: 2}}
Received: &quot;https://sandbox-api.example.com/sandboxes?page=2&amp;limit=5&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:307:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxLogs should get sandbox logs successfully" name="Sandbox Orchestrator Service getSandboxLogs should get sandbox logs successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/sandboxes/sandbox-789/logs&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/json&quot;}, &quot;params&quot;: {&quot;level&quot;: &quot;ERROR&quot;, &quot;limit&quot;: 50}}
Received: &quot;https://sandbox-api.example.com/sandboxes/sandbox-789/logs?level=ERROR&amp;limit=50&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:347:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxLogs should handle log retrieval errors" name="Sandbox Orchestrator Service getSandboxLogs should handle log retrieval errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxLogs should handle log level filtering" name="Sandbox Orchestrator Service getSandboxLogs should handle log level filtering" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: Any&lt;String&gt;, ObjectContaining {&quot;params&quot;: {&quot;level&quot;: &quot;WARN&quot;}}
Received: &quot;https://sandbox-api.example.com/sandboxes/sandbox-789/logs?level=WARN&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:385:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service restartSandbox should restart sandbox successfully" name="Sandbox Orchestrator Service restartSandbox should restart sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service restartSandbox should handle restart errors" name="Sandbox Orchestrator Service restartSandbox should handle restart errors" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service restartSandbox should handle restart with force option" name="Sandbox Orchestrator Service restartSandbox should handle restart with force option" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service scaleSandbox should scale sandbox successfully" name="Sandbox Orchestrator Service scaleSandbox should scale sandbox successfully" time="0.002">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service scaleSandbox should handle scaling errors" name="Sandbox Orchestrator Service scaleSandbox should handle scaling errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service scaleSandbox should validate scaling limits" name="Sandbox Orchestrator Service scaleSandbox should validate scaling limits" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Utility Helper Functions" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:57" time="0.085" tests="25">
    <testcase classname="Utility Helper Functions String Utilities should capitalize first letter" name="Utility Helper Functions String Utilities should capitalize first letter" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions String Utilities should generate random string" name="Utility Helper Functions String Utilities should generate random string" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions String Utilities should slugify strings" name="Utility Helper Functions String Utilities should slugify strings" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions String Utilities should truncate strings" name="Utility Helper Functions String Utilities should truncate strings" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should remove duplicates from array" name="Utility Helper Functions Array Utilities should remove duplicates from array" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should chunk array into smaller arrays" name="Utility Helper Functions Array Utilities should chunk array into smaller arrays" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should shuffle array" name="Utility Helper Functions Array Utilities should shuffle array" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should group array by key" name="Utility Helper Functions Array Utilities should group array by key" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should deep clone object" name="Utility Helper Functions Object Utilities should deep clone object" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should pick specific keys from object" name="Utility Helper Functions Object Utilities should pick specific keys from object" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should omit specific keys from object" name="Utility Helper Functions Object Utilities should omit specific keys from object" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should check if object is empty" name="Utility Helper Functions Object Utilities should check if object is empty" time="0.005">
    </testcase>
    <testcase classname="Utility Helper Functions Date Utilities should format date" name="Utility Helper Functions Date Utilities should format date" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Date Utilities should check if date is valid" name="Utility Helper Functions Date Utilities should check if date is valid" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Date Utilities should get relative time" name="Utility Helper Functions Date Utilities should get relative time" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should format number with commas" name="Utility Helper Functions Number Utilities should format number with commas" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should round to decimal places" name="Utility Helper Functions Number Utilities should round to decimal places" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should check if number is in range" name="Utility Helper Functions Number Utilities should check if number is in range" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should generate random number in range" name="Utility Helper Functions Number Utilities should generate random number in range" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Validation Utilities should validate email format" name="Utility Helper Functions Validation Utilities should validate email format" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Validation Utilities should validate URL format" name="Utility Helper Functions Validation Utilities should validate URL format" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Validation Utilities should validate phone number format" name="Utility Helper Functions Validation Utilities should validate phone number format" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions File Utilities should get file extension" name="Utility Helper Functions File Utilities should get file extension" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions File Utilities should format file size" name="Utility Helper Functions File Utilities should format file size" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions File Utilities should validate file type" name="Utility Helper Functions File Utilities should validate file type" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AGS Service" errors="0" failures="20" skipped="0" timestamp="2025-09-17T15:45:58" time="0.351" tests="20">
    <testcase classname="AGS Service parseAgsClaim should parse valid AGS claim" name="AGS Service parseAgsClaim should parse valid AGS claim" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service parseAgsClaim should return null for missing AGS claim" name="AGS Service parseAgsClaim should return null for missing AGS claim" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service parseAgsClaim should handle parsing errors gracefully" name="AGS Service parseAgsClaim should handle parsing errors gracefully" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service ensureLineItem should create new line item when none exists" name="AGS Service ensureLineItem should create new line item when none exists" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service ensureLineItem should return existing line item when found" name="AGS Service ensureLineItem should return existing line item when found" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service ensureLineItem should validate required parameters" name="AGS Service ensureLineItem should validate required parameters" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service postScore should post score successfully" name="AGS Service postScore should post score successfully" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service postScore should validate required parameters" name="AGS Service postScore should validate required parameters" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service postScore should validate progress values" name="AGS Service postScore should validate progress values" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service postScore should handle API errors" name="AGS Service postScore should handle API errors" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service listResults should list results successfully" name="AGS Service listResults should list results successfully" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service listResults should handle user filter" name="AGS Service listResults should handle user filter" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service listResults should validate line item ID" name="AGS Service listResults should validate line item ID" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service listLineItems should list line items successfully" name="AGS Service listLineItems should list line items successfully" time="0.007">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service listLineItems should handle filters" name="AGS Service listLineItems should handle filters" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service listLineItems should validate line items URL" name="AGS Service listLineItems should validate line items URL" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service getServiceStatus should return service status" name="AGS Service getServiceStatus should return service status" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service getServiceStatus should handle service unavailable" name="AGS Service getServiceStatus should handle service unavailable" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service _makeAgsRequest should retry on server errors" name="AGS Service _makeAgsRequest should retry on server errors" time="0.001">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="AGS Service _makeAgsRequest should not retry on client errors" name="AGS Service _makeAgsRequest should not retry on client errors" time="0">
      <failure>TypeError: _agsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/agsService.test.js:16:18)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="User Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:58" time="0.075" tests="25">
    <testcase classname="User Model (Mocked) Model Definition should define User model with correct attributes" name="User Model (Mocked) Model Definition should define User model with correct attributes" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Definition should define table name correctly" name="User Model (Mocked) Model Definition should define table name correctly" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Definition should define timestamps" name="User Model (Mocked) Model Definition should define timestamps" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Validation should validate email format" name="User Model (Mocked) Model Validation should validate email format" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Validation should validate required fields" name="User Model (Mocked) Model Validation should validate required fields" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Validation should validate UUID format" name="User Model (Mocked) Model Validation should validate UUID format" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should create user successfully" name="User Model (Mocked) Model Methods should create user successfully" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find user by email" name="User Model (Mocked) Model Methods should find user by email" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find user by ID" name="User Model (Mocked) Model Methods should find user by ID" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find all active users" name="User Model (Mocked) Model Methods should find all active users" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should update user" name="User Model (Mocked) Model Methods should update user" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should delete user" name="User Model (Mocked) Model Methods should delete user" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find or create user" name="User Model (Mocked) Model Methods should find or create user" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should count users" name="User Model (Mocked) Model Methods should count users" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Hooks should hash password before create" name="User Model (Mocked) Model Hooks should hash password before create" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Hooks should hash password before update" name="User Model (Mocked) Model Hooks should hash password before update" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Hooks should exclude password from JSON" name="User Model (Mocked) Model Hooks should exclude password from JSON" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Associations should have roles association" name="User Model (Mocked) Model Associations should have roles association" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Associations should have courses association" name="User Model (Mocked) Model Associations should have courses association" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Associations should have projects association" name="User Model (Mocked) Model Associations should have projects association" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Scopes should have active scope" name="User Model (Mocked) Model Scopes should have active scope" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Scopes should have inactive scope" name="User Model (Mocked) Model Scopes should have inactive scope" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Error Handling should handle duplicate email error" name="User Model (Mocked) Error Handling should handle duplicate email error" time="0.008">
    </testcase>
    <testcase classname="User Model (Mocked) Error Handling should handle validation error" name="User Model (Mocked) Error Handling should handle validation error" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Error Handling should handle user not found" name="User Model (Mocked) Error Handling should handle user not found" time="0">
    </testcase>
  </testsuite>
  <testsuite name="LTI Controller" errors="0" failures="12" skipped="0" timestamp="2025-09-17T15:45:58" time="0.328" tests="12">
    <testcase classname="LTI Controller getLTIConfiguration should return LTI configuration" name="LTI Controller getLTIConfiguration should return LTI configuration" time="0.001">
      <failure>TypeError: (0 , _ltiService.getLTIConfigurationData) is not a function
    at /Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/src/controllers/lti.controller.js:387:48
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:124:32)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller oidcInit should initiate OIDC login successfully" name="LTI Controller oidcInit should initiate OIDC login successfully" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:158:36)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller oidcInit should handle missing parameters" name="LTI Controller oidcInit should handle missing parameters" time="0.001">
      <failure>TypeError: (0 , _ltiController.oidcInit) is not a function
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:181:21)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller oidcCallback should handle OIDC callback successfully" name="LTI Controller oidcCallback should handle OIDC callback successfully" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:211:37)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller oidcCallback should handle missing code and state" name="LTI Controller oidcCallback should handle missing code and state" time="0">
      <failure>TypeError: (0 , _ltiController.oidcCallback) is not a function
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:226:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller getJWKS should return JWKS" name="LTI Controller getJWKS should return JWKS" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockReturnValue&apos;)
    at Object.mockReturnValue (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:251:31)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller handleDeepLinking should handle deep linking request" name="LTI Controller handleDeepLinking should handle deep linking request" time="0">
      <failure>TypeError: (0 , _ltiService.handleDeepLinkingRequest) is not a function
    at /Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/src/controllers/lti.controller.js:225:48
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:276:30)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller registerPlatform should register platform successfully" name="LTI Controller registerPlatform should register platform successfully" time="0.001">
      <failure>TypeError: (0 , _ltiService.registerPlatformData) is not a function
    at /Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/src/controllers/lti.controller.js:237:46
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:296:29)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller getLTISession should return LTI session data" name="LTI Controller getLTISession should return LTI session data" time="0.001">
      <failure>TypeError: (0 , _ltiController.getLTISession) is not a function
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:321:26)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller getLTISession should return 401 when no LTI session" name="LTI Controller getLTISession should return 401 when no LTI session" time="0.001">
      <failure>TypeError: (0 , _ltiController.getLTISession) is not a function
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:355:26)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller cleanupLTISessions should cleanup expired sessions successfully" name="LTI Controller cleanupLTISessions should cleanup expired sessions successfully" time="0.006">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:367:41)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller cleanupLTISessions should handle cleanup errors" name="LTI Controller cleanupLTISessions should handle cleanup errors" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockRejectedValue&apos;)
    at Object.mockRejectedValue (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:381:41)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="LTI Role Service" errors="0" failures="36" skipped="0" timestamp="2025-09-17T15:45:58" time="0.288" tests="36">
    <testcase classname="LTI Role Service parseRolesFromLaunch should parse valid roles from launch data" name="LTI Role Service parseRolesFromLaunch should parse valid roles from launch data" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service parseRolesFromLaunch should handle missing roles claim" name="LTI Role Service parseRolesFromLaunch should handle missing roles claim" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service parseRolesFromLaunch should handle empty roles array" name="LTI Role Service parseRolesFromLaunch should handle empty roles array" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service parseRolesFromLaunch should handle parsing errors gracefully" name="LTI Role Service parseRolesFromLaunch should handle parsing errors gracefully" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRolePermissions should return permissions for single role" name="LTI Role Service getRolePermissions should return permissions for single role" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRolePermissions should return permissions for multiple roles" name="LTI Role Service getRolePermissions should return permissions for multiple roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRolePermissions should cache permissions" name="LTI Role Service getRolePermissions should cache permissions" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRolePermissions should handle empty roles" name="LTI Role Service getRolePermissions should handle empty roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRolePermissions should handle null roles" name="LTI Role Service getRolePermissions should handle null roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasPermission should return true for valid permission" name="LTI Role Service hasPermission should return true for valid permission" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasPermission should return false for invalid permission" name="LTI Role Service hasPermission should return false for invalid permission" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasPermission should handle empty roles" name="LTI Role Service hasPermission should handle empty roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasPermission should handle null roles" name="LTI Role Service hasPermission should handle null roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAnyPermission should return true if any role has permission" name="LTI Role Service hasAnyPermission should return true if any role has permission" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAnyPermission should return false if no role has permission" name="LTI Role Service hasAnyPermission should return false if no role has permission" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAnyPermission should handle empty permissions" name="LTI Role Service hasAnyPermission should handle empty permissions" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAnyPermission should handle null permissions" name="LTI Role Service hasAnyPermission should handle null permissions" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAllPermissions should return true if all roles have all permissions" name="LTI Role Service hasAllPermissions should return true if all roles have all permissions" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAllPermissions should return false if not all permissions are met" name="LTI Role Service hasAllPermissions should return false if not all permissions are met" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service hasAllPermissions should handle empty permissions" name="LTI Role Service hasAllPermissions should handle empty permissions" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service validateRoles should validate correct roles" name="LTI Role Service validateRoles should validate correct roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service validateRoles should identify invalid roles" name="LTI Role Service validateRoles should identify invalid roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service validateRoles should handle empty roles" name="LTI Role Service validateRoles should handle empty roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleHierarchy should return hierarchy for valid roles" name="LTI Role Service getRoleHierarchy should return hierarchy for valid roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleHierarchy should handle empty roles" name="LTI Role Service getRoleHierarchy should handle empty roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleHierarchy should handle null roles" name="LTI Role Service getRoleHierarchy should handle null roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleLevel should return correct level for known roles" name="LTI Role Service getRoleLevel should return correct level for known roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleLevel should return default level for unknown roles" name="LTI Role Service getRoleLevel should return default level for unknown roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service canElevateRole should return true for elevatable roles" name="LTI Role Service canElevateRole should return true for elevatable roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service canElevateRole should return false for system admin" name="LTI Role Service canElevateRole should return false for system admin" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleSummary should return complete role summary" name="LTI Role Service getRoleSummary should return complete role summary" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleSummary should handle empty roles" name="LTI Role Service getRoleSummary should handle empty roles" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service getRoleSummary should handle null roles" name="LTI Role Service getRoleSummary should handle null roles" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service Cache Management should clear cache" name="LTI Role Service Cache Management should clear cache" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service Cache Management should return cache statistics" name="LTI Role Service Cache Management should return cache statistics" time="0.001">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Role Service Logging should log role information" name="LTI Role Service Logging should log role information" time="0">
      <failure>TypeError: _ltiRoleService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/rbac/ltiRoleService.test.js:8:22)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Validation Middleware" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:59" time="0.064" tests="20">
    <testcase classname="Validation Middleware Email Validation should validate correct email format" name="Validation Middleware Email Validation should validate correct email format" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Email Validation should reject invalid email format" name="Validation Middleware Email Validation should reject invalid email format" time="0.002">
    </testcase>
    <testcase classname="Validation Middleware UUID Validation should validate correct UUID format" name="Validation Middleware UUID Validation should validate correct UUID format" time="0">
    </testcase>
    <testcase classname="Validation Middleware UUID Validation should reject invalid UUID format" name="Validation Middleware UUID Validation should reject invalid UUID format" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Required Field Validation should validate required fields present" name="Validation Middleware Required Field Validation should validate required fields present" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Required Field Validation should reject missing required fields" name="Validation Middleware Required Field Validation should reject missing required fields" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Required Field Validation should handle empty string as missing field" name="Validation Middleware Required Field Validation should handle empty string as missing field" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware String Length Validation should validate string length within limits" name="Validation Middleware String Length Validation should validate string length within limits" time="0">
    </testcase>
    <testcase classname="Validation Middleware String Length Validation should reject strings outside length limits" name="Validation Middleware String Length Validation should reject strings outside length limits" time="0">
    </testcase>
    <testcase classname="Validation Middleware Numeric Validation should validate numeric values" name="Validation Middleware Numeric Validation should validate numeric values" time="0">
    </testcase>
    <testcase classname="Validation Middleware Numeric Validation should reject invalid numeric values" name="Validation Middleware Numeric Validation should reject invalid numeric values" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Date Validation should validate date formats" name="Validation Middleware Date Validation should validate date formats" time="0.005">
    </testcase>
    <testcase classname="Validation Middleware Date Validation should reject invalid date formats" name="Validation Middleware Date Validation should reject invalid date formats" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Array Validation should validate array contents" name="Validation Middleware Array Validation should validate array contents" time="0">
    </testcase>
    <testcase classname="Validation Middleware Array Validation should reject invalid arrays" name="Validation Middleware Array Validation should reject invalid arrays" time="0.002">
    </testcase>
    <testcase classname="Validation Middleware Object Validation should validate object structure" name="Validation Middleware Object Validation should validate object structure" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Object Validation should reject invalid objects" name="Validation Middleware Object Validation should reject invalid objects" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware File Validation should validate file types" name="Validation Middleware File Validation should validate file types" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware File Validation should reject invalid file types" name="Validation Middleware File Validation should reject invalid file types" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware File Validation should validate file size" name="Validation Middleware File Validation should validate file size" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Database Configuration" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:59" time="0.067" tests="18">
    <testcase classname="Database Configuration Environment Configuration should configure development environment" name="Database Configuration Environment Configuration should configure development environment" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Environment Configuration should configure production environment" name="Database Configuration Environment Configuration should configure production environment" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Environment Configuration should configure test environment" name="Database Configuration Environment Configuration should configure test environment" time="0">
    </testcase>
    <testcase classname="Database Configuration Default Values should use default port when not specified" name="Database Configuration Default Values should use default port when not specified" time="0">
    </testcase>
    <testcase classname="Database Configuration Default Values should use default dialect" name="Database Configuration Default Values should use default dialect" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Default Values should handle missing environment variables" name="Database Configuration Default Values should handle missing environment variables" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Configuration Validation should validate required database fields" name="Database Configuration Configuration Validation should validate required database fields" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Configuration Validation should validate port number" name="Database Configuration Configuration Validation should validate port number" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Configuration Validation should validate host format" name="Database Configuration Configuration Validation should validate host format" time="0.004">
    </testcase>
    <testcase classname="Database Configuration Connection String Generation should generate connection string" name="Database Configuration Connection String Generation should generate connection string" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Connection String Generation should handle special characters in password" name="Database Configuration Connection String Generation should handle special characters in password" time="0.001">
    </testcase>
    <testcase classname="Database Configuration SSL Configuration should configure SSL for production" name="Database Configuration SSL Configuration should configure SSL for production" time="0.001">
    </testcase>
    <testcase classname="Database Configuration SSL Configuration should not configure SSL for development" name="Database Configuration SSL Configuration should not configure SSL for development" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Pool Configuration should configure connection pool" name="Database Configuration Pool Configuration should configure connection pool" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Pool Configuration should use environment variables for pool config" name="Database Configuration Pool Configuration should use environment variables for pool config" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Logging Configuration should enable logging for development" name="Database Configuration Logging Configuration should enable logging for development" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Logging Configuration should disable logging for production" name="Database Configuration Logging Configuration should disable logging for production" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Logging Configuration should disable logging for test" name="Database Configuration Logging Configuration should disable logging for test" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Role Mapping Configuration" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:59" time="0.114" tests="36">
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should map Instructor to ProjectOwner" name="Role Mapping Configuration mapLtiRolesToInternal should map Instructor to ProjectOwner" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should map TeachingAssistant to Grader" name="Role Mapping Configuration mapLtiRolesToInternal should map TeachingAssistant to Grader" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should map Learner to Student" name="Role Mapping Configuration mapLtiRolesToInternal should map Learner to Student" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should map Administrator to Administrator" name="Role Mapping Configuration mapLtiRolesToInternal should map Administrator to Administrator" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should handle multiple roles" name="Role Mapping Configuration mapLtiRolesToInternal should handle multiple roles" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should handle empty roles array" name="Role Mapping Configuration mapLtiRolesToInternal should handle empty roles array" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should handle null/undefined roles" name="Role Mapping Configuration mapLtiRolesToInternal should handle null/undefined roles" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration mapLtiRolesToInternal should handle unmapped roles" name="Role Mapping Configuration mapLtiRolesToInternal should handle unmapped roles" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration getHighestPriorityRole should return highest priority role" name="Role Mapping Configuration getHighestPriorityRole should return highest priority role" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration getHighestPriorityRole should handle single role" name="Role Mapping Configuration getHighestPriorityRole should handle single role" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration getHighestPriorityRole should handle empty roles" name="Role Mapping Configuration getHighestPriorityRole should handle empty roles" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration getHighestPriorityRole should handle null/undefined roles" name="Role Mapping Configuration getHighestPriorityRole should handle null/undefined roles" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration hasPermission should return true for valid permission" name="Role Mapping Configuration hasPermission should return true for valid permission" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration hasPermission should return false for invalid permission" name="Role Mapping Configuration hasPermission should return false for invalid permission" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration hasPermission should handle null role" name="Role Mapping Configuration hasPermission should handle null role" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration hasPermission should handle undefined role" name="Role Mapping Configuration hasPermission should handle undefined role" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration hasAnyPermission should return true if any role has permission" name="Role Mapping Configuration hasAnyPermission should return true if any role has permission" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration hasAnyPermission should return false if no role has permission" name="Role Mapping Configuration hasAnyPermission should return false if no role has permission" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration hasAnyPermission should handle empty roles" name="Role Mapping Configuration hasAnyPermission should handle empty roles" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration hasAnyPermission should handle null roles" name="Role Mapping Configuration hasAnyPermission should handle null roles" time="0.003">
    </testcase>
    <testcase classname="Role Mapping Configuration getAllPermissions should return all permissions for multiple roles" name="Role Mapping Configuration getAllPermissions should return all permissions for multiple roles" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration getAllPermissions should return unique permissions" name="Role Mapping Configuration getAllPermissions should return unique permissions" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration getAllPermissions should handle empty roles" name="Role Mapping Configuration getAllPermissions should handle empty roles" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration getAllPermissions should handle null roles" name="Role Mapping Configuration getAllPermissions should handle null roles" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration canAccessRoute should allow access for valid permission" name="Role Mapping Configuration canAccessRoute should allow access for valid permission" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration canAccessRoute should deny access for invalid permission" name="Role Mapping Configuration canAccessRoute should deny access for invalid permission" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration canAccessRoute should allow access for routes without permission requirements" name="Role Mapping Configuration canAccessRoute should allow access for routes without permission requirements" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration canAccessRoute should handle empty roles" name="Role Mapping Configuration canAccessRoute should handle empty roles" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration getRoutePermission should return correct permission for known route" name="Role Mapping Configuration getRoutePermission should return correct permission for known route" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration getRoutePermission should return null for unknown route" name="Role Mapping Configuration getRoutePermission should return null for unknown route" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration getRoutePermission should handle different HTTP methods" name="Role Mapping Configuration getRoutePermission should handle different HTTP methods" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration Configuration Constants should have valid IMS_ROLES" name="Role Mapping Configuration Configuration Constants should have valid IMS_ROLES" time="0">
    </testcase>
    <testcase classname="Role Mapping Configuration Configuration Constants should have valid INTERNAL_ROLES" name="Role Mapping Configuration Configuration Constants should have valid INTERNAL_ROLES" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration Configuration Constants should have valid ROLE_MAPPING" name="Role Mapping Configuration Configuration Constants should have valid ROLE_MAPPING" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration Configuration Constants should have valid ROLE_HIERARCHY" name="Role Mapping Configuration Configuration Constants should have valid ROLE_HIERARCHY" time="0.001">
    </testcase>
    <testcase classname="Role Mapping Configuration Configuration Constants should have valid PERMISSIONS" name="Role Mapping Configuration Configuration Constants should have valid PERMISSIONS" time="0">
    </testcase>
  </testsuite>
  <testsuite name="NRPS Service" errors="0" failures="14" skipped="0" timestamp="2025-09-17T15:45:59" time="0.174" tests="14">
    <testcase classname="NRPS Service parseNrpsClaim should parse valid NRPS claim" name="NRPS Service parseNrpsClaim should parse valid NRPS claim" time="0.001">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service parseNrpsClaim should return null for missing NRPS claim" name="NRPS Service parseNrpsClaim should return null for missing NRPS claim" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service parseNrpsClaim should handle parsing errors gracefully" name="NRPS Service parseNrpsClaim should handle parsing errors gracefully" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service listMembers should list members successfully" name="NRPS Service listMembers should list members successfully" time="0.001">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service listMembers should handle query parameters" name="NRPS Service listMembers should handle query parameters" time="0.001">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service listMembers should handle API errors" name="NRPS Service listMembers should handle API errors" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service listMembers should validate NRPS URL" name="NRPS Service listMembers should validate NRPS URL" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service getMembersByRole should filter members by role" name="NRPS Service getMembersByRole should filter members by role" time="0.001">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service getInstructors should call getMembersByRole with instructor role" name="NRPS Service getInstructors should call getMembersByRole with instructor role" time="0.001">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service getStudents should call getMembersByRole with learner role" name="NRPS Service getStudents should call getMembersByRole with learner role" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service validateNrpsResponse should validate correct NRPS response" name="NRPS Service validateNrpsResponse should validate correct NRPS response" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service validateNrpsResponse should reject invalid NRPS response" name="NRPS Service validateNrpsResponse should reject invalid NRPS response" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service getServiceStatus should return service status" name="NRPS Service getServiceStatus should return service status" time="0.004">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="NRPS Service getServiceStatus should handle service unavailable" name="NRPS Service getServiceStatus should handle service unavailable" time="0">
      <failure>TypeError: _nrpsService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/nrpsService.test.js:16:19)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Validation Utilities" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:45:59" time="0.057" tests="14">
    <testcase classname="Validation Utilities Email Validation should validate correct email formats" name="Validation Utilities Email Validation should validate correct email formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Email Validation should reject invalid email formats" name="Validation Utilities Email Validation should reject invalid email formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities UUID Validation should validate correct UUID formats" name="Validation Utilities UUID Validation should validate correct UUID formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities UUID Validation should reject invalid UUID formats" name="Validation Utilities UUID Validation should reject invalid UUID formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Password Validation should validate strong passwords" name="Validation Utilities Password Validation should validate strong passwords" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Password Validation should reject weak passwords" name="Validation Utilities Password Validation should reject weak passwords" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities URL Validation should validate correct URL formats" name="Validation Utilities URL Validation should validate correct URL formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities URL Validation should reject invalid URL formats" name="Validation Utilities URL Validation should reject invalid URL formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Date Validation should validate correct date formats" name="Validation Utilities Date Validation should validate correct date formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Date Validation should reject invalid date formats" name="Validation Utilities Date Validation should reject invalid date formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities File Extension Validation should validate allowed file extensions" name="Validation Utilities File Extension Validation should validate allowed file extensions" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities File Extension Validation should reject disallowed file extensions" name="Validation Utilities File Extension Validation should reject disallowed file extensions" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Input Sanitization should sanitize HTML content" name="Validation Utilities Input Sanitization should sanitize HTML content" time="0.004">
    </testcase>
    <testcase classname="Validation Utilities Input Sanitization should trim whitespace" name="Validation Utilities Input Sanitization should trim whitespace" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="LTI Token Cache Service" errors="0" failures="15" skipped="0" timestamp="2025-09-17T15:45:59" time="0.154" tests="15">
    <testcase classname="LTI Token Cache Service generateCacheKey should generate consistent cache keys" name="LTI Token Cache Service generateCacheKey should generate consistent cache keys" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service generateCacheKey should handle different scopes" name="LTI Token Cache Service generateCacheKey should handle different scopes" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service getCachedToken should return null when no cached token exists" name="LTI Token Cache Service getCachedToken should return null when no cached token exists" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service getCachedToken should return cached token when valid" name="LTI Token Cache Service getCachedToken should return cached token when valid" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service getCachedToken should return null and delete expired token" name="LTI Token Cache Service getCachedToken should return null and delete expired token" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service getCachedToken should handle Redis errors gracefully" name="LTI Token Cache Service getCachedToken should handle Redis errors gracefully" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service cacheToken should cache token successfully" name="LTI Token Cache Service cacheToken should cache token successfully" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service cacheToken should handle Redis errors gracefully" name="LTI Token Cache Service cacheToken should handle Redis errors gracefully" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service invalidateToken should invalidate specific token" name="LTI Token Cache Service invalidateToken should invalidate specific token" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service invalidateToken should handle Redis errors gracefully" name="LTI Token Cache Service invalidateToken should handle Redis errors gracefully" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service invalidatePlatformTokens should invalidate all platform tokens" name="LTI Token Cache Service invalidatePlatformTokens should invalidate all platform tokens" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service invalidatePlatformTokens should handle no tokens found" name="LTI Token Cache Service invalidatePlatformTokens should handle no tokens found" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service getCacheStats should return cache statistics" name="LTI Token Cache Service getCacheStats should return cache statistics" time="0.001">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service getCacheStats should handle Redis errors" name="LTI Token Cache Service getCacheStats should handle Redis errors" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Token Cache Service cleanupExpiredTokens should cleanup expired tokens" name="LTI Token Cache Service cleanupExpiredTokens should cleanup expired tokens" time="0">
      <failure>TypeError: _ltiTokenCacheService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/tests/unit/services/ltiTokenCache.test.js:22:25)
    at Promise.then.completed (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/Projects/OTTL/bits_ds/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Logger Configuration" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:46:00" time="0.055" tests="11">
    <testcase classname="Logger Configuration Environment Configuration should configure development logger" name="Logger Configuration Environment Configuration should configure development logger" time="0.002">
    </testcase>
    <testcase classname="Logger Configuration Environment Configuration should configure production logger" name="Logger Configuration Environment Configuration should configure production logger" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Environment Configuration should configure test logger" name="Logger Configuration Environment Configuration should configure test logger" time="0">
    </testcase>
    <testcase classname="Logger Configuration Log Level Configuration should handle different log levels" name="Logger Configuration Log Level Configuration should handle different log levels" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Log Level Configuration should use default log level when not specified" name="Logger Configuration Log Level Configuration should use default log level when not specified" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Log Formatting should format log messages correctly" name="Logger Configuration Log Formatting should format log messages correctly" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Log Formatting should handle log messages with metadata" name="Logger Configuration Log Formatting should handle log messages with metadata" time="0.002">
    </testcase>
    <testcase classname="Logger Configuration Error Handling should handle logger errors gracefully" name="Logger Configuration Error Handling should handle logger errors gracefully" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Error Handling should handle invalid log levels" name="Logger Configuration Error Handling should handle invalid log levels" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Performance Logging should log performance metrics" name="Logger Configuration Performance Logging should log performance metrics" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Security Logging should log security events" name="Logger Configuration Security Logging should log security events" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="Health Endpoint (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:46:00" time="0.044" tests="5">
    <testcase classname="Health Endpoint (Mocked) GET /health should return 200 with health status" name="Health Endpoint (Mocked) GET /health should return 200 with health status" time="0.002">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should validate response structure" name="Health Endpoint (Mocked) GET /health should validate response structure" time="0.002">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should handle different environments" name="Health Endpoint (Mocked) GET /health should handle different environments" time="0.003">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should include database status when available" name="Health Endpoint (Mocked) GET /health should include database status when available" time="0.001">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should handle service failures" name="Health Endpoint (Mocked) GET /health should handle service failures" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Utility Functions" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:46:00" time="0.04" tests="6">
    <testcase classname="Utility Functions should perform basic math operations" name="Utility Functions should perform basic math operations" time="0.001">
    </testcase>
    <testcase classname="Utility Functions should handle string operations" name="Utility Functions should handle string operations" time="0">
    </testcase>
    <testcase classname="Utility Functions should handle array operations" name="Utility Functions should handle array operations" time="0.001">
    </testcase>
    <testcase classname="Utility Functions should handle object operations" name="Utility Functions should handle object operations" time="0.001">
    </testcase>
    <testcase classname="Utility Functions should handle async operations" name="Utility Functions should handle async operations" time="0.001">
    </testcase>
    <testcase classname="Utility Functions should handle error cases" name="Utility Functions should handle error cases" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Health Check" errors="0" failures="0" skipped="0" timestamp="2025-09-17T15:46:00" time="0.031" tests="2">
    <testcase classname="Health Check should pass basic health check" name="Health Check should pass basic health check" time="0.001">
    </testcase>
    <testcase classname="Health Check should handle basic math" name="Health Check should handle basic math" time="0.001">
    </testcase>
  </testsuite>
</testsuites>