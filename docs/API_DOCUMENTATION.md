# BITS-DataScience Projects Platform API Documentation

## Overview

The BITS-DataScience Projects Platform provides a comprehensive REST API for managing role-based learning, project assignments, submissions, and grading. The platform integrates with D2L-Brightspace LMS via LTI 1.3 OIDC, supports Google OAuth authentication, and uses AWS S3 for file storage.

## Base URL

```
Production: https://api.bits-datascience.edu
Development: http://localhost:5001
```

## Authentication

The API supports multiple authentication methods:

### Authentication Methods

1. **JWT Authentication**: Traditional username/password login
2. **Google OAuth**: Social login integration
3. **LTI 1.3 OIDC**: Learning Management System integration (Brightspace D2L)

### Authentication Flow

#### JWT/Google OAuth Flow
1. **Login**: `POST /api/auth/login` or `POST /api/auth/google`
2. **Token Generation**: Receive JWT access token
3. **API Access**: Include JWT in Authorization header

#### LTI 1.3 OIDC Flow
1. **OIDC Initiation**: `GET/POST /api/lti/oidc/init`
2. **Platform Authentication**: Redirect to Brightspace D2L
3. **OIDC Callback**: `POST /api/lti/oidc/callback`
4. **Session Creation**: LTI session established
5. **API Access**: Use LTI session for protected endpoints

### Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## API Endpoints

### Project Assignment Management

#### POST /api/project-assignments
Create a new project assignment.

**Request Body:**
```json
{
  "project_id": "uuid",
  "user_id": "uuid",
  "role": "instructor|ta|reviewer|mentor",
  "assignment_type": "primary|secondary|guest",
  "permissions": {},
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-12-31T23:59:59Z",
  "notes": "string",
  "metadata": {}
}
```

#### GET /api/project-assignments
Get all project assignments with filtering and pagination.

**Query Parameters:**
- `project_id`: Filter by project ID
- `user_id`: Filter by user ID
- `role`: Filter by role
- `assignment_type`: Filter by assignment type
- `is_active`: Filter by active status
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `sort_by`: Sort field (default: assigned_at)
- `sort_order`: Sort order ASC/DESC (default: DESC)

#### GET /api/project-assignments/:id
Get a specific project assignment.

#### PUT /api/project-assignments/:id
Update a project assignment.

#### DELETE /api/project-assignments/:id
Delete a project assignment (soft delete).

#### GET /api/project-assignments/user/:userId
Get assignments for a specific user.

#### POST /api/project-assignments/bulk
Bulk create project assignments.

**Request Body:**
```json
{
  "assignments": [
    {
      "project_id": "uuid",
      "user_id": "uuid",
      "role": "instructor|ta|reviewer|mentor"
    }
  ]
}
```

#### GET /api/project-assignments/project/:projectId/history
Get assignment history for a project.

#### GET /api/project-assignments/stats
Get assignment statistics.

---

### Rubric Management

#### POST /api/rubrics
Create a new rubric.

**Request Body:**
```json
{
  "project_id": "uuid",
  "title": "string",
  "description": "string",
  "criteria": [
    {
      "name": "string",
      "description": "string",
      "points": 25
    }
  ],
  "total_points": 100,
  "grading_scale": {},
  "is_template": false,
  "template_name": "string",
  "checkpoint_mapping": {},
  "metadata": {}
}
```

#### GET /api/rubrics
Get all rubrics with filtering and pagination.

**Query Parameters:**
- `project_id`: Filter by project ID
- `created_by`: Filter by creator
- `is_template`: Filter by template status
- `template_name`: Filter by template name
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `sort_by`: Sort field (default: created_at)
- `sort_order`: Sort order ASC/DESC (default: DESC)

#### GET /api/rubrics/:id
Get a specific rubric.

#### PUT /api/rubrics/:id
Update a rubric.

#### DELETE /api/rubrics/:id
Delete a rubric.

#### GET /api/rubrics/templates
Get rubric templates.

#### POST /api/rubrics/:id/duplicate
Duplicate a rubric template for a project.

#### GET /api/rubrics/project/:projectId
Get rubrics for a specific project.

#### GET /api/rubrics/stats
Get rubric statistics.

---

### Authentication & Session Management

#### POST /api/auth/google
Google OAuth authentication and LMS synchronization.

**Request Body:**
```json
{
  "googleToken": "string",
  "lmsData": {
    "userId": "string",
    "email": "string",
    "name": "string",
    "roles": ["string"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "string",
    "email": "string",
    "role": "student|instructor|admin",
    "status": "active|inactive|pending"
  },
  "tokens": {
    "accessToken": "string",
    "refreshToken": "string",
    "expiresIn": 3600
  }
}
```

#### POST /api/auth/refresh
Refresh JWT access token.

**Request Body:**
```json
{
  "refreshToken": "string"
}
```

#### POST /api/auth/logout
Logout and invalidate tokens.

#### POST /api/auth/logout-all
Logout from all devices.

#### GET /api/auth/logout/stats
Get logout blacklist statistics (Admin only).

---

### LTI 1.3 Integration

#### GET /api/lti/config
Get LTI tool configuration for platform registration.

**Response:**
```json
{
  "title": "BITS-DataScience Projects Platform",
  "description": "Interactive data science projects and assignments platform for BITS Pilani",
  "target_link_uri": "http://localhost:5001/lti/launch",
  "oidc_initiation_url": "http://localhost:5001/api/lti/oidc/init",
  "public_jwk_url": "http://localhost:5001/.well-known/jwks.json",
  "scopes": [
    "openid",
    "https://purl.imsglobal.org/spec/lti-ags/scope/lineitem",
    "https://purl.imsglobal.org/spec/lti-ags/scope/score",
    "https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly"
  ]
}
```

#### GET/POST /api/lti/oidc/init
OIDC login initiation endpoint.

**Query Parameters:**
- `iss`: Platform issuer identifier
- `login_hint`: User identifier hint
- `target_link_uri`: Target URI for launch
- `client_id`: OAuth client ID
- `lti_message_hint`: LTI message hint

#### POST /api/lti/oidc/callback
OIDC callback endpoint.

**Request Body:**
```json
{
  "code": "authorization-code",
  "state": "state-parameter"
}
```

#### GET /api/lti/session
Get current LTI session data.

**Response:**
```json
{
  "success": true,
  "session": {
    "user": {
      "id": "uuid",
      "email": "string",
      "name": "string",
      "roles": ["string"]
    },
    "context": {
      "id": "uuid",
      "contextId": "string",
      "title": "string",
      "label": "string"
    },
    "resourceLink": {
      "id": "uuid",
      "resourceLinkId": "string",
      "title": "string"
    },
    "launchData": {
      "messageType": "string",
      "version": "string",
      "deploymentId": "string"
    }
  }
}
```

#### GET /api/lti/roster/:contextId
Get course roster (LTI NRPS).

#### POST /api/lti/grades
Submit grade (LTI AGS).

**Request Body:**
```json
{
  "lineItemId": "string",
  "userId": "string",
  "score": 85,
  "maxScore": 100,
  "comment": "string"
}
```

#### POST /api/lti/cleanup
Clean up expired LTI sessions (Admin only).

---

### User Management

#### GET /api/users
Get paginated list of users (Admin/Instructor only).

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search by name or email
- `role`: Filter by role
- `status`: Filter by status

**Response:**
```json
{
  "success": true,
  "users": [
    {
      "id": "uuid",
      "name": "string",
      "email": "string",
      "role": "string",
      "status": "string",
      "profileData": {},
      "createdAt": "datetime",
      "lastLoginAt": "datetime"
    }
  ],
  "pagination": {
    "current": 1,
    "total": 10,
    "pages": 5,
    "limit": 10
  }
}
```

#### GET /api/users/profile
Get current user profile.

#### PUT /api/users/profile
Update current user profile.

**Request Body:**
```json
{
  "name": "string",
  "profileData": {
    "firstName": "string",
    "lastName": "string",
    "bio": "string",
    "preferences": {}
  }
}
```

#### GET /api/users/:id
Get user by ID (Admin/Instructor only).

#### PUT /api/users/:id/status
Update user status (Admin only).

**Request Body:**
```json
{
  "status": "active|inactive|suspended"
}
```

#### DELETE /api/users/:id
Soft delete user (Admin only).

---

### Course Management

#### GET /api/courses
Get courses for current user.

#### GET /api/courses/project-creation
Get courses for project creation dropdown (instructor/TA only).

**Response:**
```json
{
  "success": true,
  "message": "Project creation courses retrieved successfully",
  "data": {
    "courses": [
      {
        "id": "uuid",
        "name": "Introduction to Data Science",
        "code": "DS101",
        "term": "Fall 2024",
        "academicYear": "2024-2025",
        "startDate": "2024-09-01T00:00:00.000Z",
        "endDate": "2024-12-15T23:59:59.000Z",
        "userRole": "instructor",
        "isPrimaryInstructor": true
      }
    ],
    "total": 1
  }
}
```

#### GET /api/courses/dropdown/{role}
Get course dropdown for specific user role (instructor, ta, student).

**Parameters:**
- `role`: User role (instructor, ta, student)

#### GET /api/courses/{courseId}/user-role
Validate user's role in a specific course.

**Response:**
```json
{
  "success": true,
  "message": "User course role retrieved successfully",
  "data": {
    "courseId": "uuid",
    "userId": "uuid",
    "role": "instructor",
    "status": "active",
    "enrolledAt": "2024-09-01T00:00:00.000Z",
    "user": {
      "id": "uuid",
      "name": "Dr. John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

#### GET /api/courses/{courseId}/can-create-projects
Check if user can create projects in a specific course.

**Response:**
```json
{
  "success": true,
  "message": "Project creation permission checked successfully",
  "data": {
    "courseId": "uuid",
    "userId": "uuid",
    "canCreate": true,
    "reason": "User has teaching role in this course",
    "userRole": "instructor"
  }
}
```

#### GET /api/courses/{courseId}/teaching-staff
Get teaching staff for a specific course.

#### GET /api/courses/{courseId}/summary
Get course summary for dropdown display.

### Permission Feedback System

#### GET /api/permissions/summary
Get user's permission summary for UI display.

**Response:**
```json
{
  "success": true,
  "message": "Permission summary retrieved successfully",
  "data": {
    "userId": "uuid",
    "totalPermissions": 15,
    "totalRoles": 2,
    "categories": ["project", "course", "user"],
    "permissionsByCategory": {
      "project": [
        {
          "key": "project:create",
          "name": "Create Projects",
          "description": "Ability to create new projects"
        }
      ]
    },
    "primaryRole": "Instructor",
    "hasAdminAccess": false,
    "hasProjectAccess": true,
    "hasCourseAccess": true,
    "hasTemplateAccess": true
  }
}
```

#### GET /api/permissions/resource/{resourceType}/{resourceId}
Get user's permissions for specific resource.

**Parameters:**
- `resourceType`: Type of resource (project, course, user, submission, grade, project_template)
- `resourceId`: Resource ID (optional)

#### GET /api/permissions/actions/{resourceType}/{resourceId}
Get available actions for user based on permissions.

#### POST /api/permissions/check-action
Check if user can perform specific action.

**Request Body:**
```json
{
  "action": "create_project",
  "resourceType": "project",
  "resourceId": "uuid"
}
```

#### GET /api/permissions/ui-context/{resourceType}/{resourceId}
Get UI context for permissions (feature flags, UI elements).

**Response:**
```json
{
  "success": true,
  "message": "UI context retrieved successfully",
  "data": {
    "resourceType": "project",
    "resourceId": "uuid",
    "accessLevel": "manage",
    "hasAccess": true,
    "uiElements": {
      "showCreateButton": true,
      "showEditButton": true,
      "showDeleteButton": false,
      "showPublishButton": true
    },
    "featureFlags": {
      "canCreate": true,
      "canEdit": true,
      "canDelete": false,
      "canPublish": true
    }
  }
}
```

**Query Parameters:**
- `status`: Filter by course status
- `semester`: Filter by semester
- `search`: Search course name or code

**Response:**
```json
{
  "success": true,
  "courses": [
    {
      "id": "uuid",
      "name": "string",
      "code": "string",
      "description": "string",
      "semester": "string",
      "status": "active|archived",
      "instructor": {
        "id": "uuid",
        "name": "string"
      },
      "enrollmentCount": 0,
      "projectCount": 0
    }
  ]
}
```

#### POST /api/courses
Create new course (Instructor/Admin only).

**Request Body:**
```json
{
  "name": "string",
  "code": "string",
  "description": "string",
  "semester": "string",
  "settings": {
    "allowLateSubmissions": true,
    "autoGrading": false,
    "maxAttempts": 3
  }
}
```

#### GET /api/courses/:id
Get course details.

#### PUT /api/courses/:id
Update course (Instructor/Admin only).

#### DELETE /api/courses/:id
Delete course (Admin only).

#### GET /api/courses/:id/enrollments
Get course enrollments.

#### POST /api/courses/:id/enroll
Enroll user in course.

**Request Body:**
```json
{
  "userId": "uuid",
  "role": "student|ta"
}
```

#### DELETE /api/courses/:id/enrollments/:userId
Remove user from course.

---

## Announcements System

The announcements system allows instructors to create, manage, and publish course-wide announcements with support for scheduling, priority levels, and target audiences.

### Endpoints

#### **Create Announcement**
```http
POST /api/announcements
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Project Deadline Extended",
  "content": "The deadline for Project 2 has been extended to Friday...",
  "courseId": "uuid",
  "announcementType": "deadline_reminder",
  "priority": "high",
  "isPinned": true,
  "targetAudience": ["students"],
  "attachments": []
}
```

#### **Get Course Announcements**
```http
GET /api/announcements/course/:courseId
Authorization: Bearer <token>
Query Parameters:
- status (optional): String - Filter by status (all, draft, published, archived)
- announcementType (optional): String - Filter by type
- priority (optional): String - Filter by priority level
- isPinned (optional): Boolean - Filter by pin status
- page (optional): Number - Page number (default: 1)
- limit (optional): Number - Items per page (default: 20, max: 100)
- includeExpired (optional): Boolean - Include expired announcements
```

#### **Get Announcement by ID**
```http
GET /api/announcements/:id
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Announcement ID
```

#### **Update Announcement**
```http
PUT /api/announcements/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Project Deadline",
  "content": "Updated content...",
  "priority": "urgent"
}
```

#### **Publish Announcement**
```http
POST /api/announcements/:id/publish
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Announcement ID
```

#### **Archive Announcement**
```http
POST /api/announcements/:id/archive
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Announcement ID
```

#### **Toggle Pin Status**
```http
POST /api/announcements/:id/toggle-pin
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Announcement ID
```

#### **Delete Announcement**
```http
DELETE /api/announcements/:id
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Announcement ID
```

#### **Get Instructor Announcements**
```http
GET /api/announcements/instructor
Authorization: Bearer <token>
Query Parameters:
- courseId (optional): UUID - Filter by specific course
```

#### **Get Student Announcements**
```http
GET /api/announcements/student
Authorization: Bearer <token>
Query Parameters:
- courseId (optional): UUID - Filter by specific course
```

#### **Schedule Announcement**
```http
POST /api/announcements/:id/schedule
Authorization: Bearer <token>
Content-Type: application/json

{
  "scheduledFor": "2025-09-15T09:00:00Z"
}
```

---

## Messages System

The messages system enables direct communication between users with support for threaded conversations, course/project context, and bulk messaging capabilities.

### Endpoints

#### **Send Message**
```http
POST /api/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "recipientId": "uuid",
  "subject": "Question about Project 1",
  "content": "I have a question regarding the data preprocessing...",
  "messageType": "project_related",
  "priority": "normal",
  "courseId": "uuid",
  "projectId": "uuid"
}
```

#### **Reply to Message**
```http
POST /api/messages/:id/reply
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Re: Question about Project 1",
  "content": "Great question! Here's the answer...",
  "attachments": []
}
```

#### **Get Inbox**
```http
GET /api/messages/inbox
Authorization: Bearer <token>
Query Parameters:
- status (optional): String - Filter by status (all, sent, delivered, read, archived)
- messageType (optional): String - Filter by message type
- priority (optional): String - Filter by priority level
- isRead (optional): Boolean - Filter by read status
- page (optional): Number - Page number (default: 1)
- limit (optional): Number - Items per page (default: 20, max: 100)
- courseId (optional): UUID - Filter by course
- projectId (optional): UUID - Filter by project
```

#### **Get Sent Messages**
```http
GET /api/messages/sent
Authorization: Bearer <token>
Query Parameters:
- messageType (optional): String - Filter by message type
- priority (optional): String - Filter by priority level
- page (optional): Number - Page number (default: 1)
- limit (optional): Number - Items per page (default: 20, max: 100)
- courseId (optional): UUID - Filter by course
- projectId (optional): UUID - Filter by project
```

#### **Get Conversation Thread**
```http
GET /api/messages/thread/:threadId
Authorization: Bearer <token>
Path Parameters:
- threadId: UUID - Thread ID
```

#### **Mark Message as Read**
```http
POST /api/messages/:id/read
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Message ID
```

#### **Mark Multiple Messages as Read**
```http
POST /api/messages/mark-read
Authorization: Bearer <token>
Content-Type: application/json

{
  "messageIds": ["uuid1", "uuid2", "uuid3"]
}
```

#### **Get Message by ID**
```http
GET /api/messages/:id
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Message ID
```

#### **Update Message**
```http
PUT /api/messages/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Updated Subject",
  "content": "Updated content..."
}
```

#### **Delete Message**
```http
DELETE /api/messages/:id
Authorization: Bearer <token>
Path Parameters:
- id: UUID - Message ID
```

#### **Get Unread Count**
```http
GET /api/messages/unread-count
Authorization: Bearer <token>
```

#### **Get Instructor Message Statistics**
```http
GET /api/messages/instructor/stats
Authorization: Bearer <token>
```

#### **Send Course Announcement (Bulk Messaging)**
```http
POST /api/messages/course-announcement
Authorization: Bearer <token>
Content-Type: application/json

{
  "courseId": "uuid",
  "subject": "Important Course Update",
  "content": "Please note the following changes...",
  "priority": "high"
}
```

---

### Student Dashboard

The Student Dashboard provides comprehensive project progress tracking, deadline management, and activity monitoring for students.

#### GET /api/student/dashboard
Get comprehensive student dashboard with project overview, deadlines, and recent activity.

**Query Parameters:**
- `courseId` (optional): Filter by specific course UUID

**Response:**
```json
{
  "success": true,
  "message": "Student dashboard retrieved successfully",
  "data": {
    "studentId": "uuid",
    "projectOverview": {
      "totalProjects": 5,
      "activeProjects": 2,
      "completedProjects": 2,
      "overdueProjects": 1,
      "averageProgress": 65.5,
      "projects": [
        {
          "id": "uuid",
          "title": "Data Analysis Project",
          "course": {
            "id": "uuid",
            "name": "Introduction to Data Science",
            "code": "DS101"
          },
          "status": "in_progress",
          "progress": 75.0,
          "dueDate": "2024-12-15T23:59:59Z",
          "startDate": "2024-10-01T00:00:00Z",
          "endDate": "2024-12-15T23:59:59Z",
          "lastActivity": "2024-11-15T10:30:00Z",
          "timeSpent": 25.5,
          "grade": null,
          "difficultyLevel": "intermediate",
          "estimatedHours": 40
        }
      ]
    },
    "upcomingDeadlines": [
      {
        "projectId": "uuid",
        "projectTitle": "Final Project Submission",
        "course": {
          "id": "uuid",
          "name": "Advanced Analytics",
          "code": "DS201"
        },
        "dueDate": "2024-12-20T23:59:59Z",
        "daysUntilDue": 5,
        "status": "in_progress",
        "progress": 85.0
      }
    ],
    "recentActivity": [
      {
        "id": "uuid",
        "type": "checkpoint_submitted",
        "title": "Checkpoint 2 Submitted",
        "description": "You submitted Checkpoint 2 for Data Analysis Project",
        "timestamp": "2024-11-15T10:30:00Z",
        "isRead": false,
        "project": {
          "id": "uuid",
          "title": "Data Analysis Project"
        },
        "course": {
          "id": "uuid",
          "name": "Introduction to Data Science",
          "code": "DS101"
        }
      }
    ],
    "courseProgress": [
      {
        "courseId": "uuid",
        "courseName": "Introduction to Data Science",
        "courseCode": "DS101",
        "totalProjects": 3,
        "completedProjects": 2,
        "activeProjects": 1,
        "averageProgress": 78.3,
        "totalTimeSpent": 45.5
      }
    ],
    "lastUpdated": "2024-11-15T10:30:00Z"
  }
}
```

#### GET /api/student/projects/:projectId/progress
Get detailed progress for a specific project including checkpoint status.

**Path Parameters:**
- `projectId`: Project UUID

**Response:**
```json
{
  "success": true,
  "message": "Project progress retrieved successfully",
  "data": {
    "projectId": "uuid",
    "projectTitle": "Data Analysis Project",
    "status": "in_progress",
    "progressPercentage": 75.0,
    "currentPhase": "Data Collection",
    "timeSpent": 25.5,
    "startDate": "2024-10-01T00:00:00Z",
    "completionDate": null,
    "grade": null,
    "feedback": null,
    "checkpoints": [
      {
        "id": "uuid",
        "title": "Project Proposal",
        "description": "Submit project proposal and timeline",
        "dueDate": "2024-10-15T23:59:59Z",
        "status": "completed",
        "submittedAt": "2024-10-14T16:30:00Z",
        "grade": 95.0,
        "feedback": "Excellent proposal with clear objectives"
      }
    ],
    "lastActivity": "2024-11-15T10:30:00Z",
    "projectDetails": {
      "dueDate": "2024-12-15T23:59:59Z",
      "difficultyLevel": "intermediate",
      "estimatedHours": 40
    }
  }
}
```

#### GET /api/student/projects/overview
Get student's project overview with progress summary.

**Query Parameters:**
- `courseId` (optional): Filter by specific course UUID

**Response:**
```json
{
  "success": true,
  "message": "Student project overview retrieved successfully",
  "data": {
    "totalProjects": 5,
    "activeProjects": 2,
    "completedProjects": 2,
    "overdueProjects": 1,
    "averageProgress": 65.5,
    "projects": [...]
  }
}
```

#### GET /api/student/projects/stats
Get student's project statistics and metrics.

**Query Parameters:**
- `courseId` (optional): Filter by specific course UUID

**Response:**
```json
{
  "success": true,
  "message": "Student project statistics retrieved successfully",
  "data": {
    "totalProjects": 5,
    "totalTimeSpent": 67.5,
    "byStatus": {
      "not_started": 0,
      "in_progress": 2,
      "completed": 2,
      "overdue": 1
    },
    "statuses": ["not_started", "in_progress", "completed", "overdue"]
  }
}
```

#### GET /api/student/deadlines
Get upcoming project deadlines with countdown.

**Query Parameters:**
- `days` (optional): Number of days to look ahead (default: 30, max: 365)

**Response:**
```json
{
  "success": true,
  "message": "Upcoming deadlines retrieved successfully",
  "data": {
    "deadlines": [
      {
        "projectId": "uuid",
        "projectTitle": "Final Project Submission",
        "course": {
          "id": "uuid",
          "name": "Advanced Analytics",
          "code": "DS201"
        },
        "dueDate": "2024-12-20T23:59:59Z",
        "daysUntilDue": 5,
        "status": "in_progress",
        "progress": 85.0
      }
    ],
    "total": 1,
    "daysAhead": 30
  }
}
```

#### GET /api/student/activity
Get student's recent activity and notifications.

**Query Parameters:**
- `limit` (optional): Number of activities to return (default: 10, max: 100)
- `activityType` (optional): Filter by activity type

**Activity Types:**
- `project_started`: Student started working on a project
- `checkpoint_submitted`: Checkpoint submission
- `project_completed`: Project completion
- `grade_received`: Grade received for project/checkpoint
- `feedback_received`: Feedback received from instructor
- `deadline_approaching`: Project deadline approaching
- `course_enrolled`: Enrolled in a new course

**Response:**
```json
{
  "success": true,
  "message": "Recent activity retrieved successfully",
  "data": {
    "activities": [
      {
        "id": "uuid",
        "type": "checkpoint_submitted",
        "title": "Checkpoint 2 Submitted",
        "description": "You submitted Checkpoint 2 for Data Analysis Project",
        "timestamp": "2024-11-15T10:30:00Z",
        "isRead": false,
        "project": {
          "id": "uuid",
          "title": "Data Analysis Project"
        },
        "course": {
          "id": "uuid",
          "name": "Introduction to Data Science",
          "code": "DS101"
        }
      }
    ],
    "total": 1,
    "limit": 10,
    "activityType": "all"
  }
}
```

#### GET /api/student/activity/unread-count
Get count of unread activities for notification badges.

**Response:**
```json
{
  "success": true,
  "message": "Unread activity count retrieved successfully",
  "data": {
    "unreadCount": 3
  }
}
```

#### GET /api/student/courses/:courseId/progress
Get student's progress summary for a specific course.

**Path Parameters:**
- `courseId`: Course UUID

**Response:**
```json
{
  "success": true,
  "message": "Course progress retrieved successfully",
  "data": [
    {
      "courseId": "uuid",
      "courseName": "Introduction to Data Science",
      "courseCode": "DS101",
      "totalProjects": 3,
      "completedProjects": 2,
      "activeProjects": 1,
      "averageProgress": 78.3,
      "totalTimeSpent": 45.5
    }
  ]
}
```

#### PUT /api/student/projects/:projectId/progress
Update project progress (start, update, complete).

**Path Parameters:**
- `projectId`: Project UUID

**Request Body:**
```json
{
  "courseId": "uuid",
  "progressPercentage": 75.0,
  "currentPhase": "Data Analysis",
  "timeSpent": 25.5,
  "status": "in_progress"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Project progress updated successfully",
  "data": {
    "id": "uuid",
    "student_id": "uuid",
    "project_id": "uuid",
    "course_id": "uuid",
    "progress_percentage": 75.0,
    "current_phase": "Data Analysis",
    "time_spent_hours": 25.5,
    "status": "in_progress",
    "last_activity": "2024-11-15T10:30:00Z"
  }
}
```

#### PUT /api/student/activity/:activityId/read
Mark activity notification as read.

**Path Parameters:**
- `activityId`: Activity UUID

**Response:**
```json
{
  "success": true,
  "message": "Activity marked as read successfully",
  "data": {
    "id": "uuid",
    "is_read": true,
    "timestamp": "2024-11-15T10:30:00Z"
  }
}
```

---

### Project Management

#### GET /api/projects
Get projects based on user role and course access.

**Query Parameters:**
- `courseId`: Filter by course
- `status`: Filter by status
- `difficulty`: Filter by difficulty level
- `search`: Search title or description

**Response:**
```json
{
  "success": true,
  "projects": [
    {
      "id": "uuid",
      "title": "string",
      "description": "string",
      "courseId": "uuid",
      "difficultyLevel": "beginner|intermediate|advanced",
      "status": "draft|published|archived",
      "dueDate": "datetime",
      "maxAttempts": 3,
      "isTemplate": false,
      "rubrics": [
        {
          "id": "uuid",
          "criteria": "string",
          "maxScore": 100,
          "weight": 0.3
        }
      ],
      "course": {
        "name": "string",
        "code": "string"
      }
    }
  ]
}
```

#### POST /api/projects
Create new project (Instructor/Admin only).

**Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "courseId": "uuid",
  "difficultyLevel": "beginner|intermediate|advanced",
  "dueDate": "datetime",
  "maxAttempts": 3,
  "instructions": "string",
  "requirements": ["string"],
  "resources": [
    {
      "type": "dataset|notebook|documentation",
      "name": "string",
      "url": "string",
      "description": "string"
    }
  ],
  "rubrics": [
    {
      "criteria": "string",
      "description": "string",
      "maxScore": 100,
      "weight": 0.3
    }
  ],
  "isTemplate": false
}
```

#### GET /api/projects/:id
Get project details.

#### PUT /api/projects/:id
Update project (Creator/Admin only).

#### DELETE /api/projects/:id
Delete project (Creator/Admin only).

#### POST /api/projects/:id/duplicate
Duplicate project as template.

#### GET /api/projects/templates
Get project templates.

#### GET /api/projects/assignments/user/:role?
Get projects by user assignment.

**Parameters:**
- `role`: User role (instructor, ta, reviewer, mentor) - optional

**Response:**
```json
{
  "success": true,
  "message": "User project assignments retrieved successfully",
  "data": {
    "assignments": [
      {
        "assignmentId": "uuid",
        "role": "instructor",
        "assignmentType": "primary",
        "assignedAt": "2024-01-15T10:30:00Z",
        "startDate": "2024-01-15T10:30:00Z",
        "endDate": "2024-06-15T23:59:59Z",
        "project": {
          "id": "uuid",
          "title": "Machine Learning Classification",
          "status": "published",
          "course": {
            "id": "uuid",
            "name": "Data Science 101",
            "code": "DS101"
          },
          "creator": {
            "id": "uuid",
            "name": "Dr. Smith",
            "email": "<EMAIL>"
          }
        }
      }
    ],
    "total": 1,
    "role": "instructor"
  }
}
```

#### GET /api/projects/workload
Get user's project workload and statistics.

**Response:**
```json
{
  "success": true,
  "message": "User project workload retrieved successfully",
  "data": {
    "totalProjects": 5,
    "activeProjects": 3,
    "draftProjects": 2,
    "totalEstimatedHours": 120,
    "byRole": {
      "instructor": {
        "count": 3,
        "projects": []
      },
      "ta": {
        "count": 2,
        "projects": []
      }
    },
    "upcomingDeadlines": [
      {
        "projectId": "uuid",
        "projectTitle": "ML Classification",
        "dueDate": "2024-02-01T23:59:59Z",
        "daysUntilDue": 15
      }
    ]
  }
}
```

#### GET /api/projects/:id/assignment-stats
Get project assignment statistics.

**Response:**
```json
{
  "success": true,
  "message": "Project assignment statistics retrieved successfully",
  "data": {
    "totalAssignments": 4,
    "byRole": {
      "instructor": 1,
      "ta": 2,
      "reviewer": 1
    },
    "roles": ["instructor", "ta", "reviewer"]
  }
}
```

---

### Instructor Dashboard

The instructor dashboard provides comprehensive analytics, progress tracking, and activity monitoring for instructors managing courses and projects.

#### GET /api/instructor/dashboard
Get comprehensive dashboard overview with aggregate statistics.

**Query Parameters:**
- `courseId` (optional): Filter by specific course

**Response:**
```json
{
  "success": true,
  "dashboard": {
    "totalProjects": 8,
    "activeProjects": 6,
    "draftProjects": 1,
    "archivedProjects": 1,
    "totalStudents": 180,
    "activeStudents": 165,
    "totalSubmissions": 142,
    "pendingGrades": 18,
    "averageGrade": 79.8,
    "recentActivity": [
      {
        "id": "uuid",
        "type": "checkpoint_submitted",
        "description": "Submitted checkpoint for review",
        "user": {"id": "uuid", "name": "John Doe", "email": "<EMAIL>"},
        "project": {"id": "uuid", "title": "ML Classification"},
        "course": {"id": "uuid", "name": "Data Science 101", "code": "DS101"},
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ],
    "courseOverview": [
      {
        "courseId": "uuid",
        "name": "Data Science 101",
        "code": "DS101",
        "totalProjects": 3,
        "activeProjects": 2,
        "totalStudents": 45,
        "totalSubmissions": 38,
        "averageGrade": 82.3
      }
    ],
    "projectProgress": [
      {
        "projectId": "uuid",
        "title": "Machine Learning Classification",
        "status": "published",
        "startDate": "2024-01-01T00:00:00Z",
        "dueDate": "2024-02-01T00:00:00Z",
        "course": {"id": "uuid", "name": "DS101", "code": "DS101"},
        "progressPercentage": 75.5,
        "totalStudents": 45,
        "activeStudents": 42,
        "submissionsCount": 38,
        "gradedCount": 35,
        "pendingGrades": 3,
        "averageGrade": 82.3,
        "checkpointCompletionRate": 68.2,
        "lastActivity": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### GET /api/instructor/projects
Get enhanced project listing with comprehensive statistics for instructors.

**Query Parameters:**
- `courseId` (optional): Filter by specific course
- `status` (optional): Filter by project status (draft|published|archived)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `includeStats` (optional): Include detailed statistics (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "projects": [
      {
        "id": "uuid",
        "title": "Machine Learning Classification",
        "status": "published",
        "startDate": "2024-01-01T00:00:00Z",
        "dueDate": "2024-02-01T00:00:00Z",
        "progressPercentage": 75.5,
        "totalStudents": 45,
        "activeStudents": 42,
        "submissionsCount": 38,
        "gradedCount": 35,
        "pendingGrades": 3,
        "averageGrade": 82.3,
        "checkpoints": 4,
        "course": {"id": "uuid", "name": "DS101", "code": "DS101"},
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "dashboardStats": {
      "totalProjects": 8,
      "activeProjects": 6,
      "totalStudents": 180,
      "totalSubmissions": 142,
      "pendingGrades": 18,
      "averageGrade": 79.8
    },
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 8,
      "pages": 1
    }
  }
}
```

#### GET /api/instructor/projects/:id/stats
Get detailed project statistics and analytics.

**Response:**
```json
{
  "success": true,
  "projectStats": {
    "project": {
      "id": "uuid",
      "title": "Machine Learning Classification",
      "status": "published",
      "startDate": "2024-01-01T00:00:00Z",
      "dueDate": "2024-02-01T00:00:00Z",
      "course": {"id": "uuid", "name": "DS101", "code": "DS101"}
    },
    "statistics": {
      "totalStudents": 45,
      "activeStudents": 42,
      "submissionsCount": 38,
      "gradedCount": 35,
      "pendingGrades": 3,
      "averageGrade": 82.3,
      "progressPercentage": 75.5,
      "checkpointCompletionRate": 68.2,
      "lastActivity": "2024-01-15T10:30:00Z"
    },
    "checkpointBreakdown": [
      {
        "id": "uuid",
        "title": "Data Preprocessing",
        "checkpointNumber": 1,
        "totalStudents": 45,
        "completed": 38,
        "inProgress": 5,
        "submitted": 2,
        "notStarted": 0,
        "completionRate": 84.4
      }
    ],
    "recentSubmissions": [
      {
        "id": "uuid",
        "status": "submitted",
        "submittedAt": "2024-01-15T10:30:00Z",
        "student": {"id": "uuid", "name": "John Doe", "email": "<EMAIL>"},
        "grade": null
      }
    ]
  }
}
```

#### GET /api/instructor/activity
Get recent activity timeline for courses and projects.

**Query Parameters:**
- `courseId` (optional): Filter by specific course
- `projectId` (optional): Filter by specific project
- `limit` (optional): Number of activities (default: 20, max: 100)

**Response:**
```json
{
  "success": true,
  "activities": [
    {
      "id": "uuid",
      "type": "checkpoint_submitted",
      "description": "Submitted checkpoint for review",
      "user": {"id": "uuid", "name": "John Doe", "email": "<EMAIL>"},
      "project": {"id": "uuid", "title": "ML Classification"},
      "course": {"id": "uuid", "name": "DS101", "code": "DS101"},
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### GET /api/instructor/analytics
Get activity analytics and performance metrics.

**Query Parameters:**
- `courseId` (optional): Filter by specific course
- `startDate` (optional): Start date for analysis (ISO8601 format)
- `endDate` (optional): End date for analysis (ISO8601 format)
- `activityType` (optional): Filter by activity type

**Response:**
```json
{
  "success": true,
  "analytics": {
    "activityCounts": [
      {"type": "checkpoint_submitted", "count": 45},
      {"type": "grade_assigned", "count": 32},
      {"type": "project_created", "count": 3}
    ],
    "dailyActivity": [
      {"date": "2024-01-15", "count": 12},
      {"date": "2024-01-16", "count": 8},
      {"date": "2024-01-17", "count": 15}
    ],
    "totalActivities": 77
  }
}
```

#### GET /api/instructor/courses/:id/overview
Get comprehensive course overview with statistics.

**Response:**
```json
{
  "success": true,
  "courseOverview": {
    "courseId": "uuid",
    "name": "Data Science 101",
    "code": "DS101",
    "term": "Fall 2024",
    "totalProjects": 3,
    "activeProjects": 2,
    "totalStudents": 45,
    "totalSubmissions": 38,
    "averageGrade": 82.3
  }
}
```

#### GET /api/instructor/users/:id/activity
Get user activity summary for specific user.

**Query Parameters:**
- `limit` (optional): Number of activities (default: 50, max: 100)

**Response:**
```json
{
  "success": true,
  "activities": [
    {
      "id": "uuid",
      "type": "checkpoint_submitted",
      "description": "Submitted checkpoint for review",
      "project": {"id": "uuid", "title": "ML Classification"},
      "course": {"id": "uuid", "name": "DS101", "code": "DS101"},
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

### Submission Management

#### GET /api/submissions
Get submissions based on user role.

**Query Parameters:**
- `projectId`: Filter by project
- `userId`: Filter by user (Instructor/Admin only)
- `status`: Filter by status
- `graded`: Filter by grading status

**Response:**
```json
{
  "success": true,
  "submissions": [
    {
      "id": "uuid",
      "projectId": "uuid",
      "userId": "uuid",
      "status": "draft|submitted|graded",
      "submittedAt": "datetime",
      "attempt": 1,
      "notebooks": [
        {
          "name": "string",
          "s3Key": "string",
          "size": 1024,
          "lastModified": "datetime"
        }
      ],
      "files": [
        {
          "name": "string",
          "s3Key": "string",
          "type": "string",
          "size": 1024
        }
      ],
      "project": {
        "title": "string",
        "dueDate": "datetime"
      },
      "user": {
        "name": "string",
        "email": "string"
      },
      "grade": {
        "totalScore": 85,
        "maxScore": 100,
        "feedback": "string"
      }
    }
  ]
}
```

#### POST /api/submissions
Create new submission.

**Request Body:**
```json
{
  "projectId": "uuid",
  "notebooks": [
    {
      "name": "string",
      "content": "string",
      "cellOutputs": []
    }
  ],
  "files": [
    {
      "name": "string",
      "s3Key": "string",
      "type": "string"
    }
  ],
  "metadata": {
    "environment": "jupyter",
    "packages": ["pandas", "numpy"],
    "executionTime": 1200
  }
}
```

#### GET /api/submissions/:id
Get submission details.

#### PUT /api/submissions/:id
Update submission (Owner only, before submission).

#### POST /api/submissions/:id/submit
Submit for grading.

#### POST /api/submissions/:id/auto-save
Auto-save submission progress.

**Request Body:**
```json
{
  "notebooks": [
    {
      "name": "string",
      "content": "string",
      "cellOutputs": []
    }
  ]
}
```

#### GET /api/submissions/:id/files/:filename
Download submission file.

---

### Grade Management

#### GET /api/grades
Get grades based on user role.

**Query Parameters:**
- `submissionId`: Filter by submission
- `projectId`: Filter by project
- `userId`: Filter by user
- `evaluatorId`: Filter by evaluator

**Response:**
```json
{
  "success": true,
  "grades": [
    {
      "id": "uuid",
      "submissionId": "uuid",
      "evaluatorId": "uuid",
      "totalScore": 85,
      "maxScore": 100,
      "feedback": "string",
      "rubricScores": [
        {
          "rubricId": "uuid",
          "score": 25,
          "maxScore": 30,
          "feedback": "string"
        }
      ],
      "gradedAt": "datetime",
      "submission": {
        "project": {
          "title": "string"
        },
        "user": {
          "name": "string"
        }
      }
    }
  ]
}
```

#### POST /api/grades
Create grade for submission (Instructor/Admin only).

**Request Body:**
```json
{
  "submissionId": "uuid",
  "rubricScores": [
    {
      "rubricId": "uuid",
      "score": 25,
      "feedback": "string"
    }
  ],
  "overallFeedback": "string",
  "sendNotification": true
}
```

#### PUT /api/grades/:id
Update grade (Evaluator/Admin only).

#### DELETE /api/grades/:id
Delete grade (Admin only).

#### POST /api/grades/bulk
Bulk grade submissions.

**Request Body:**
```json
{
  "grades": [
    {
      "submissionId": "uuid",
      "rubricScores": [],
      "overallFeedback": "string"
    }
  ]
}
```

#### GET /api/grades/analytics/:projectId
Get grading analytics for project.

---

### Role & Permission Management

#### GET /api/roles
Get all roles (Admin only).

#### POST /api/roles
Create new role (Admin only).

**Request Body:**
```json
{
  "name": "string",
  "description": "string",
  "permissions": ["uuid"]
}
```

#### GET /api/roles/permissions
Get all permissions.

#### PUT /api/roles/:id/permissions
Update role permissions (Admin only).

#### POST /api/users/:userId/roles
Assign role to user (Admin only).

#### DELETE /api/users/:userId/roles/:roleId
Remove role from user (Admin only).

---

### File Storage (S3 Integration)

#### POST /api/s3/upload
Get presigned URL for file upload.

**Request Body:**
```json
{
  "fileName": "string",
  "fileType": "string",
  "purpose": "submission|project|profile",
  "metadata": {
    "projectId": "uuid",
    "submissionId": "uuid"
  }
}
```

**Response:**
```json
{
  "success": true,
  "uploadUrl": "string",
  "s3Key": "string",
  "downloadUrl": "string"
}
```

#### GET /api/s3/download/:s3Key
Get presigned URL for file download.

#### DELETE /api/s3/:s3Key
Delete file from S3 (Owner/Admin only).

#### GET /api/s3/list
List user's files.

**Query Parameters:**
- `purpose`: Filter by purpose
- `projectId`: Filter by project

---

### Sandbox Environment Management

#### POST /api/sandbox/create
Create a new sandbox environment for data science projects.

**Request Body:**
```json
{
  "userId": "uuid",
  "projectId": "uuid",
  "resources": {
    "cpu": "string",
    "memory": "string",
    "storage": "string"
  },
  "environment": "string",
  "autoStartServer": "boolean"
}
```

**Response:**
```json
{
  "success": true,
  "sandboxId": "string",
  "workspace": {
    "workspaceId": "string",
    "s3Prefix": "string"
  },
  "jupyterhubUser": "string",
  "jupyterhubUserCreated": true,
  "serverStarted": true,
  "message": "string"
}
```

#### DELETE /api/sandbox/:sandboxId
Delete a sandbox environment.

**Response:**
```json
{
  "success": true,
  "message": "Sandbox deleted successfully"
}
```

#### GET /api/sandbox/:sandboxId/status
Get sandbox environment status.

**Response:**
```json
{
  "success": true,
  "status": "creating|running|stopped|error",
  "resources": {
    "cpu": "string",
    "memory": "string",
    "storage": "string"
  },
  "uptime": "string",
  "url": "string"
}
```

#### PUT /api/sandbox/:sandboxId/resources
Update sandbox resource allocation.

**Request Body:**
```json
{
  "cpu": "string",
  "memory": "string",
  "storage": "string"
}
```

#### GET /api/sandbox/list
List user's sandbox environments.

**Query Parameters:**
- `userId`: Filter by user ID
- `projectId`: Filter by project ID
- `status`: Filter by status

**Response:**
```json
{
  "success": true,
  "userId": "string",
  "sandboxes": [
    {
      "sandboxId": "string",
      "projectId": "string",
      "status": "string",
      "createdAt": "string",
      "serverStatus": "string"
    }
  ],
  "count": "number"
}
```

#### GET /api/sandbox/:sandboxId/logs
Get sandbox operation logs.

**Query Parameters:**
- `level`: Log level (INFO|WARN|ERROR)
- `limit`: Number of log entries (default: 50)
- `since`: ISO timestamp for filtering

#### POST /api/sandbox/:sandboxId/restart
Restart a sandbox environment.

**Request Body:**
```json
{
  "force": "boolean"
}
```

#### POST /api/sandbox/:sandboxId/scale
Scale sandbox resources.

**Request Body:**
```json
{
  "replicas": "number",
  "autoScaling": "boolean"
}
```

#### GET /api/sandbox/health
Get sandbox service health status.

**Response:**
```json
{
  "success": true,
  "status": "healthy|degraded|unhealthy",
  "services": {
    "jupyterhub": "string",
    "sandboxApi": "string",
    "s3Workspace": "string"
  },
  "timestamp": "string"
}
```

---

### JupyterHub Management

#### POST /api/jupyterhub/users
Create a new JupyterHub user.

**Request Body:**
```json
{
  "name": "string",
  "admin": "boolean",
  "groups": ["string"]
}
```

#### DELETE /api/jupyterhub/users/:username
Delete a JupyterHub user.

#### GET /api/jupyterhub/users/:username
Get JupyterHub user information.

#### GET /api/jupyterhub/users
List all JupyterHub users.

#### POST /api/jupyterhub/users/:username/servers/:serverName
Start a JupyterHub server for a user.

**Request Body:**
```json
{
  "image": "string",
  "resources": {
    "cpu": "string",
    "memory": "string"
  }
}
```

#### DELETE /api/jupyterhub/users/:username/servers/:serverName
Stop a JupyterHub server.

#### GET /api/jupyterhub/users/:username/servers/:serverName
Get server status.

#### GET /api/jupyterhub/users/:username/servers
List user's servers.

#### POST /api/jupyterhub/groups
Create a new JupyterHub group.

**Request Body:**
```json
{
  "name": "string",
  "users": ["string"]
}
```

#### DELETE /api/jupyterhub/groups/:groupName
Delete a JupyterHub group.

#### GET /api/jupyterhub/groups
List all JupyterHub groups.

#### POST /api/jupyterhub/groups/:groupName/users
Add users to a group.

#### DELETE /api/jupyterhub/groups/:groupName/users/:username
Remove user from a group.

---

### LMS Integration

#### POST /api/lms/sync/users
Sync users from LMS (Admin only).

#### POST /api/lms/sync/courses
Sync courses from LMS (Admin only).

#### GET /api/lms/status
Get LMS sync status.

---

### LTI Integration

#### GET /api/lti/config
Get LTI tool configuration for platform registration.

#### POST /api/lti/login
LTI login initiation endpoint.

#### POST /api/lti/launch
LTI launch endpoint for resource link requests.

#### GET /api/lti/jwks
Get tool's public key set for JWT verification.

#### POST /api/lti/deep-linking
Handle LTI deep linking for content selection.

#### GET /api/lti/platforms
Get registered LTI platforms (Admin only).

#### POST /api/lti/platforms
Register new LTI platform (Admin only).

**Request Body:**
```json
{
  "platformId": "string",
  "platformName": "string",
  "clientId": "string",
  "authLoginUrl": "string",
  "authTokenUrl": "string",
  "keySetUrl": "string",
  "settings": {}
}
```

#### POST /api/lti/grades
Send grade to LMS via Assignment and Grade Services.

**Request Body:**
```json
{
  "submissionId": "uuid",
  "gradeId": "uuid"
}
```

---

## Error Handling

### Standard Error Response

```json
{
  "success": false,
  "error": "Error Type",
  "message": "Human-readable error message",
  "details": {},
  "timestamp": "datetime",
  "path": "/api/endpoint"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

### Common Validation Errors

```json
{
  "success": false,
  "error": "Validation Error",
  "message": "Request validation failed",
  "details": {
    "field": "email",
    "message": "Valid email is required"
  }
}
```

---

## Data Models

### User Model
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  lmsUserId?: string;
  status: 'active' | 'inactive' | 'suspended';
  profileData: {
    firstName?: string;
    lastName?: string;
    bio?: string;
    avatar?: string;
    preferences: Record<string, any>;
  };
  roles: Role[];
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}
```

### Course Model
```typescript
interface Course {
  id: string;
  name: string;
  code: string;
  description?: string;
  semester: string;
  instructorId: string;
  status: 'active' | 'archived';
  settings: {
    allowLateSubmissions: boolean;
    autoGrading: boolean;
    maxAttempts: number;
  };
  instructor: User;
  enrollments: CourseEnrollment[];
  projects: Project[];
}
```

### Project Model
```typescript
interface Project {
  id: string;
  title: string;
  description: string;
  courseId: string;
  creatorId: string;
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced';
  status: 'draft' | 'published' | 'archived';
  dueDate?: Date;
  maxAttempts: number;
  instructions?: string;
  requirements: string[];
  resources: ProjectResource[];
  rubrics: Rubric[];
  isTemplate: boolean;
  course: Course;
  submissions: Submission[];
}
```

### Submission Model
```typescript
interface Submission {
  id: string;
  projectId: string;
  userId: string;
  status: 'draft' | 'submitted' | 'graded';
  submittedAt?: Date;
  attempt: number;
  notebooks: NotebookFile[];
  files: SubmissionFile[];
  metadata: Record<string, any>;
  project: Project;
  user: User;
  grade?: Grade;
}
```

### Grade Model
```typescript
interface Grade {
  id: string;
  submissionId: string;
  evaluatorId: string;
  totalScore: number;
  maxScore: number;
  feedback?: string;
  rubricScores: RubricScore[];
  gradedAt: Date;
  submission: Submission;
  evaluator: User;
}
```

---

## Rate Limiting

- **Authentication**: 5 requests per minute
- **File Upload**: 10 requests per minute
- **General API**: 100 requests per minute
- **LTI Endpoints**: 20 requests per minute

---

## Webhooks

### Grade Update Webhook
Triggered when a grade is created or updated.

**Payload:**
```json
{
  "event": "grade.updated",
  "data": {
    "gradeId": "uuid",
    "submissionId": "uuid",
    "projectId": "uuid",
    "userId": "uuid",
    "totalScore": 85,
    "maxScore": 100
  },
  "timestamp": "datetime"
}
```

### Submission Update Webhook
Triggered when a submission is submitted.

**Payload:**
```json
{
  "event": "submission.submitted",
  "data": {
    "submissionId": "uuid",
    "projectId": "uuid",
    "userId": "uuid",
    "attempt": 1
  },
  "timestamp": "datetime"
}
```

---

## SDK Examples

### JavaScript/TypeScript

```typescript
import axios from 'axios';

class BitsDataScienceAPI {
  private baseURL = 'https://api.bits-datascience.edu';
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  private get headers() {
    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    };
  }

  async getProjects(courseId?: string) {
    const params = courseId ? { courseId } : {};
    const response = await axios.get(`${this.baseURL}/api/projects`, {
      headers: this.headers,
      params
    });
    return response.data;
  }

  async submitProject(projectId: string, notebooks: any[], files: any[]) {
    const response = await axios.post(`${this.baseURL}/api/submissions`, {
      projectId,
      notebooks,
      files
    }, {
      headers: this.headers
    });
    return response.data;
  }
}
```

### Python

```python
import requests
from typing import Optional, List, Dict, Any

class BitsDataScienceAPI:
    def __init__(self, token: str, base_url: str = "https://api.bits-datascience.edu"):
        self.base_url = base_url
        self.token = token
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    def get_projects(self, course_id: Optional[str] = None) -> Dict[str, Any]:
        params = {"courseId": course_id} if course_id else {}
        response = requests.get(
            f"{self.base_url}/api/projects",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def submit_project(self, project_id: str, notebooks: List[Dict], files: List[Dict]) -> Dict[str, Any]:
        data = {
            "projectId": project_id,
            "notebooks": notebooks,
            "files": files
        }
        response = requests.post(
            f"{self.base_url}/api/submissions",
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
```

---

## Testing

### Health Check
```bash
curl -X GET https://api.bits-datascience.edu/health
```

### Authentication Test
```bash
curl -X POST https://api.bits-datascience.edu/api/auth/google \
  -H "Content-Type: application/json" \
  -d '{"googleToken": "your-google-token"}'
```

---

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.bits-datascience.edu
- Status Page: https://status.bits-datascience.edu

---

**Last Updated**: August 20, 2025  
**Version**: 2.2.0  
**Maintainer**: BITS Pilani DataScience Team