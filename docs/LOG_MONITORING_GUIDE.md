# Server Log Monitoring Guide

## Quick Reference Commands

### View Latest Logs
```bash
# View last 50 lines of current log file
tail -50 logs/app-$(date +%Y-%m-%d).log

# Follow logs in real-time
tail -f logs/app-$(date +%Y-%m-%d).log

# View specific number of lines
tail -100 logs/app-2025-01-11.log
```

### Filter by Log Level
```bash
# Error logs only
grep "error" logs/app-2025-01-11.log
grep -i "ERROR" logs/app-2025-01-11.log

# Warning logs
grep -i "warn" logs/app-2025-01-11.log

# Info logs
grep -i "info" logs/app-2025-01-11.log

# Multiple levels
grep -E "error|warn|ERROR|WARN" logs/app-2025-01-11.log
```

### Filter by Component/Service
```bash
# Enhanced Project Service
grep -i "enhancedproject" logs/app-2025-01-11.log

# JWT Middleware
grep -i "jwt" logs/app-2025-01-11.log

# Database operations
grep -i "sequelize\|database" logs/app-2025-01-11.log

# Authentication
grep -i "auth\|login\|token" logs/app-2025-01-11.log
```

### Filter by HTTP Status
```bash
# Server errors (5xx)
grep "500\|501\|502\|503\|504" logs/app-2025-01-11.log

# Client errors (4xx)
grep "400\|401\|403\|404\|422" logs/app-2025-01-11.log

# All errors
grep -E "4[0-9][0-9]|5[0-9][0-9]" logs/app-2025-01-11.log
```

### Filter by API Endpoints
```bash
# All HTTP requests
grep -E "GET|POST|PUT|DELETE" logs/app-2025-01-11.log

# Specific endpoint
grep "/api/projects" logs/app-2025-01-11.log

# Failed requests
grep -E "(GET|POST|PUT|DELETE).*[45][0-9][0-9]" logs/app-2025-01-11.log
```

### Time-based Filtering
```bash
# Today's logs
grep "$(date +%Y-%m-%d)" logs/app-2025-01-11.log

# Specific hour (e.g., 14:xx)
grep "14:" logs/app-2025-01-11.log

# Last hour logs
grep "$(date -d '1 hour ago' +%H):" logs/app-2025-01-11.log
```

## Advanced Filtering

### Context Lines
```bash
# Show 5 lines before and after match
grep -A 5 -B 5 "error" logs/app-2025-01-11.log

# Show 10 lines after error
grep -A 10 "SequelizeEagerLoadingError" logs/app-2025-01-11.log
```

### Combined Filters
```bash
# Latest 50 error logs
grep "error" logs/app-2025-01-11.log | tail -50

# Real-time error monitoring with color
tail -f logs/app-2025-01-11.log | grep --color=always -E "error|ERROR|fail|FAIL"

# Errors from specific service
grep -i "enhancedproject" logs/app-2025-01-11.log | grep -i "error"
```

### Count Occurrences
```bash
# Count total errors
grep -c "error" logs/app-2025-01-11.log

# Count by error type
grep -c "SequelizeEagerLoadingError" logs/app-2025-01-11.log

# Count HTTP status codes
grep -c "500" logs/app-2025-01-11.log
```

## File Management

### Find Latest Log File
```bash
# List log files by date (newest first)
ls -lt logs/

# Get most recent log file name
ls -t logs/ | head -1

# View most recent log
tail -50 logs/$(ls -t logs/ | head -1)
```

### Log File Sizes
```bash
# Check log file sizes
ls -lh logs/

# Find large log files (>10MB)
find logs/ -size +10M -ls
```

## Common Debugging Scenarios

### Database Connection Issues
```bash
grep -i -E "database|connection|sequelize.*error" logs/app-2025-01-11.log
```

### Authentication Problems
```bash
grep -i -E "jwt|auth|token.*error|unauthorized" logs/app-2025-01-11.log
```

### API Endpoint Errors
```bash
grep -E "(GET|POST|PUT|DELETE).*[45][0-9][0-9]" logs/app-2025-01-11.log | tail -20
```

### Performance Issues
```bash
# Slow requests (>1000ms)
grep -E "[0-9]{4,}\.[0-9]+ ms" logs/app-2025-01-11.log
```

## Real-time Monitoring

### Live Error Monitoring
```bash
# Monitor errors in real-time with color highlighting
tail -f logs/app-$(date +%Y-%m-%d).log | grep --color=always -E "error|ERROR|fail|FAIL|[45][0-9][0-9]"
```

### Monitor Specific Service
```bash
# Monitor Enhanced Project Service
tail -f logs/app-$(date +%Y-%m-%d).log | grep --color=always -i "enhancedproject"
```

### Monitor API Calls
```bash
# Monitor all API requests
tail -f logs/app-$(date +%Y-%m-%d).log | grep --color=always -E "GET|POST|PUT|DELETE"
```

## Log Analysis Tips

1. **Start with recent logs**: Use `tail -f` for real-time monitoring
2. **Filter progressively**: Start broad, then narrow down
3. **Use context**: `-A` and `-B` flags show surrounding lines
4. **Combine filters**: Chain `grep` commands with pipes
5. **Save results**: Redirect output to files for analysis
   ```bash
   grep "error" logs/app-2025-01-11.log > error_analysis.txt
   ```

## Useful Aliases

Add these to your `.bashrc` or `.zshrc`:

```bash
# Quick log viewing
alias logtoday='tail -f logs/app-$(date +%Y-%m-%d).log'
alias logerrors='tail -f logs/app-$(date +%Y-%m-%d).log | grep --color=always -E "error|ERROR|fail|FAIL"'
alias logapi='tail -f logs/app-$(date +%Y-%m-%d).log | grep --color=always -E "GET|POST|PUT|DELETE"'

# Log analysis
alias logcount='grep -c'
alias logfind='grep -i'
alias logcontext='grep -A 5 -B 5'
```

## Emergency Debugging

When the server is having issues:

```bash
# 1. Check latest errors
tail -100 logs/app-$(date +%Y-%m-%d).log | grep -E "error|ERROR|fail|FAIL"

# 2. Check database connectivity
grep -i "database\|sequelize.*error" logs/app-$(date +%Y-%m-%d).log | tail -10

# 3. Check recent API failures
grep -E "(GET|POST|PUT|DELETE).*[45][0-9][0-9]" logs/app-$(date +%Y-%m-%d).log | tail -20

# 4. Monitor in real-time
tail -f logs/app-$(date +%Y-%m-%d).log | grep --color=always -E "error|ERROR|[45][0-9][0-9]"
```