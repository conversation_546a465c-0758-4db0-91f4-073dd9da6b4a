import { sequelize } from '../src/config/database.config.js';

const resetSubmissionsTable = async () => {
  try {
    console.log("Connected to <PERSON> ✅");

    const sql = `
    -- Drop grades constraint
    ALTER TABLE IF EXISTS public.grades
    DROP CONSTRAINT IF EXISTS grades_submission_id_fkey;

    -- Drop submissions table
    DROP TABLE IF EXISTS public.submissions CASCADE;

    -- Ensure pgcrypto extension for UUID generation
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";

    -- Ensure enum type exists
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_submissions_status') THEN
            CREATE TYPE enum_submissions_status AS ENUM (
                'in_progress',
                'submitted',
                'grading',
                'graded',
                'returned'
            );
        END IF;
    END$$;

    -- Create submissions table
    CREATE TABLE public.submissions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        project_id UUID NOT NULL,
        attempts INTEGER NOT NULL DEFAULT 1,
        notebook_s3_url VARCHAR(255),
        status enum_submissions_status DEFAULT 'in_progress',
        submitted_at TIMESTAMPTZ,
        time_spent INTEGER DEFAULT 0,
        current_progress INTEGER DEFAULT 0,
        execution_time DOUBLE PRECISION,
        execution_output TEXT,
        submission_summary TEXT,
        metadata JSONB,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        deleted_at TIMESTAMPTZ,
        CONSTRAINT submissions_user_id_fkey FOREIGN KEY (user_id)
            REFERENCES public.users(id) ON UPDATE CASCADE,
        CONSTRAINT submissions_project_id_fkey FOREIGN KEY (project_id)
            REFERENCES public.projects(id) ON UPDATE CASCADE
    );

    -- Indexes
    CREATE INDEX submissions_user_id_idx ON public.submissions(user_id);
    CREATE INDEX submissions_project_id_idx ON public.submissions(project_id);
    CREATE INDEX submissions_status_idx ON public.submissions(status);
    CREATE INDEX submissions_submitted_at_idx ON public.submissions(submitted_at);

    -- Re-add grades FK (NOT VALID to skip integrity check on old rows)
    ALTER TABLE public.grades
    ADD CONSTRAINT grades_submission_id_fkey
    FOREIGN KEY (submission_id)
    REFERENCES public.submissions(id)
    ON UPDATE CASCADE
    ON DELETE CASCADE
    NOT VALID;
    `;

    await sequelize.query(sql);
    console.log("✅ Submissions table reset and Grades FK re-added");

  } catch (err) {
    console.error("❌ Error running script:", err);
  } finally {
    await sequelize.close();
    console.log("Connection closed 🔌");
  }
};

resetSubmissionsTable();
