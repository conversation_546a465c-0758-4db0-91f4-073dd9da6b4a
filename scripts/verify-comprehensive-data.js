#!/usr/bin/env node

import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME || 'bits_platform',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || 'password',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: false
  }
);

async function verifyComprehensiveData() {
  try {
    console.log('🔍 Verifying Comprehensive Sample Data\n');
    console.log('='.repeat(80));

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully\n');

    // Get table counts
    const tables = [
      'users',
      'courses',
      'projects',
      'submissions',
      'grades',
      'announcements',
      'activities',
      'checkpoints',
      'checkpoint_goals',
      'rubrics',
      'role_permissions'
    ];

    console.log('📊 TABLE COUNTS:');
    console.log('-'.repeat(40));

    for (const table of tables) {
      try {
        const [results] = await sequelize.query(
          `SELECT COUNT(*) as count FROM ${table}`
        );
        const count = results[0].count;
        console.log(`${table.padEnd(20)} : ${count.toString().padStart(6)}`);
      } catch (error) {
        console.log(`${table.padEnd(20)} : ERROR`);
      }
    }

    console.log('\n' + '='.repeat(80));

    // Detailed verification queries
    console.log('\n📋 DETAILED DATA VERIFICATION:\n');

    // 1. Users by role
    console.log('👥 USERS BY ROLE:');
    console.log('-'.repeat(40));
    const [usersByRole] = await sequelize.query(`
      SELECT r.name as role, COUNT(ur.user_id) as user_count
      FROM roles r
      LEFT JOIN user_roles ur ON r.id = ur.role_id
      GROUP BY r.id, r.name
      ORDER BY user_count DESC
    `);
    usersByRole.forEach(row => {
      console.log(
        `${row.role.padEnd(15)} : ${row.user_count.toString().padStart(3)} users`
      );
    });

    // 2. Projects by course
    console.log('\n📚 PROJECTS BY COURSE:');
    console.log('-'.repeat(40));
    const [projectsByCourse] = await sequelize.query(`
      SELECT c.name as course, COUNT(p.id) as project_count
      FROM courses c
      LEFT JOIN projects p ON c.id = p.course_id
      GROUP BY c.id, c.name
      ORDER BY project_count DESC
    `);
    projectsByCourse.forEach(row => {
      console.log(
        `${row.course.substring(0, 30).padEnd(30)} : ${row.project_count.toString().padStart(2)} projects`
      );
    });

    // 3. Submissions and grades
    console.log('\n📝 SUBMISSIONS & GRADES:');
    console.log('-'.repeat(40));
    const [submissionStats] = await sequelize.query(`
      SELECT
        COUNT(s.id) as total_submissions,
        COUNT(g.id) as total_grades,
        ROUND(AVG(g.total_score), 2) as avg_score
      FROM submissions s
      LEFT JOIN grades g ON s.id = g.submission_id
    `);
    const stats = submissionStats[0];
    console.log(
      `Total Submissions    : ${stats.total_submissions.toString().padStart(6)}`
    );
    console.log(
      `Total Grades         : ${stats.total_grades.toString().padStart(6)}`
    );
    console.log(`Average Score        : ${stats.avg_score || 'N/A'}`);

    // 4. Announcements by type
    console.log('\n📢 ANNOUNCEMENTS BY TYPE:');
    console.log('-'.repeat(40));
    const [announcementsByType] = await sequelize.query(`
      SELECT announcement_type, COUNT(*) as count
      FROM announcements
      GROUP BY announcement_type
      ORDER BY count DESC
    `);
    announcementsByType.forEach(row => {
      console.log(
        `${row.announcement_type.padEnd(20)} : ${row.count.toString().padStart(3)}`
      );
    });

    // 5. Activities by type
    console.log('\n🎯 ACTIVITIES BY TYPE:');
    console.log('-'.repeat(40));
    const [activitiesByType] = await sequelize.query(`
      SELECT activity_type, COUNT(*) as count
      FROM activities
      GROUP BY activity_type
      ORDER BY count DESC
    `);
    activitiesByType.forEach(row => {
      console.log(
        `${row.activity_type.padEnd(20)} : ${row.count.toString().padStart(3)}`
      );
    });

    // 6. Checkpoints and goals
    console.log('\n🎯 CHECKPOINTS & GOALS:');
    console.log('-'.repeat(40));
    const [checkpointStats] = await sequelize.query(`
      SELECT 
        COUNT(DISTINCT c.id) as total_checkpoints,
        COUNT(cg.id) as total_goals,
        ROUND(AVG(cg.points), 2) as avg_points_per_goal
      FROM checkpoints c
      LEFT JOIN checkpoint_goals cg ON c.id = cg.checkpoint_id
    `);
    const cpStats = checkpointStats[0];
    console.log(
      `Total Checkpoints    : ${cpStats.total_checkpoints.toString().padStart(6)}`
    );
    console.log(
      `Total Goals          : ${cpStats.total_goals.toString().padStart(6)}`
    );
    console.log(
      `Avg Points per Goal  : ${cpStats.avg_points_per_goal || 'N/A'}`
    );

    // 7. Rubrics
    console.log('\n📊 RUBRICS:');
    console.log('-'.repeat(40));
    const [rubricStats] = await sequelize.query(`
      SELECT 
        COUNT(*) as total_rubrics,
        COUNT(CASE WHEN is_template = true THEN 1 END) as template_rubrics,
        ROUND(AVG(total_points), 2) as avg_total_points
      FROM rubrics
    `);
    const rubStats = rubricStats[0];
    console.log(
      `Total Rubrics        : ${rubStats.total_rubrics.toString().padStart(6)}`
    );
    console.log(
      `Template Rubrics     : ${rubStats.template_rubrics.toString().padStart(6)}`
    );
    console.log(`Avg Total Points     : ${rubStats.avg_total_points || 'N/A'}`);

    // 8. Role permissions
    console.log('\n🔐 ROLE PERMISSIONS:');
    console.log('-'.repeat(40));
    const [rolePermissions] = await sequelize.query(`
      SELECT r.name as role, COUNT(rp.permission_id) as permission_count
      FROM roles r
      LEFT JOIN role_permissions rp ON r.id = rp.role_id
      GROUP BY r.id, r.name
      ORDER BY permission_count DESC
    `);
    rolePermissions.forEach(row => {
      console.log(
        `${row.role.padEnd(15)} : ${row.permission_count.toString().padStart(3)} permissions`
      );
    });

    console.log('\n' + '='.repeat(80));
    console.log('✅ Comprehensive data verification completed successfully!');
    console.log('🎉 All sample data has been created and verified.');
  } catch (error) {
    console.error('❌ Error during verification:', error.message);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run verification
verifyComprehensiveData();
