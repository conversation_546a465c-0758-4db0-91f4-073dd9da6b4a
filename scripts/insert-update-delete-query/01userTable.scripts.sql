--Insert User Tables


-- Enable once per database (needed for gen_random_uuid):
CREATE EXTENSION IF NOT EXISTS pgcrypto;

INSERT INTO public.users 
  (id, name, email, lms_user_id, google_id, password_hash, profile_picture, last_login, status, preferences, metadata, created_at, updated_at, deleted_at)  
VALUES  (gen_random_uuid(), '<PERSON><PERSON> V', '<EMAIL>', 'LMS-0001', 'google-oauth2|10001', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/arun.png', NOW(), 'active', '{"theme":"dark","lang":"en","notifications":{"email":true,"sms":false}}'::jsonb, '{"source":"seed","role":"admin","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Naresh S', '<EMAIL>', 'LMS-0002',   'google-oauth2|10002', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/naresh.png', NOW(), 'active', '{"theme":"light","lang":"en","notifications":{"email":true,"sms":true}}'::jsonb, '{"source":"seed","dept":"engineering","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Priya K', '<EMAIL>', 'LMS-0003', 'google-oauth2|10003', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/priya.png', NOW(), 'active', '{"theme":"dark","lang":"sv","notifications":{"email":false,"sms":true}}'::jsonb, '{"source":"seed","dept":"product","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Sanjay R', '<EMAIL>', 'LMS-0004', 'google-oauth2|10004', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/sanjay.png', NOW(), 'active', '{"theme":"light","lang":"en"}'::jsonb, '{"source":"seed","dept":"sales","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Divya M', '<EMAIL>', 'LMS-0005', 'google-oauth2|10005', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/divya.png', NOW(), 'active', '{"theme":"dark","lang":"en"}'::jsonb, '{"source":"seed","dept":"support","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Karthik P', '<EMAIL>', 'LMS-0006', 'google-oauth2|10006', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/karthik.png', NOW(), 'active', '{"theme":"light","lang":"en","notifications":{"email":true}}'::jsonb, '{"source":"seed","dept":"engineering","level":"L2","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Meera L', '<EMAIL>', 'LMS-0007', 'google-oauth2|10007', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/meera.png', NOW(), 'active', '{"theme":"dark","lang":"en"}'::jsonb, '{"source":"seed","dept":"design","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Vikram D', '<EMAIL>', 'LMS-0008', 'google-oauth2|10008', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/vikram.png', NOW(), 'active', '{"theme":"light","lang":"en"}'::jsonb, '{"source":"seed","dept":"ops","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Anitha G', '<EMAIL>', 'LMS-0009', 'google-oauth2|10009', '$$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/anitha.png', NOW(), 'active', '{"theme":"dark","lang":"sv"}'::jsonb, '{"source":"seed","dept":"marketing","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL),
        (gen_random_uuid(), 'Rahul T', '<EMAIL>', 'LMS-0010', 'google-oauth2|10010', '$2b$10$IsuUgqeMBpPp7oF/ahPDguKStPzhI/PsrA2UlAgnF8AYGrNFboLBK', 'https://cdn.bits.com/avatars/rahul.png', NOW(), 'active', '{"theme":"light","lang":"en","notifications":{"email":false}}'::jsonb, '{"source":"seed","dept":"finance","import_batch":"2025-09-05"}'::jsonb, NOW(), NOW(), NULL);

