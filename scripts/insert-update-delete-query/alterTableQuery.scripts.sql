--Porject table alteration script--
ALTER TABLE projects
ADD COLUMN sandbox_time_duration VARCHAR(5) DEFAULT null,
ADD COLUMN late_submission_days_allowed INTEGER DEFAULT 0;

COMMENT ON COLUMN projects.sandbox_time_duration IS 'Allowed sandbox duration in HH:MM format';
COMMENT ON COLUMN projects.late_submission_days_allowed IS 'Number of days late submission is allowed';


--- Alter dataset_s3_url column from TEXT to JSONB
-- Step 1: Drop default and set all existing NULL values to '[]'
-- This ensures that the column can be safely converted to jsonb type
ALTER TABLE projects ALTER COLUMN dataset_s3_url DROP DEFAULT;

-- Force reset values to empty JSON array
UPDATE projects SET dataset_s3_url = '[]';

-- Now change type
ALTER TABLE projects ALTER COLUMN dataset_s3_url TYPE jsonb USING '[]'::jsonb, ALTER COLUMN dataset_s3_url SET DEFAULT '[]'::jsonb;
COMMENT ON COLUMN projects.dataset_s3_url IS 'Array of S3 URLs for project dataset files';

-- Make course_id column NOT NULL
ALTER TABLE projects ALTER COLUMN course_id DROP NOT NULL;

-- Make title column in rubrics table nullable
ALTER TABLE rubrics ALTER COLUMN title DROP NOT NULL;

--- Add start_date column to checkpoints table
ALTER TABLE checkpoints
ADD COLUMN start_date TIMESTAMP NULL;

COMMENT ON COLUMN checkpoints.start_date IS 'When this checkpoint should be started';
