-- Needs pgcrypto for gen_random_uuid()
-- CREATE EXTENSION IF NOT EXISTS pgcrypto;

INSERT INTO public.user_roles
  (id, user_id, role_id, assigned_by, assigned_at, is_primary, context, created_at, updated_at, deleted_at)
VALUES
  -- admin
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='admin'),
   NULL, NOW(), TRUE, '{"source":"seed"}'::jsonb, NOW(), NOW(), NULL),

  -- instructors
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='instructor'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, <PERSON><PERSON>(), NOW(), NULL),
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='instructor'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='instructor'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),

  -- TAs
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='ta'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='ta'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='ta'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),

  -- students
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='student'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='student'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL),
  (gen_random_uuid(),
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   (SELECT id FROM public.roles WHERE name='student'),
   (SELECT id FROM public.users WHERE email='<EMAIL>'), NOW(), TRUE, '{}'::jsonb, NOW(), NOW(), NULL);
