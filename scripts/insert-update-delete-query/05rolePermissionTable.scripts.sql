-- CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 1) Give ALL permissions to admin and instructor
INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  r.id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by, -- seed admin
  NOW(), NOW(), NOW(), NULL
FROM public.roles r
CROSS JOIN public.permissions p
WHERE r.name IN ('admin','instructor');

-- 2) Give limited permissions to student
INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  (SELECT id FROM public.roles WHERE name='student') AS role_id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by,
  NOW(), NOW(), NOW(), NULL
FROM public.permissions p
WHERE p.key IN ('view_projects','view_courses','project:read');

	INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  (SELECT id FROM public.roles WHERE name='admin') AS role_id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by,
  NOW(), NOW(), NOW(), NULL
FROM public.permissions p
WHERE p.key IN ('publish_projects');


INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  (SELECT id FROM public.roles WHERE name='instructor') AS role_id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by,
  NOW(), NOW(), NOW(), NULL
FROM public.permissions p
WHERE p.key IN ('publish_projects');




	INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  (SELECT id FROM public.roles WHERE name='admin') AS role_id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by,
  NOW(), NOW(), NOW(), NULL
FROM public.permissions p
WHERE p.key IN ('submit_assignments', 'view_submissions');


INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  (SELECT id FROM public.roles WHERE name='instructor') AS role_id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by,
  NOW(), NOW(), NOW(), NULL
FROM public.permissions p
WHERE p.key IN ('submit_assignments', 'view_submissions');


INSERT INTO public.role_permissions
  (id, role_id, permission_id, granted_by, granted_at, created_at, updated_at, deleted_at)
SELECT
  gen_random_uuid(),
  (SELECT id FROM public.roles WHERE name='student') AS role_id,
  p.id,
  (SELECT id FROM public.users WHERE email='<EMAIL>') AS granted_by,
  NOW(), NOW(), NOW(), NULL
FROM public.permissions p
WHERE p.key IN ('submit_assignments', 'view_submissions');