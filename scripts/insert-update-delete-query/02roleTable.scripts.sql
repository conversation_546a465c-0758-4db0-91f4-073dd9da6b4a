-- If you use gen_random_uuid(), make sure pgcrypto is enabled:
-- CREATE EXTENSION IF NOT EXISTS pgcrypto;

INSERT INTO public.roles
  (id, name, lms_role_reference, description, is_system_role, priority, metadata, created_at, updated_at, deleted_at)
VALUES
  (gen_random_uuid(), 'admin',      'ROLE_ADMIN',      'Full administrative access',                  TRUE, 100, '{"scopes":["*"]}'::jsonb,                                      NOW(), NOW(), NULL),
  (gen_random_uuid(), 'instructor', 'ROLE_INSTRUCTOR', 'Create/manage courses and grade submissions', TRUE,  80, '{"scopes":["courses:write","grades:write"]}'::jsonb,          NOW(), NOW(), NULL),
  (gen_random_uuid(), 'ta',         'ROLE_TA',         'Assist instructor; manage content and grades',FALSE, 70, '{"scopes":["courses:write","grades:write","moderate"]}'::jsonb, NOW(), NOW(), NULL),
  (gen_random_uuid(), 'student',    'ROLE_STUDENT',    'Learner with read/submit permissions',         FALSE, 10, '{"scopes":["courses:read","assignments:submit"]}'::jsonb,   NOW(), NOW(), NULL);
