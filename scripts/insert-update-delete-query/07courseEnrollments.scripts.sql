-- Data Science 101
-- Instructors (2 random, not already in this course)
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'instructor', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Data Science 101'
WHERE r.name = 'instructor'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- TAs (2 random)
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'ta', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Data Science 101'
WHERE r.name = 'ta'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- Students (2 random)
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'student', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Data Science 101'
WHERE r.name = 'student'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- Machine Learning Basics
-- Instructors
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'instructor', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Machine Learning Basics'
WHERE r.name = 'instructor'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- TAs
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'ta', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Machine Learning Basics'
WHERE r.name = 'ta'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- Students
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'student', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Machine Learning Basics'
WHERE r.name = 'student'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- Deep Learning Advanced
-- Instructors
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'instructor', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Deep Learning Advanced'
WHERE r.name = 'instructor'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- TAs
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'ta', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Deep Learning Advanced'
WHERE r.name = 'ta'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;

-- Students
INSERT INTO public.course_enrollments
  (id, user_id, course_id, role_in_course, enrollment_status, enrolled_at, created_at, updated_at)
SELECT gen_random_uuid(), ur.user_id, c.id, 'student', 'active', NOW(), NOW(), NOW()
FROM public.user_roles ur
JOIN public.roles r ON r.id = ur.role_id
JOIN public.courses c ON c.name = 'Deep Learning Advanced'
WHERE r.name = 'student'
  AND ur.user_id NOT IN (SELECT user_id FROM public.course_enrollments WHERE course_id = c.id)
ORDER BY random()
LIMIT 2;
