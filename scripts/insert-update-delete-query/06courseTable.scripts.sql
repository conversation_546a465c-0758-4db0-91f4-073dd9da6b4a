-- If not already:
-- CREATE EXTENSION IF NOT EXISTS pgcrypto;

INSERT INTO public.courses
  (id, lms_course_id, name, code, description, term, academic_year, instructor_id,
   status, start_date, end_date, settings, metadata, created_at, updated_at, deleted_at)
VALUES
  -- Data Science 101 -> Instructor: Naresh
  (gen_random_uuid(), 'LMS-DS-101', 'Data Science 101', 'DS101',
   'Intro to data science: Python, stats, EDA, and visualization.',
   'Fall', '2025-2026',
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   'active', TIMESTAMP '2025-09-15 09:00:00', TIMESTAMP '2025-12-20 17:00:00',
   '{"credits":3,"modality":"hybrid","language":"en"}'::jsonb,
   '{"category":"data","seed":true}'::jsonb, NOW(), NOW(), NULL),

  -- Machine Learning Basics -> Instructor: Priya
  (gen_random_uuid(), 'LMS-ML-201', 'Machine Learning Basics', 'ML201',
   'Supervised/unsupervised learning, model eval, scikit-learn.',
   'Spring', '2025-2026',
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   'active', TIMESTAMP '2026-01-10 09:00:00', TIMESTAMP '2026-04-30 17:00:00',
   '{"credits":4,"modality":"in-person","language":"en"}'::jsonb,
   '{"category":"ml","prereq":"DS101","seed":true}'::jsonb, NOW(), NOW(), NULL),

  -- Deep Learning Advanced -> Instructor: Sanjay
  (gen_random_uuid(), 'LMS-DL-301', 'Deep Learning Advanced', 'DL301',
   'Neural networks, CNNs/RNNs/Transformers, training at scale.',
   'Summer', '2025-2026',
   (SELECT id FROM public.users WHERE email='<EMAIL>'),
   'active', TIMESTAMP '2026-05-15 09:00:00', TIMESTAMP '2026-08-15 17:00:00',
   '{"credits":4,"modality":"online","language":"en"}'::jsonb,
   '{"category":"deep-learning","prereq":"ML201","seed":true}'::jsonb, NOW(), NOW(), NULL);
