
--- *********************Create or replace the projects table with all constraints and indexes***********************
BEGIN;
-- 0) (Optional) if you plan to use gen_random_uuid()
-- CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 1) Ensure required ENUM types exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_projects_status') THEN
    CREATE TYPE public.enum_projects_status AS ENUM ('draft','published','archived');
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_projects_difficulty_level') THEN
    CREATE TYPE public.enum_projects_difficulty_level AS ENUM ('beginner','intermediate','advanced');
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'project_type') THEN
    CREATE TYPE public.project_type AS ENUM ('group','individual');
  END IF;
END$$;

-- 2) Ensure the sequence for project_code exists
CREATE SEQUENCE IF NOT EXISTS public.projects_project_code_seq;

-- 3) Drop and recreate the table
DROP TABLE IF EXISTS public.projects CASCADE;

CREATE TABLE public.projects
(
    id uuid NOT NULL,
    title varchar(255) NOT NULL,
    description text,
    instructions text,
    course_id uuid NOT NULL,
    created_by uuid NOT NULL,
    status enum_projects_status DEFAULT 'draft'::enum_projects_status,
    difficulty_level enum_projects_difficulty_level DEFAULT 'beginner'::enum_projects_difficulty_level,
    estimated_hours integer,
    notebook_template_s3_url varchar(255),
    dataset_s3_url varchar(255),
    additional_files_s3_urls jsonb DEFAULT '[]'::jsonb,
    due_date timestamptz,
    late_submission_allowed boolean DEFAULT true,
    late_penalty_percent numeric(5,2) DEFAULT 0,
    max_attempts integer DEFAULT 1,
    auto_grading_enabled boolean DEFAULT false,
    tags jsonb DEFAULT '[]'::jsonb,
    settings jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz NOT NULL,
    updated_at timestamptz NOT NULL,
    deleted_at timestamptz,
    creator_id uuid,
    "isScreen" integer NOT NULL DEFAULT 1,
    project_code char(6) NOT NULL DEFAULT lpad((nextval('projects_project_code_seq'))::text, 6, '0'),
    instructor_id uuid[] DEFAULT '{}'::uuid[],
    total_points integer,
    project_overview text,
    type project_type NOT NULL DEFAULT 'group'::project_type,
    category_id uuid,
    teaching_ass_id uuid[] DEFAULT '{}'::uuid[],
    learning_objectives text,
    prerequisites text,
    start_date timestamptz,

    CONSTRAINT projects_pkey PRIMARY KEY (id),
    CONSTRAINT uq_projects_project_code UNIQUE (project_code),

    CONSTRAINT projects_course_id_fkey FOREIGN KEY (course_id)
        REFERENCES public.courses (id) ON UPDATE CASCADE ON DELETE NO ACTION,

    CONSTRAINT projects_created_by_fkey FOREIGN KEY (created_by)
        REFERENCES public.users (id) ON UPDATE NO ACTION ON DELETE NO ACTION,

    CONSTRAINT projects_creator_id_fkey FOREIGN KEY (creator_id)
        REFERENCES public.users (id) ON UPDATE CASCADE ON DELETE SET NULL,

    CONSTRAINT "projects_isScreen_check" CHECK ("isScreen" = ANY (ARRAY[0,1,2,3,4])),
    CONSTRAINT project_id_format_chk CHECK (project_code ~ '^[0-9]{6}$'),
    CONSTRAINT chk_projects_project_code_format CHECK (project_code ~ '^[0-9]{6}$')
);

-- Make the sequence owned by the column (so it’s dropped with the table)
ALTER SEQUENCE public.projects_project_code_seq OWNED BY public.projects.project_code;

-- 4) Indexes
CREATE INDEX IF NOT EXISTS idx_projects_instructor_id_gin
    ON public.projects USING gin (instructor_id);

CREATE INDEX IF NOT EXISTS idx_projects_start_date
    ON public.projects USING btree (start_date ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS idx_projects_teaching_ass_id_gin
    ON public.projects USING gin (teaching_ass_id);

CREATE INDEX IF NOT EXISTS projects_course_id
    ON public.projects USING btree (course_id ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS projects_created_by
    ON public.projects USING btree (created_by ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS projects_difficulty_level
    ON public.projects USING btree (difficulty_level ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS projects_due_date
    ON public.projects USING btree (due_date ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS projects_status
    ON public.projects USING btree (status ASC NULLS LAST);

CREATE UNIQUE INDEX IF NOT EXISTS ux_projects_project_id
    ON public.projects USING btree (project_code ASC NULLS LAST);

COMMIT;

--*****************************************************************************************************************************

/* Quick checks (optional)

Confirm ENUMs exist:

\dT+ public.enum_projects_status
\dT+ public.enum_projects_difficulty_level
\dT+ public.project_type


See values:

SELECT unnest(enum_range(NULL::public.enum_projects_status));
SELECT unnest(enum_range(NULL::public.enum_projects_difficulty_level));
SELECT unnest(enum_range(NULL::public.project_type));


Confirm table:

\dt public.projects
\d+ public.projects */