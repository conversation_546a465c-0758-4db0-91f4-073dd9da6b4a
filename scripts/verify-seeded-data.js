#!/usr/bin/env node

/**
 * Database Verification Script for Seeded Projects Data
 * 
 * This script verifies that the sample projects and related data
 * were successfully inserted into the database with proper relationships.
 */

import { sequelize } from '../src/config/database.config.js';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkDatabaseConnection() {
  try {
    await sequelize.authenticate();
    colorLog('green', '✅ Database connection established successfully');
    return true;
  } catch (error) {
    colorLog('red', '❌ Unable to connect to the database:');
    console.error(error.message);
    return false;
  }
}

async function verifySeededData() {
  try {
    colorLog('cyan', '🔍 Verifying seeded data...');
    
    // Check users
    const [userResults] = await sequelize.query(
      "SELECT COUNT(*) as count FROM users WHERE lms_user_id LIKE '%_00%'"
    );
    const userCount = userResults[0].count;
    colorLog('blue', `📊 Sample users created: ${userCount}`);
    
    // Check courses
    const [courseResults] = await sequelize.query(
      "SELECT COUNT(*) as count FROM courses WHERE lms_course_id LIKE 'CS%_2024_FALL'"
    );
    const courseCount = courseResults[0].count;
    colorLog('blue', `📚 Sample courses created: ${courseCount}`);
    
    // Check projects
    const [projectResults] = await sequelize.query(
      "SELECT COUNT(*) as count FROM projects WHERE title LIKE 'Sample Project%'"
    );
    const projectCount = projectResults[0].count;
    colorLog('blue', `🚀 Sample projects created: ${projectCount}`);
    
    // Get detailed project information
    const [projects] = await sequelize.query(`
      SELECT 
        p.title, 
        p.status, 
        p.difficulty_level, 
        p.estimated_hours,
        c.name as course_name,
        c.code as course_code,
        u.name as creator_name
      FROM projects p 
      JOIN courses c ON p.course_id = c.id 
      JOIN users u ON p.created_by = u.id 
      WHERE p.title LIKE 'Sample Project%' 
      ORDER BY p.title
    `);
    
    colorLog('magenta', '\n📋 Project Details:');
    console.log('=' .repeat(120));
    console.log('| Title                                    | Course    | Creator           | Status    | Difficulty | Hours |');
    console.log('=' .repeat(120));
    
    projects.forEach(project => {
      const title = project.title.length > 40 ? project.title.substring(0, 37) + '...' : project.title;
      const creator = project.creator_name.length > 17 ? project.creator_name.substring(0, 14) + '...' : project.creator_name;
      console.log(
        `| ${title.padEnd(40)} | ${project.course_code.padEnd(9)} | ${creator.padEnd(17)} | ${project.status.padEnd(9)} | ${project.difficulty_level.padEnd(10)} | ${project.estimated_hours.toString().padEnd(5)} |`
      );
    });
    console.log('=' .repeat(120));
    
    // Check foreign key relationships
    colorLog('cyan', '\n🔗 Verifying relationships...');
    
    const [projectsWithCourses] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM projects p 
      JOIN courses c ON p.course_id = c.id 
      WHERE p.title LIKE 'Sample Project%'
    `);
    
    const [projectsWithCreators] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM projects p 
      JOIN users u ON p.created_by = u.id 
      WHERE p.title LIKE 'Sample Project%'
    `);
    
    const projectsWithCoursesCount = projectsWithCourses[0].count;
    const projectsWithCreatorsCount = projectsWithCreators[0].count;
    
    colorLog('green', `✅ Projects with valid course relationships: ${projectsWithCoursesCount}/${projectCount}`);
    colorLog('green', `✅ Projects with valid creator relationships: ${projectsWithCreatorsCount}/${projectCount}`);
    
    // Verify data integrity
    if (projectsWithCoursesCount == projectCount && projectsWithCreatorsCount == projectCount) {
      colorLog('green', '✅ All foreign key constraints are satisfied');
      return true;
    } else {
      colorLog('red', '❌ Some foreign key relationships are missing');
      return false;
    }
    
  } catch (error) {
    colorLog('red', '❌ Error verifying seeded data:');
    console.error(error.message);
    return false;
  }
}

async function showSampleQueries() {
  try {
    colorLog('cyan', '\n📊 Sample Queries and Results:');
    
    // Query 1: Projects by difficulty level
    colorLog('yellow', '\n1. Projects grouped by difficulty level:');
    const [projectsByDifficulty] = await sequelize.query(`
      SELECT 
        difficulty_level,
        COUNT(*) as count,
        AVG(estimated_hours) as avg_hours
      FROM projects 
      WHERE title LIKE 'Sample Project%'
      GROUP BY difficulty_level
      ORDER BY difficulty_level
    `);
    
    console.table(projectsByDifficulty);
    
    // Query 2: Projects by course
    colorLog('yellow', '\n2. Projects by course:');
    const [projectsByCourse] = await sequelize.query(`
      SELECT 
        c.code as course_code,
        c.name as course_name,
        COUNT(p.id) as project_count
      FROM courses c
      LEFT JOIN projects p ON c.id = p.course_id AND p.title LIKE 'Sample Project%'
      WHERE c.lms_course_id LIKE 'CS%_2024_FALL'
      GROUP BY c.id, c.code, c.name
      ORDER BY c.code
    `);
    
    console.table(projectsByCourse);
    
    // Query 3: Instructor workload
    colorLog('yellow', '\n3. Instructor workload (projects created):');
    const [instructorWorkload] = await sequelize.query(`
      SELECT 
        u.name as instructor_name,
        u.email,
        COUNT(p.id) as project_count,
        AVG(p.estimated_hours) as avg_project_hours
      FROM users u
      LEFT JOIN projects p ON u.id = p.created_by AND p.title LIKE 'Sample Project%'
      WHERE u.lms_user_id LIKE 'instructor_%'
      GROUP BY u.id, u.name, u.email
      ORDER BY project_count DESC
    `);
    
    console.table(instructorWorkload);
    
    // Query 4: Project status distribution
    colorLog('yellow', '\n4. Project status distribution:');
    const [statusDistribution] = await sequelize.query(`
      SELECT 
        status,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
      FROM projects 
      WHERE title LIKE 'Sample Project%'
      GROUP BY status
      ORDER BY count DESC
    `);
    
    console.table(statusDistribution);
    
    return true;
  } catch (error) {
    colorLog('red', '❌ Error running sample queries:');
    console.error(error.message);
    return false;
  }
}

async function main() {
  try {
    colorLog('bright', '🔍 BITS DataScience Platform - Data Verification Script');
    colorLog('bright', '=' .repeat(60));
    
    // Check database connection
    const connected = await checkDatabaseConnection();
    if (!connected) {
      process.exit(1);
    }
    
    // Verify data
    const verified = await verifySeededData();
    if (!verified) {
      colorLog('yellow', '⚠️  Data verification failed');
      process.exit(1);
    }
    
    // Show sample queries
    await showSampleQueries();
    
    colorLog('green', '\n🎉 Data verification completed successfully!');
    colorLog('cyan', '\nThe sample projects and related data have been successfully seeded.');
    colorLog('cyan', 'You can now use these projects for testing and development.');
    
  } catch (error) {
    colorLog('red', '❌ Fatal error in verification script:');
    console.error(error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
