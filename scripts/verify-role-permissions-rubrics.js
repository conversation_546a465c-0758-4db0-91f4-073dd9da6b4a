#!/usr/bin/env node

/**
 * Verification Script for Role Permissions and Rubrics Data
 * 
 * This script verifies that the role permissions and rubrics data
 * were successfully inserted into the database with proper relationships.
 */

import { sequelize } from '../src/config/database.config.js';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkDatabaseConnection() {
  try {
    await sequelize.authenticate();
    colorLog('green', '✅ Database connection established successfully');
    return true;
  } catch (error) {
    colorLog('red', '❌ Unable to connect to the database:');
    console.error(error.message);
    return false;
  }
}

async function verifyRolePermissions() {
  try {
    colorLog('cyan', '🔍 Verifying role permissions...');
    
    // Get role permission counts
    const [rolePermissionCounts] = await sequelize.query(`
      SELECT 
        r.name as role,
        COUNT(rp.permission_id) as permission_count
      FROM roles r 
      LEFT JOIN role_permissions rp ON r.id = rp.role_id 
      GROUP BY r.id, r.name 
      ORDER BY permission_count DESC
    `);
    
    colorLog('blue', '📊 Role Permission Summary:');
    console.table(rolePermissionCounts);
    
    // Verify specific role permissions
    const expectedPermissions = {
      'super_admin': 23, // All permissions
      'admin': 19,       // Most permissions except system admin
      'instructor': 12,  // Teaching permissions
      'ta': 6,          // Limited teaching permissions
      'student': 5      // Basic learning permissions
    };
    
    let allCorrect = true;
    for (const [role, expectedCount] of Object.entries(expectedPermissions)) {
      const roleData = rolePermissionCounts.find(r => r.role === role);
      const actualCount = roleData ? parseInt(roleData.permission_count) : 0;
      
      if (actualCount === expectedCount) {
        colorLog('green', `✅ ${role}: ${actualCount} permissions (correct)`);
      } else {
        colorLog('red', `❌ ${role}: ${actualCount} permissions (expected ${expectedCount})`);
        allCorrect = false;
      }
    }
    
    return allCorrect;
  } catch (error) {
    colorLog('red', '❌ Error verifying role permissions:');
    console.error(error.message);
    return false;
  }
}

async function verifyRubrics() {
  try {
    colorLog('cyan', '🔍 Verifying rubrics...');
    
    // Get rubrics summary
    const [rubricsSummary] = await sequelize.query(`
      SELECT 
        COUNT(*) as total_rubrics,
        COUNT(CASE WHEN is_template = true THEN 1 END) as template_count,
        COUNT(CASE WHEN is_template = false THEN 1 END) as project_rubrics
      FROM rubrics 
      WHERE title LIKE 'Sample Rubric%'
    `);
    
    const summary = rubricsSummary[0];
    colorLog('blue', `📊 Rubrics Summary:`);
    colorLog('blue', `   Total rubrics: ${summary.total_rubrics}`);
    colorLog('blue', `   Template rubrics: ${summary.template_count}`);
    colorLog('blue', `   Project rubrics: ${summary.project_rubrics}`);
    
    // Get detailed rubrics information
    const [rubrics] = await sequelize.query(`
      SELECT 
        r.title,
        r.total_points,
        r.is_template,
        r.template_name,
        jsonb_array_length(r.criteria) as criteria_count,
        p.title as project_title,
        u.name as creator_name
      FROM rubrics r
      JOIN projects p ON r.project_id = p.id
      JOIN users u ON r.created_by = u.id
      WHERE r.title LIKE 'Sample Rubric%'
      ORDER BY r.title
    `);
    
    colorLog('magenta', '\n📋 Rubrics Details:');
    console.log('=' .repeat(120));
    console.log('| Title                                    | Points | Template | Criteria | Project                    | Creator           |');
    console.log('=' .repeat(120));
    
    rubrics.forEach(rubric => {
      const title = rubric.title.length > 40 ? rubric.title.substring(0, 37) + '...' : rubric.title;
      const project = rubric.project_title.length > 26 ? rubric.project_title.substring(0, 23) + '...' : rubric.project_title;
      const creator = rubric.creator_name.length > 17 ? rubric.creator_name.substring(0, 14) + '...' : rubric.creator_name;
      const isTemplate = rubric.is_template ? 'Yes' : 'No';
      
      console.log(
        `| ${title.padEnd(40)} | ${rubric.total_points.toString().padEnd(6)} | ${isTemplate.padEnd(8)} | ${rubric.criteria_count.toString().padEnd(8)} | ${project.padEnd(26)} | ${creator.padEnd(17)} |`
      );
    });
    console.log('=' .repeat(120));
    
    // Verify rubric criteria structure
    const [criteriaCheck] = await sequelize.query(`
      SELECT 
        title,
        jsonb_extract_path_text(criteria, '0', 'name') as first_criterion,
        jsonb_extract_path_text(criteria, '0', 'points') as first_criterion_points
      FROM rubrics 
      WHERE title LIKE 'Sample Rubric%'
      ORDER BY title
    `);
    
    colorLog('yellow', '\n🔍 Criteria Structure Verification:');
    criteriaCheck.forEach(rubric => {
      if (rubric.first_criterion && rubric.first_criterion_points) {
        colorLog('green', `✅ ${rubric.title}: First criterion "${rubric.first_criterion}" (${rubric.first_criterion_points} points)`);
      } else {
        colorLog('red', `❌ ${rubric.title}: Missing or invalid criteria structure`);
      }
    });
    
    return rubrics.length > 0;
  } catch (error) {
    colorLog('red', '❌ Error verifying rubrics:');
    console.error(error.message);
    return false;
  }
}

async function showDetailedPermissions() {
  try {
    colorLog('cyan', '\n📊 Detailed Permission Analysis:');
    
    // Show permissions by category for each role
    const roles = ['student', 'ta', 'instructor', 'admin', 'super_admin'];
    
    for (const roleName of roles) {
      colorLog('yellow', `\n${roleName.toUpperCase()} Permissions:`);
      
      const [permissions] = await sequelize.query(`
        SELECT 
          p.category,
          p.key,
          p.name
        FROM roles r 
        JOIN role_permissions rp ON r.id = rp.role_id 
        JOIN permissions p ON rp.permission_id = p.id 
        WHERE r.name = :roleName
        ORDER BY p.category, p.key
      `, {
        replacements: { roleName }
      });
      
      const groupedPermissions = permissions.reduce((acc, perm) => {
        if (!acc[perm.category]) acc[perm.category] = [];
        acc[perm.category].push(`${perm.key} (${perm.name})`);
        return acc;
      }, {});
      
      Object.entries(groupedPermissions).forEach(([category, perms]) => {
        colorLog('blue', `  ${category}:`);
        perms.forEach(perm => {
          console.log(`    - ${perm}`);
        });
      });
    }
    
    return true;
  } catch (error) {
    colorLog('red', '❌ Error showing detailed permissions:');
    console.error(error.message);
    return false;
  }
}

async function main() {
  try {
    colorLog('bright', '🔍 BITS DataScience Platform - Role Permissions & Rubrics Verification');
    colorLog('bright', '=' .repeat(80));
    
    // Check database connection
    const connected = await checkDatabaseConnection();
    if (!connected) {
      process.exit(1);
    }
    
    // Verify role permissions
    const rolePermissionsValid = await verifyRolePermissions();
    if (!rolePermissionsValid) {
      colorLog('yellow', '⚠️  Role permissions verification failed');
    }
    
    // Verify rubrics
    const rubricsValid = await verifyRubrics();
    if (!rubricsValid) {
      colorLog('yellow', '⚠️  Rubrics verification failed');
    }
    
    // Show detailed permissions
    await showDetailedPermissions();
    
    if (rolePermissionsValid && rubricsValid) {
      colorLog('green', '\n🎉 All verifications completed successfully!');
      colorLog('cyan', '\nThe role permissions and rubrics have been successfully seeded.');
      colorLog('cyan', 'You can now use these for testing role-based access control and grading functionality.');
    } else {
      colorLog('red', '\n❌ Some verifications failed. Please check the output above.');
      process.exit(1);
    }
    
  } catch (error) {
    colorLog('red', '❌ Fatal error in verification script:');
    console.error(error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
