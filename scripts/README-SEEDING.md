# Database Seeding Scripts

This directory contains scripts for seeding the BITS DataScience Platform database with sample data
for development and testing purposes.

## Overview

The seeding system provides:

- **Sample Users**: Instructors, Teaching Assistants, and Students
- **Sample Courses**: Multiple courses with different configurations
- **Sample Projects**: 5 comprehensive projects covering various data science topics
- **Proper Relationships**: All foreign key constraints are maintained
- **Idempotent Operations**: Safe to run multiple times without creating duplicates

## Files

### Core Seeding Scripts

- **`src/seeders/004-sample-projects.js`** - Main seeding script for projects and related data
- **`src/seeders/005-role-permissions-and-rubrics.js`** - Role permissions mapping and sample
  rubrics
- **`scripts/seed-projects.js`** - Wrapper script with enhanced output and error handling
- **`scripts/verify-seeded-data.js`** - Verification script to check seeded data integrity
- **`scripts/verify-role-permissions-rubrics.js`** - Verification script for role permissions and
  rubrics

### Configuration

- **`src/config/database.js`** - Database configuration for Sequelize CLI
- **`.sequelizerc`** - Sequelize CLI configuration file

## Sample Data Created

### Users (5 total)

- **<PERSON>. <PERSON>** (Instructor) - Computer Science Department
- **Dr. Michael Chen** (Instructor) - Data Science Department
- **Alice Smith** (Teaching Assistant) - Computer Science Department
- **Bob Wilson** (Teaching Assistant) - Data Science Department
- **Emma Davis** (Student) - 3rd year Computer Science major

### Courses (3 total)

- **CS501** - Introduction to Data Science (Fall 2024)
- **CS502** - Machine Learning Fundamentals (Fall 2024)
- **CS503** - Advanced Analytics (Fall 2024)

### Projects (5 total)

1. **Sample Project 1: Data Exploration with Pandas**
   - Course: CS501 (Introduction to Data Science)
   - Difficulty: Beginner
   - Duration: 8 hours
   - Status: Published

2. **Sample Project 2: Machine Learning Classification**
   - Course: CS502 (Machine Learning Fundamentals)
   - Difficulty: Intermediate
   - Duration: 12 hours
   - Status: Published

3. **Sample Project 3: Time Series Analysis**
   - Course: CS503 (Advanced Analytics)
   - Difficulty: Advanced
   - Duration: 15 hours
   - Status: Published

4. **Sample Project 4: Deep Learning with Neural Networks**
   - Course: CS502 (Machine Learning Fundamentals)
   - Difficulty: Advanced
   - Duration: 20 hours
   - Status: Draft

5. **Sample Project 5: Data Visualization Dashboard**
   - Course: CS503 (Advanced Analytics)
   - Difficulty: Intermediate
   - Duration: 16 hours
   - Status: Published

### Role Permissions (65 total mappings)

**Super Admin (23 permissions):**

- All system permissions including user management, system administration, and role/permission
  management

**Admin (19 permissions):**

- Most permissions except system administration and role/permission management
- Can manage users, courses, projects, and view analytics

**Instructor (12 permissions):**

- Teaching-focused permissions: course management, project creation/editing, grading, rubric
  management
- Can view analytics and manage enrollments

**Teaching Assistant (6 permissions):**

- Limited teaching permissions: view courses/projects, grade submissions, view grades and users

**Student (5 permissions):**

- Basic learning permissions: view courses/projects, submit assignments, view own submissions and
  grades

### Sample Rubrics (3 total)

1. **Sample Rubric 1: Data Analysis Evaluation**
   - Project: Data Exploration with Pandas
   - 5 criteria: Data Loading, Data Cleaning, Statistical Analysis, Visualization, Code Quality
   - Total: 100 points

2. **Sample Rubric 2: Machine Learning Project Evaluation**
   - Project: Machine Learning Classification
   - 4 criteria: Data Preprocessing, Model Implementation, Model Evaluation, Model Comparison
   - Total: 100 points

3. **Sample Rubric Template: General Data Science Project**
   - Reusable template for any data science project
   - 5 criteria: Problem Understanding, Data Handling, Analysis & Methods, Results & Interpretation,
     Technical Quality
   - Total: 100 points
   - Marked as template for reuse

## Usage

### Running the Seeding Scripts

#### Option 1: Using Sequelize CLI (Recommended)

```bash
# Run specific seeders
npx sequelize-cli db:seed --seed 004-sample-projects.js
npx sequelize-cli db:seed --seed 005-role-permissions-and-rubrics.js

# Run all seeders
npm run db:seed
```

#### Option 2: Using the Enhanced Script

```bash
# Run with enhanced output and verification
node scripts/seed-projects.js
```

### Verifying Seeded Data

```bash
# Verify projects and related data
node scripts/verify-seeded-data.js

# Verify role permissions and rubrics
node scripts/verify-role-permissions-rubrics.js
```

### Removing Seeded Data

```bash
# Remove specific seeder data
npx sequelize-cli db:seed:undo --seed 005-role-permissions-and-rubrics.js
npx sequelize-cli db:seed:undo --seed 004-sample-projects.js

# Remove all seeder data
npm run db:seed:undo
```

## Features

### Idempotent Operations

The seeding scripts check for existing data before inserting new records:

```javascript
const existingProjects = await queryInterface.sequelize.query(
  "SELECT COUNT(*) as count FROM projects WHERE title LIKE 'Sample Project%'",
  { type: Sequelize.QueryTypes.SELECT, transaction }
);

if (existingProjects[0].count > 0) {
  console.log('Sample projects already exist, skipping seeding...');
  return;
}
```

### Transaction Safety

All operations are wrapped in database transactions:

```javascript
const transaction = await queryInterface.sequelize.transaction();
try {
  // Seeding operations
  await transaction.commit();
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

### Referential Integrity

Data is inserted in the correct order to maintain foreign key relationships:

1. Users (referenced by courses and projects)
2. Courses (referenced by projects)
3. Projects (references users and courses)

### Comprehensive Data

Each project includes:

- Complete metadata (title, description, instructions)
- Learning objectives and prerequisites
- File URLs (templates, datasets, resources)
- Grading configuration
- Collaboration settings
- Due dates and submission policies

## Database Schema Compatibility

The seeding scripts are designed to work with the actual database schema:

- Uses correct column names and data types
- Respects JSONB fields for arrays and objects
- Handles ENUM values properly
- Maintains foreign key constraints

## Troubleshooting

### Common Issues

1. **Database Connection Error**

   ```
   Solution: Ensure PostgreSQL is running and connection details are correct
   ```

2. **Foreign Key Constraint Violations**

   ```
   Solution: The scripts handle this automatically by creating dependencies first
   ```

3. **Duplicate Data**
   ```
   Solution: Scripts are idempotent - they check for existing data before inserting
   ```

### Verification Commands

Check if data was inserted correctly:

```sql
-- Count sample projects
SELECT COUNT(*) FROM projects WHERE title LIKE 'Sample Project%';

-- Check relationships
SELECT p.title, c.name as course_name, u.name as creator_name
FROM projects p
JOIN courses c ON p.course_id = c.id
JOIN users u ON p.created_by = u.id
WHERE p.title LIKE 'Sample Project%';
```

## Development Notes

### Adding New Sample Data

To add new sample projects:

1. Edit `src/seeders/004-sample-projects.js`
2. Add new project objects to the `projects` array
3. Ensure all required fields are included
4. Test with `node scripts/verify-seeded-data.js`

### Schema Changes

If the database schema changes:

1. Update the seeding script to match new column names/types
2. Update the verification script queries
3. Test thoroughly in development environment

## Integration with Development Workflow

The seeding scripts integrate with the standard development setup:

```bash
# Full development setup
npm run setup  # Includes db:create, db:migrate, and db:seed

# Reset database with fresh data
npm run db:reset  # Includes undo, migrate, and seed
```

This provides a consistent development environment with realistic test data for all team members.

## Comprehensive Data Seeding

### Additional Seeder: `006-comprehensive-sample-data.js`

**Purpose**: Populates all remaining tables with realistic sample data for complete system testing.

**What it creates**:

- **3 Submissions**: Student submissions for projects with realistic metadata
- **3 Grades**: Detailed grading with rubric scores and feedback
- **9 Announcements**: Course announcements with different types and priorities
- **54 Activities**: User activity tracking across different action types
- **15 Checkpoints**: Project milestones with goals and progress tracking
- **45 Checkpoint Goals**: Detailed goals for each checkpoint with completion criteria

**Usage**:

```bash
# Seed comprehensive data
npx sequelize-cli db:seed --seed 006-comprehensive-sample-data.js

# Verify comprehensive data
node scripts/verify-comprehensive-data.js

# Remove comprehensive data
npx sequelize-cli db:seed:undo --seed 006-comprehensive-sample-data.js
```

**Features**:

- ✅ Complete database population across all major tables
- ✅ Realistic relationships and foreign key constraints
- ✅ Proper enum value usage for all database enums
- ✅ JSON field handling for complex data structures
- ✅ Transaction-based with comprehensive error handling
- ✅ Idempotent operations (safe to run multiple times)

### Complete System Verification

The `scripts/verify-comprehensive-data.js` script provides detailed verification of all seeded data:

- **Table Counts**: Shows record counts for all major tables
- **User Distribution**: Users by role with permission counts
- **Project Distribution**: Projects by course
- **Submission & Grading Stats**: Average scores and completion rates
- **Activity Tracking**: Activity types and frequencies
- **Checkpoint Progress**: Goals and completion tracking
- **Role-Based Access**: Permission distribution across roles

This comprehensive seeding system ensures that the BITS DataScience Platform has realistic test data
for all features and functionality.
