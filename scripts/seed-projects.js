#!/usr/bin/env node

/**
 * Database Seeding Script for Projects and Related Data
 * 
 * This script:
 * 1. Seeds the database with sample projects and related data
 * 2. Verifies that the data was inserted successfully
 * 3. Provides detailed output about the seeding process
 * 4. Includes error handling and rollback mechanisms
 */

import { sequelize } from '../src/config/database.config.js';
import { 
  User, 
  Course, 
  Project, 
  ProjectTemplate, 
  Rubric 
} from '../src/models/associations.js';
import logger from '../src/config/logger.config.js';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkDatabaseConnection() {
  try {
    await sequelize.authenticate();
    colorLog('green', '✅ Database connection established successfully');
    return true;
  } catch (error) {
    colorLog('red', '❌ Unable to connect to the database:');
    console.error(error.message);
    return false;
  }
}

async function runSeeder() {
  try {
    colorLog('cyan', '🌱 Starting database seeding process...');
    
    // Run the seeder using sequelize-cli
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    const { stdout, stderr } = await execAsync('npx sequelize-cli db:seed --seed 004-sample-projects.js');
    
    if (stderr && !stderr.includes('Warning')) {
      throw new Error(`Seeder error: ${stderr}`);
    }
    
    colorLog('green', '✅ Seeding completed successfully');
    return true;
  } catch (error) {
    colorLog('red', '❌ Error running seeder:');
    console.error(error.message);
    return false;
  }
}

async function verifySeededData() {
  try {
    colorLog('cyan', '🔍 Verifying seeded data...');
    
    // Check users
    const userCount = await User.count({
      where: {
        email: {
          [sequelize.Sequelize.Op.like]: '%@bits.edu'
        }
      }
    });
    colorLog('blue', `📊 Sample users created: ${userCount}`);
    
    // Check courses
    const courseCount = await Course.count({
      where: {
        lms_course_id: {
          [sequelize.Sequelize.Op.like]: 'CS%_2024_FALL'
        }
      }
    });
    colorLog('blue', `📚 Sample courses created: ${courseCount}`);
    
    // Check projects
    const projectCount = await Project.count({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      }
    });
    colorLog('blue', `🚀 Sample projects created: ${projectCount}`);
    
    // Get detailed project information
    const projects = await Project.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      },
      include: [
        {
          model: Course,
          as: 'course',
          attributes: ['name', 'code']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['name', 'email']
        }
      ],
      attributes: ['id', 'title', 'status', 'difficulty_level', 'type', 'total_points', 'project_code']
    });
    
    colorLog('magenta', '\n📋 Project Details:');
    console.log('=' .repeat(100));
    console.log('| Project Code | Title                                    | Course    | Status    | Difficulty | Type       | Points |');
    console.log('=' .repeat(100));
    
    projects.forEach(project => {
      const title = project.title.length > 40 ? project.title.substring(0, 37) + '...' : project.title;
      const courseCode = project.course ? project.course.code : 'N/A';
      console.log(
        `| ${project.project_code.padEnd(12)} | ${title.padEnd(40)} | ${courseCode.padEnd(9)} | ${project.status.padEnd(9)} | ${project.difficulty_level.padEnd(10)} | ${project.type.padEnd(10)} | ${project.total_points.toString().padEnd(6)} |`
      );
    });
    console.log('=' .repeat(100));
    
    // Check foreign key relationships
    colorLog('cyan', '\n🔗 Verifying relationships...');
    
    const projectsWithCourses = await Project.count({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      },
      include: [{
        model: Course,
        as: 'course',
        required: true
      }]
    });
    
    const projectsWithCreators = await Project.count({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      },
      include: [{
        model: User,
        as: 'creator',
        required: true
      }]
    });
    
    colorLog('green', `✅ Projects with valid course relationships: ${projectsWithCourses}/${projectCount}`);
    colorLog('green', `✅ Projects with valid creator relationships: ${projectsWithCreators}/${projectCount}`);
    
    // Verify data integrity
    if (projectsWithCourses === projectCount && projectsWithCreators === projectCount) {
      colorLog('green', '✅ All foreign key constraints are satisfied');
      return true;
    } else {
      colorLog('red', '❌ Some foreign key relationships are missing');
      return false;
    }
    
  } catch (error) {
    colorLog('red', '❌ Error verifying seeded data:');
    console.error(error.message);
    return false;
  }
}

async function showSampleQueries() {
  try {
    colorLog('cyan', '\n📊 Sample Queries and Results:');
    
    // Query 1: Projects by difficulty level
    colorLog('yellow', '\n1. Projects grouped by difficulty level:');
    const projectsByDifficulty = await Project.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      },
      attributes: [
        'difficulty_level',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('AVG', sequelize.col('total_points')), 'avg_points']
      ],
      group: ['difficulty_level'],
      raw: true
    });
    
    console.table(projectsByDifficulty);
    
    // Query 2: Projects by course
    colorLog('yellow', '\n2. Projects by course:');
    const projectsByCourse = await Project.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      },
      include: [{
        model: Course,
        as: 'course',
        attributes: ['name', 'code']
      }],
      attributes: ['title', 'status', 'total_points'],
      order: [['course', 'code']]
    });
    
    const courseData = projectsByCourse.map(p => ({
      course: p.course ? `${p.course.code} - ${p.course.name}` : 'No Course',
      project: p.title,
      status: p.status,
      points: p.total_points
    }));
    
    console.table(courseData);
    
    // Query 3: Instructor workload
    colorLog('yellow', '\n3. Instructor workload (projects created):');
    const instructorWorkload = await Project.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.like]: 'Sample Project%'
        }
      },
      include: [{
        model: User,
        as: 'creator',
        attributes: ['name', 'email']
      }],
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('Project.id')), 'project_count'],
        [sequelize.fn('SUM', sequelize.col('total_points')), 'total_points']
      ],
      group: ['creator.id', 'creator.name', 'creator.email'],
      raw: true
    });
    
    console.table(instructorWorkload);
    
    return true;
  } catch (error) {
    colorLog('red', '❌ Error running sample queries:');
    console.error(error.message);
    return false;
  }
}

async function main() {
  try {
    colorLog('bright', '🚀 BITS DataScience Platform - Project Seeding Script');
    colorLog('bright', '=' .repeat(60));
    
    // Check database connection
    const connected = await checkDatabaseConnection();
    if (!connected) {
      process.exit(1);
    }
    
    // Run seeder
    const seeded = await runSeeder();
    if (!seeded) {
      process.exit(1);
    }
    
    // Verify data
    const verified = await verifySeededData();
    if (!verified) {
      colorLog('yellow', '⚠️  Data verification failed, but seeding may have been successful');
    }
    
    // Show sample queries
    await showSampleQueries();
    
    colorLog('green', '\n🎉 Database seeding completed successfully!');
    colorLog('cyan', '\nNext steps:');
    colorLog('cyan', '1. Start your application: npm run dev');
    colorLog('cyan', '2. Access the API to view projects');
    colorLog('cyan', '3. Check the database directly if needed');
    
  } catch (error) {
    colorLog('red', '❌ Fatal error in seeding script:');
    console.error(error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
